// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formId} = require('../../../common/appConstants');
const { ehrTables } = require('../../../common/tablealias');

module.exports.deleteTalentPoolDetails = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside deleteTalentPoolDetails function.");
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formId.talentPoolCandidate);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
        
             // Check if there are candidates associated with the Talent_Pool_Id
            const candidate = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
            .select('CRI.Archived', 'CPI.Candidate_Id')
            .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
            .where('CPI.Talent_Pool_Id', args.talentPoolId)
            .orderByRaw(`CASE WHEN CRI.Archived = 'No' THEN 1 WHEN CRI.Archived = 'Yes' THEN 2 ELSE 3 END`).first();

            
            // If there are associated candidates, prevent deletion
            if (candidate) {
                throw candidate?.Archived.toLowerCase() === 'yes' ? 'TAP0116' //Unable to delete the talent pool as it contains candidates who are currently archived.
                :'TAP0102'; //Sorry, This talent pool has associated candidates. could not delete it.
            }

            const currentRecord = await getCurrentRecord(organizationDbConnection, args.talentPoolId);
            
            let result = await organizationDbConnection(ehrTables.talentPool)
            .del().where('Talent_Pool_Id', args.talentPoolId);
           
            if(!result){
                throw 'TAP0101'; //Something went wrong while deleting talent pool details. Please try after some time.
            }

            let systemLogParam = {
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                changedData: JSON.stringify(currentRecord[0]),
                organizationDbConnection: organizationDbConnection,
                message: `Talent pool details deleted for talent pool id: ${args.talentPoolId}`
            };
            // Call the function to add the system log
            await commonLib.func.createSystemLogActivities(systemLogParam);
            return { errorCode: '', message: 'Talent pool details deleted successfully.'}
        }else{
            console.log('No rights to delete the talent pool details');
            throw '_DB0103';
        }
    }catch (e) {
        console.log('Error while deleteTalentPoolDetails main catch block.', e);
        let errResult = commonLib.func.getError(e, 'TAP0003'); //An error occurred while attempting to delete the talent pool details. Please contact the platform administrator for assistance.
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}

async function getCurrentRecord(organizationDbConnection, talentPoolId) {
    try {
        let result = await organizationDbConnection(ehrTables.talentPool)
                .select('*')
                .where('Talent_Pool_Id', talentPoolId)

    return result;
    } catch (error) {
        console.log('Error while getCurrentRecord catch block', error);  
        throw error;
    }
}