//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require table names
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const {ApolloError } = require('apollo-server-lambda');
//Require constants
const { syntrumValues } = require('../../../common/appConstants');
//Require common functions
const { handleErrorLog,extractResponse,replaceNullValues,addExternalIntegrationFailureRecords } = require('../../common/syntrumCommonFunctions');
const { getSyntrumAuthToken } = require('./getSyntrumAuthToken');
const { getEmployeeLeaveDetails,handleFinalSettlementDataRequest } = require ('./fetchSyntrumPayrollDetails');
//Require knex to make DB connection
const knex = require('knex');
//Require moment
const moment = require('moment');

let organizationDbConnection;
let errorCodesListToHandle = ['_EC0007','_EC0001','SYN0104','SYN0103','SYN0107'];

//Function to trigger the syntrum integration API
module.exports.executeSyntrumIntegrationRequest = async (parent, args, context, info) => {
    console.log('Inside executeSyntrumIntegrationRequest function',args);

    let inputDetailsToLog;
    let uniqueIds;
    let elementHeader;
    let action;
    let employeePayrollKey = syntrumValues.employeePayrollKey;
    try{
        await validateInputs(args);
        organizationDbConnection = knex(context.connection.OrganizationDb);

        uniqueIds = args.uniqueIds;
        elementHeader = args.elementHeader;
        let baseElementHeader = args.baseElementHeader;

        action = await getActionNames(baseElementHeader,args.action);

        let authAndUrlDetails = await getSyntrumAuthToken(organizationDbConnection);
        authAndUrlDetails.integrationRequestType = args.integrationRequestType;

        let syntrumInputs = { args,employeePayrollKey,action };
        let inputDetails  = await getSyntrumInputDetails(organizationDbConnection,context,syntrumInputs);
        inputDetailsToLog = inputDetails[employeePayrollKey];

        let apiResponse = await handleIntegrationRequest(inputDetails,authAndUrlDetails);
        let logDetails ={ action, entityType:elementHeader,  uniqueId: uniqueIds, formData: JSON.stringify(inputDetailsToLog) };
        await handleExternalApiLog(apiResponse,logDetails,baseElementHeader);

        return { errorCode: '',message:'External integration request completed successfully.'};
    }catch(mainCatchError){
        console.log('Error in the executeSyntrumIntegrationRequest main catch block.',args,context.Org_Code,mainCatchError);
        //Log the errors
        let errorInputsToLog = (inputDetailsToLog ? JSON.stringify(inputDetailsToLog) : JSON.stringify(args));

        let errorInputs = { inputDetails:  errorInputsToLog, uniqueIds:uniqueIds, entityType:elementHeader, action, errorCodesListToHandle, apiError: mainCatchError };
        await handleErrorLog(organizationDbConnection,errorInputs);

        let errorResult = commonLib.func.getError('SYN0011', '');
        throw new ApolloError(errorResult.message,errorResult.code);
    }
    finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}

async function validateInputs(args){
    try {
        if(!args.integrationRequestType || !args.baseElementHeader ||
            ((Array.isArray(args.uniqueIds) && args.uniqueIds.length === 0) && 
            (!args.uniqueIdDetails))){
            console.log('Invalid inputs');
            throw '_EC0007';
        }else if(args.baseElementHeader == 'finalSettlement' && !args.payrollDetails){
            console.log('Payroll details is empty');
            throw '_EC0007';
        }
        return 'success';
    } catch (error) {
        console.log('Error in the validateInputs main catch block.');
        throw error;
    }
}

/**
 * Retrieves the input parameters required for the integration.
 * 
 * @param {string} orgCode - organization code for which data need to process.
 * @param {string} elementHeader - The header information for the request.
 * @param {string} integrationRequestType - The integrationRequestType to call the respective function to fetch the details.
 * @param {Array<Int>} uniqueIds - A list of unique ids for processing.
 * @returns {Object} - The integration input parameters.
*/
async function getSyntrumInputDetails(organizationDbConnection,context,syntrumInputs){
    
    let { args,employeePayrollKey } = syntrumInputs;
    let { baseElementHeader } = args;
    let response = '',
    errorCode = '',
    inputRequestDetails = [];
    try {
        switch(baseElementHeader){
            case 'leaves':
                inputRequestDetails = await getEmployeeLeaveDetails(organizationDbConnection,context.Org_Code,syntrumInputs);
                response = await formInputDetails(inputRequestDetails,employeePayrollKey);
                break;
            default: 
                console.log('Invalid baseElementHeader',baseElementHeader);
                errorCode = 'SYN0104';
                break;
        }
        if(errorCode){
            throw errorCode;
        }
        
        return response;
    } catch (error) {
        console.log('Error in getSyntrumInputDetails function main catch block.',error);
        throw error;
    }
}

//Function to form the inputs required by the external integration
async function formInputDetails(response,key){
    try{
        if(response && response.length){
            let formInputs = replaceNullValues(response);
            return{
                [key]:formInputs
            }
        }else{
            console.log('Empty response in the formInputDetails function else block',response);
            throw '_EC0001';
        }
    } catch (error) {
        console.log('Error in the formInputDetails function main catch block');
        throw error;
    }
}

//Function to get action names to save in the error log table
async function getActionNames(baseElementHeader,action){
    console.log('Inside getActionNames function',baseElementHeader,action);
    if(baseElementHeader == 'leaves'){
        return action;//Approve or Cancel
    }else if(baseElementHeader == 'finalSettlement'){
        return 'Add';
    }else{
        console.log('Invalid baseElementHeader',baseElementHeader);
        throw 'SYN0104';
    }
}

//Function to call the integration endpoint
async function handleIntegrationRequest(inputDetails,authAndUrlDetails) { 
    try {
        const axios = require('axios');

        let {apiUrl,authToken,integrationRequestType} =authAndUrlDetails;
        const cookies = `refreshToken=${authToken}`;

        let url = apiUrl+syntrumValues[integrationRequestType];
        console.log("apiUrl,authToken",apiUrl,authToken,url);

        //call API to update the integration details
        const config = {
            method: 'post', 
            url: url,
            maxBodyLength: Infinity,
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer '+ authToken,
                "Cookie": cookies
            },
            data: JSON.stringify({
                'json': inputDetails
            }),
            timeout: 60000 //1minute
        };

        const { data: response } = await axios.request(config);
        console.log('Integration response ', JSON.stringify(response),config);

        // Return success status with the response data
        return { status: true, message: 'Success Response', response: response };
    } catch (error) {
        console.error('Error in handleIntegrationRequest function main catch block',error,inputDetails,authAndUrlDetails);

        // Destructure error response details, if available
        const { status, statusText, data } = error.response || error || {};
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
            return { status: false, message: `408 - Request Timeout - The server took longer than 1 minute to respond.`, response: null };
        }
        // Return failure status with error details
        return { status: false, message: `${status || ' Unknown Status Code '} - ${statusText || 'Unknown Status Text'}`, response: data };
    }
}

async function handleExternalApiLog(apiResponse,logDetails,baseElementHeader){
    try {
        console.log("Inside handleExternalApiLog function, success",apiResponse,logDetails,baseElementHeader);

        let logDetailsArray = [];
        let {status,response} =apiResponse;
        const getCreatedResponse = extractResponse(response, 'json');
        let success = status && getCreatedResponse?.success;
        const remarksDetails = extractResponse(getCreatedResponse, 'remarks');
        let allResponseDetails = remarksDetails?.data || [];
        if(Array.isArray(allResponseDetails) && allResponseDetails.length === 0){
            console.log('Empty data in the remarks json');
            allResponseDetails = remarksDetails?.errors || [];
        }
        console.log("Extract Response, success",success,'getCreatedResponse',getCreatedResponse,remarksDetails,allResponseDetails);
        if(Array.isArray(allResponseDetails) && allResponseDetails && allResponseDetails.length){
            for(let eachJsonResponse of allResponseDetails){
                let eachInputResponse = eachJsonResponse?.status?.toString()?.toLowerCase() === 'success' ? 'Success' : 'Failed';
                /**
                 * Entity Id will be leave id if the base element is leaves
                 * Entity Id will be user defined employee id for the full and final settlement - leave encashment and leave encashment
                */
                let logDetailsJson = { 
                    Status: eachInputResponse, 
                    Action: logDetails.action,
                    Entity_Type: logDetails.entityType,
                    Integration_Type: syntrumValues.integrationType,
                    Entity_Id: (baseElementHeader == 'leaves') ? logDetails.uniqueId[0] : eachJsonResponse.empcode, 
                    Form_Data: logDetails.formData, 
                    Failure_Reason:  JSON.stringify(allResponseDetails),
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1
                };
                logDetailsArray.push(logDetailsJson);
            }

            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(logDetailsArray);
        }else{
            console.log("Empty response data array");
            if(getCreatedResponse.status && getCreatedResponse.status.toLowerCase() == 'success'){
                success = 'Success';
            }else{
                success = 'Failed';
            }
            logDetails.status = success;

            for(let entityId of logDetails.uniqueId){
                let logDetailsJson = { 
                    Status: success, 
                    Action: logDetails.action,
                    Entity_Type: logDetails.entityType,
                    Integration_Type: syntrumValues.integrationType,
                    Entity_Id: entityId, 
                    Form_Data: logDetails.formData, 
                    Failure_Reason:  JSON.stringify(getCreatedResponse),
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1
                };
                logDetailsArray.push(logDetailsJson);
            }
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(logDetailsArray);
        }
        return true;
    } catch (error) {
        console.error('Error in handleExternalApiLog function main catch block',error);
        throw error;
    }
}