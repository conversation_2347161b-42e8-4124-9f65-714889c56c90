'use strict';
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require common function
const commonFunction = require('./common/initiateStepFunction');

// Function to initiate initiateCamuResignation step function
module.exports.initiateCamuResignation  = async(event, context) =>{
    try{
        console.log('Inside initiateCamuResignation function',event);
        // based on event define the status
        // We will be triggering the step function in 2 different cases.
        let status='';
        if(event.status==='Open')
        {
            status='Open';
        }
        if(event.status==='Failed')
        {
            status='Failed';
        }

        let triggerCamuResignation= await commonFunction.triggerStepFunction(process.env.stateMachineArn,'camuResignation',status);
        
        return {errorCode:'',message: 'initiateCamuResignation initiated successfully.'};
    }
    catch(mainCatchError){
        console.log('Error in initiateCamuResignation function main catch block.', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'TLC0141');
        return {errorCode:errResult.code,message: errResult.message};
    }
};