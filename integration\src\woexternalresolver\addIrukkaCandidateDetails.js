//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const {UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const {formName} = require('../../common/appConstants');
const { CostExplorer } = require('aws-sdk');
const { validateIrukkaCandidateDetails} = require('../../src/common/commonFunction')

//function to add IrukkaCandidateDetails
module.exports.addIrukkaCandidateDetails = async (parent, args, context, info) => {
    console.log('Inside addIrukkaCandidateDetails function');
    let validationError = {};
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const fieldValidations = {
            email: "IVE0052",
            city: "IVE0340",
            state: "IVE0341",
            country: "IVE0342",
            pinCode: "IVE0343",
        };
        validationError = validateIrukkaCandidateDetails(args, fieldValidations);
        if (Object.keys(validationError).length == 0) {
            return (
                    organizationDbConnection
                    .transaction(async function(trx){
                        const nameParts = splitFullName(args.name);
                        return(
                        await organizationDbConnection(ehrTables.candidatePersonalInfo)
                        .insert({
                            Emp_First_Name:  nameParts.firstName,
                            Emp_Middle_Name:  nameParts.middleName,
                            Emp_Last_Name:  nameParts.lastName,
                            Gender_Id: args.genderId,
                            Gender: args.gender,
                            Work_Email: args.email,
                            Irukka_Candidate_Id : args.userId
                        })
                        .transacting(trx)
                        .then(async(candidateResult) => {
                                    return (
                                        await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
                                        .insert({
                                            Candidate_Id:  candidateResult[0],
                                            Job_Post_Id: args.partnerJobReferenceId
                                        })
                                        .transacting(trx)
                                        .then(async (data) => {
                                        return (
                                            await organizationDbConnection(ehrTables.candidateContactDetails)
                                            .insert({
                                                Candidate_Id: candidateResult[0],
                                                Mobile_No_Country_Code: args.countryCode ,
                                                Mobile_No: args.phoneNumber,
                                                cCity: args.city,
                                                cState: args.state,
                                                cCountry: args.country,
                                                cPincode: args.pinCode,
                                                pCity: args.city,
                                                pState: args.state,
                                                pCountry: args.country,
                                                pPincode: args.pinCode
                                            })
                                            .transacting(trx)
                                            .then(async (data) => {
                                                if (data) {
                                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                    return { errorCode: "", message: "Irukka candidate details has been added successfully." };
                                                } else {
                                                    throw 'SET0114'
                                                }
                                            })
                                        )
                                })
                            )
                        })
                        .catch((catchError) => {
                            console.log('Error in addIrukkaCandidateDetails .catch() block', catchError);
                            let errResult = commonLib.func.getError(catchError, 'SET0115');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                        )
                    })
            )
                        
        } else {
            throw 'IVE0000';
        }
    } catch (mainCatchError) {
        console.log('Error in addIrukkaCandidateDetails function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addIrukkaCandidateDetails function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
       else{
        let errResult = commonLib.func.getError(mainCatchError, 'SET0010');
        throw new Error(JSON.stringify({ errorCode: errResult.code , message: errResult.message, validationError: null, data: null }));
       }
    }
}

function splitFullName(fullName) {
    // Split the full name into an array of words using space as the delimiter
    const nameParts = fullName.split(' ');
    // Initialize variables for first name, middle name, and last name
    let firstName = '';
    let middleName = '';
    let lastName = '';
    // Determine the first name and last name
    if (nameParts.length > 0) {
      firstName = nameParts[0];
      // Check if there are more than one part in the name
      if (nameParts.length > 1) {
        lastName = nameParts[nameParts.length - 1];
      }
      // Middle name is everything in between the first and last names
      if (nameParts.length > 2) {
        middleName = nameParts.slice(1, nameParts.length - 1).join(' ');
      }
    }
    // Return an object with the extracted names
    return {
      firstName,
      middleName,
      lastName,
    };
  }

