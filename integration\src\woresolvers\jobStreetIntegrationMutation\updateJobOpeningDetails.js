//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require knex to make DB connection
const knex = require('knex')
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda')
//Require table alias
const { ehrTables } = require('../../../common/tablealias')
const { formId } = require('../../../common/appConstants')
const {
  validateJobStreetCommonRuleInput,
  mapErrorToCustomCodeSeek,
  checkJobOpeningRecordExist
} = require('../../common/commonFunction')
const moment = require('moment')
const axios = require('axios')
const {
  updatePositionOpeningPerson
} = require('../../queries/jobStreetQueries')

module.exports.updateJobOpeningDetails = async (
  parent,
  args,
  context,
  info
) => {
  let organizationDbConnection
  let errResult
  let validationError = {}
  const loginEmployee_Id = context.Employee_Id
  const formIdValue = formId.jobpost
  organizationDbConnection = knex(context.connection.OrganizationDb)
  let checkRights = await commonLib.func.checkEmployeeAccessRights(
    organizationDbConnection,
    loginEmployee_Id,
    '',
    '',
    'UI',
    false,
    formIdValue
  )
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb)

    if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1 && checkRights.Is_Recruiter.toLowerCase() === 'yes') {
      const ACCESS_TOKEN = context.jobstreet_access_token
      const { jobStreetId, roleCode, email, recruiterName, phoneNo,recruiterNoCountryCode } = args
      let existingRecord = await checkJobOpeningRecordExist(
        organizationDbConnection,
        jobStreetId,
        ['Applied']
      )
      if (!existingRecord || !existingRecord.length) {
        console.log('existing job street opening record not found')
        throw 'EI00155'
      }
      const fieldValidations = {
        email: 'IVE0052',
        recruiterName: 'IVE0445'
      }
      validationError = await validateJobStreetCommonRuleInput(
        args,
        fieldValidations,
        1
      )
      if (Object.keys(validationError).length > 0) {
        throw 'IVE0000'
      }
      let jobOpeningData = {
        Role_Code: roleCode,
        Recruiter_Email_Id: email,
        Recruiter_Name: recruiterName,
        Applied_Status: 'Update Progress'
      }
        jobOpeningData.Recruiter_Phone_No = phoneNo
        jobOpeningData.Recruiter_No_Country_Code=recruiterNoCountryCode
      args.documentId = existingRecord[0].Document_Id
      const variables = getJobstreetData(args)
      const GRAPHQL_ENDPOINT = process.env.jobStreetTokenAPI
      let query = updatePositionOpeningPerson
      await axios
        .post(
          GRAPHQL_ENDPOINT,
          {
            query,
            variables
          },
          {
            headers: {
              Authorization: `Bearer ${ACCESS_TOKEN}`,
              'Content-Type': 'application/json'
            }
          }
        )
        .then(async (response) => {
          if (
            response.data &&
            response.data.errors &&
            response.data.errors.length
          ) {
            console.log(
              'Error in updateJobOpeningDetails function catch block.',
              response.data.errors
            )
            let errorCode = await mapErrorToCustomCodeSeek(response.data)
            if (errorCode === 'CH0001') {
              console.log('unathentcated request check the tokens')
            } else if (errorCode === 'CH0003') {
              console.log('check the validations bad_input error')
            }

            throw 'EI00156'
          }
          response.data.data.updatePostedPositionProfile
          if (
            response &&response.data &&
            response.data.data.updatePositionOpeningPersonContacts &&
            response.data.data.updatePositionOpeningPersonContacts.positionOpening
              .documentId &&
              response.data.data.updatePositionOpeningPersonContacts.positionOpening
              .documentId.value
          ) {
            jobOpeningData.Document_Id =
            response.data.data.updatePositionOpeningPersonContacts.positionOpening.documentId.value
            jobOpeningData.Applied_Status = 'Applied'
          } else {
            throw 'EI00154'
          }
        })
        .catch(async (error) => {
          if (error && error.length && error.response.data) {
            let errorCode = await mapErrorToCustomCodeSeek(error.response.data)
            if (errorCode === 'CH0001') {
              console.log(
                'unathentcated request check the tokens in catch block'
              )
            } else if (errorCode === 'CH0003') {
              console.log(
                'check the validations bad_input error in catch block'
              )
            }

            throw 'EI00152'
          } else {
            throw error
          }
        })
      return await organizationDbConnection.transaction(async (trx) => {
        jobOpeningData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
        jobOpeningData.Updated_By = loginEmployee_Id

        await organizationDbConnection(ehrTables.jobStreetJobOpenings)
          .transacting(trx)
          .where('Job_Street_Id', jobStreetId)
          .update(jobOpeningData)
          .catch((error) => {
            console.log(
              'Error in updateJobOpeningDetails .catch() block',
              error
            )
            throw error
          })
        let systemLogParam = {
          userIp: context.User_Ip,
          employeeId: loginEmployee_Id,
          organizationDbConnection: organizationDbConnection,
          message: 'Job post contact details updated sucessfuly'
        }
        await commonLib.func.createSystemLogActivities(systemLogParam)
        organizationDbConnection ? organizationDbConnection.destroy() : null
        return {
          errorCode: '',
          message: `Job opening details updated sucessfuly`
        }
      })
    } else {
      if(Object.keys(checkRights).length <0 || checkRights.Role_Update !== 1 ){
       
            console.log("The employee does not have edit access.");
            throw '_DB0102';
      }
      else{
        throw '_DB0115'
    }
}

     
  } catch (e) {
    console.log(
      'Error in updateJobOpeningDetails  function main catch block.',
      e
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    if (e === 'IVE0000') {
      console.log(
        'Validation error in the updateJobOpeningDetails  function',
        validationError
      )
      errResult = commonLib.func.getError('', 'IVE0000')
      throw new UserInputError(errResult.message, {
        validationError: validationError
      })
    } else {
      errResult = commonLib.func.getError(e, 'EI00139')
      throw new ApolloError(errResult.message, errResult.code)
    }
  }
}

function getJobstreetData(args) {
  const { roleCode, email, recruiterName, documentId, phoneNo } = args
  let phoneNumberArray = []
  if (phoneNo && phoneNo.length) {
    phoneNumberArray.push({ formattedNumber: phoneNo })
  }
  let data = {
    input: {
      positionOpening: {
        documentId: documentId,
        personContacts: [
          {
            name: { formattedName: recruiterName },
            roleCode: roleCode,
            communication: {
              email: [{ address: email }],
              phone: phoneNumberArray
            }
          }
        ]
      }
    }
  }
  return data
}
