// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
const { formId } = require('../../../common/appConstants');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');


module.exports.retrieveMicrosoftIntegration = async (parent, args, context, info) => {
    // Log the entry into the function
    console.log('Inside retrieveMicrosoftIntegration function');
    
    // Declare a variable for the database connection
    let organizationDbConnection;

    try {
        // Initialize the database connection using the organization database configuration from the context
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        // Extract the employee ID of the currently logged-in user from the context
        const loginEmployeeId = context.Employee_Id;

        // Check if the employee has view access rights for the 'myIntegrations' form
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId
        );

        // If the employee does not have view access rights, log a message and throw an error
        if (!checkRights || !checkRights.Role_View) {
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }

        // Query the database to retrieve Microsoft integration details for the logged-in employee
        const microsoftIntegration = await organizationDbConnection(ehrTables.employeeLevelMicrosoftIntegration)
            .select('Microsoft_Email', 'Calendar_Status', 'Teams_Status')
            .where('Employee_Id', loginEmployeeId)
            .first();

        // Return the retrieved Microsoft integration details with a success message
        return {
            errorCode: '',
            message: 'Microsoft integration has been retrieved successfully.',
            microsoftEmail: microsoftIntegration?.Microsoft_Email || null,
            calendarStatus: microsoftIntegration?.Calendar_Status || null,
            teamsStatus: microsoftIntegration?.Teams_Status || null,
        };

    } catch (error) {
        // Log any errors that occur in the try block
        console.log('Error in retrieveMicrosoftIntegration function main catch block.', error);
        
        // Convert the caught error into a structured error message and code
        const errResult = commonLib.func.getError(error, 'EI00219'); // EI00219: An error occurred while retrieving the microsoft integration. Please try again later.
        
        // Throw an ApolloError with the message and code from the error result
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Ensure the database connection is destroyed if it was established
        if (organizationDbConnection) organizationDbConnection.destroy();
    }
};

