# defining custom data type
scalar Date
type Query {
  listWorkScheduleDetails: listWorkScheduleDetailsResponse!
}
type Mutation {
  addUpdateRecruitmentStatus(
    Integration_Id: Int!
    Integration_Type: String!
    Integration_Status: String!
    Indeed_Client_Id: String
    Indeed_Secret_Key: String
    Indeed_Source_Name: String
    Company_Id: String
    Hirer_ID: String
  ): addUpdateRecruitmentStatusResponse
  closeJobPost(Job_Post_Id: Int!, Status: Int!): closeJobPostResponse

  publishJobPostToIndeed(
    jobPostId: Int
    integrationId: Int
    dynamicFormId: Int
    status: String
    cityId: Int
    cityName: String
    stateId: Int
    stateName: String
    countryCode: String
    workplaceType: String
    updateLocation: String
    input: CreateSourcedJobPostingsInput
  ): publishJobPostToIndeedResponse

  sfaIntegrationEmployeeDetails(candidateId: Int!, integrationType:String!): closeJobPostResponse

  updateJobOpeningDetails(
    jobStreetId: Int!
    roleCode: String!
    email: String!
    recruiterName: String!
    recruiterNoCountryCode: String
    phoneNo: String
  ): addUpdateJobPostJobStreetDetails

  addUpdateJobPostJobStreet(
    jobStreetId: Int!
    jobId: Int!
    email: String
    roleCode: String
    recruiterName: String
    documentId: String
    videoPositionCode: String
    videoUrl: String
    payDescription: String
    positionLocationId: String!
    phoneNo: String
    recruiterNoCountryCode: String
    seekAdvertisementProductId: String!
    seekWorkArrangementCodes: [String]
    subCategoryId: String
    categoryId: String!
    seekWorkTypeCode: String
    advertisementBranding: String
    seekBillingReference: String
    profileId: String
    searchBulletPointsArray: [String]
    appliedStatus: String!
    searchSummaryDescription: String!
    jobTitle: String!
    jobSummary: String!
    hirerId: String!
    minimumAmount: String
    maximumAmount: String
    currency: String
  ): addUpdateJobPostJobStreetDetails

  closeJobStreetJob(jobStreetId: Int!): addUpdateJobPostJobStreetDetails

  closeJobpostToIndeed(
    sourcedPostingId: String!
    integrationId: Int
  ): closeJobpostToIndeedResponse

  addUpdateHiringForecast(
   originalPositionId: String
    action: String!
    divisionId: String
    groupId: String
    departmentId: String
    sectionId: String
    positionTitle: String!
    organizationStructureId:Int
    foreCastList: [foreCasteInput!]!
  ): commonResponse

   updateWorkflowApprovalSetting(
    enableWorkflowApproval: String!
    formId: Int!
  ): commonResponse

  addUpdateReqruitmentPosition(
    recruitmentId: Int!
    originalPositionId: String!
    groupCode: String
    divisionCode: String
    departmentCode: String
    sectionCode: String
    costCenter: String
    employeeType: String
    noOfPosition: Int!
    workflowId: Int
    status: String
    positionCode: String
    positionTitle: String!
    groupName: String
    divisionName: String
    departmentName: String
    sectionName: String
    employeeTypeName: String
    eventId: String
    reasonForRequest:String
    reasonForReplacement:String
    customGroupId:Int
    PositionLevel:Int
  ): commonResponse
  addUpdateNewPosition(
    positionRequestId: Int
    organizationStructureId:Int
    originalPositionId: String
    positionCode: String
    positionTitle: String!
    groupName: String
    groupCode: String
    divisionCode: String
    divisionName: String
    departmentCode: String
    departmentName: String
    sectionCode: String
    sectionName: String
    costCenter: String
    employeeType: String
    employeeTypeName: String
    noOfPosition: Int!
    PositionLevel:Int
    reasonForRequest:String
    reasonForReplacement:String
    licenseCertificate: String
    workflowId: Int
    status: String!
    eventId: String
    groupId: String
    divisionId: String
    customGroupId:Int
    departmentId: String
    sectionId: String
  ): newPositionResponse
  addUpdateForeCastSettings(
    forecastSettingsId: Int!
    releaseDate: Date!
    endMonth: Int!
    roleIds: [Int!]
  ): commonResponse

  updatePositionJobSummary(
    formId:Int!
    jobDescription: String!
    approvedPosition: Int!
    warmBodies: Int!
    originalPositionId: String!
    positionCode: String!
  ):commonResponse
    addUpdateEducationRequirementsDescriptions(
    status: String!
    eventId: String
    action: String!
    positionRequestId: Int!
    input: [educationDescriptionsIndividual!]!
  ): commonResponse
  addUpdateWorkingConditions(
    workingConditionId: Int
    workingArea: String!
    timeSpent: Int
    positionRequestId: Int!
    status:String!
    eventId: String
  ): commonResponse
  deleteHiringForeCast(
  originalPositionId:String!
  forecastingYear:String!
  ):commonResponse
  addUpdateDutiesResponsibilities(
  dutiesResponsibilityId: Int
  positionRequestId: Int!
  regularDuties: String!
  noOfHoursPeriod: Float!
  period: String!
  competenciesRequired: String!
  competency: String
  ratingOfCompetency: Float
  status:String!
   eventId: String
    ):commonResponse
  deleteOpenPositionSubTable(
  deleteId:Int!
  tableKeyword:String!
  positionRequestId:Int
  status:String!
  eventId: String
):commonResponse 
  updateExtIntNewPosition(
    positionRequestId: Int!
    internalOperatingNetwork: [String]
    externalOperatingNetwork: [String]
    licenseCertificate: String
    licenseCertificateDetails:String
    comments: String
    status:String!
    eventId: String
  ): commonResponse
  addUpdateExperience(
    experienceId: Int
    typeOfJobs: String!
    months: Int
    years: Int
    positionRequestId: Int!
    status:String!
     eventId: String
  ): commonResponse
  jobStreetCompanyIdStatusUpdate(
    formId: Int!, 
    status: String!, 
    seekHirerId: Int!
  ):commonResponse

  deleteJobStreetAccount(
    formId: Int!, 
    seekHirerId: Int!
  ):commonResponse

  updateNewPositionChangeStatus(
    status: String!
    comments: String
    positionRequestId: Int!
  ):commonResponse

  addUpdateTalentPool(
      talentPoolId: Int! 
      talentPool: String!
    ): commonResponse

  deleteTalentPoolDetails(talentPoolId: Int!): commonResponse

  addCandidateToTalentPool(
      candidateId: Int!
      talentPoolId: Int!
      archiveStatusId: Int!
      archiveReasonId: Int!
      notificationTime: String
      archiveComment: String
      mailContent: String
    ): commonResponse
    
  archiveCandidateDetails(
      candidateId: Int!
      archiveReasonId: Int!
      notificationTime: String
      archiveComment: String
      archiveStatusId: Int
      mailContent: String
      action: String!
      talentPoolId: Int
      archiveStatus: String
    ): commonResponse

  moveToJobPost(
      candidateId: Int!
      jobPostId: Int!
      status: Int!
      formId: Int!
      talentPoolId: Int
  ): commonResponse
  
  uploadBulkCandidateDetails(
    candidateList : [candidateData!]!
  ): commonResponse

  moveToAnotherTalentPool(
    candidateId: Int!
    formId: Int!
    talentPoolId: Int!
  ): commonResponse

  moveArchiveToCandidate(
    candidateId: Int!
    formId: Int!
  ): commonResponse

  addUpdateMicrosoftIntegration(
    action: String!
    microsoftEmail: String
    calendarStatus: String
    teamsStatus: String
  ): commonResponse

  moveCandidateToBlackedList(
    candidateId: Int!
    blackedReasonId: Int!
    blackedComment: String
    blackedAttachment: [BlackedListAttachmentInput!]
  ): commonResponse

  moveBlackedListToCandidate(
     candidateId: Int!
  ):commonResponse
   generateSalaryInformation(formId: Int!):commonResponse
   generateSyntrumPayslipDetails(formId: Int employeeId: Int, month: Int!, year: Int!):commonResponse
}

input BlackedListAttachmentInput {
  filePath: String!
}

input CreateSourcedJobPostingsInput {
  jobPostings: [CreateSourcedJobPostingInput!]!
}
input educationDescriptionsIndividual {
  educationRequirementId: Int!
  positionRequestId: Int!
  educationDescription: String!
  mppEducationRequirementsDescriptionsId: Int!
}

input CreateSourcedJobPostingInput {
  body: SourcedJobPostingBodyInput!
  metadata: SourcedJobPostingMetadataInput!
  applyMethod: SourcedJobPostingIndeedApplyInput
}

input SourcedJobPostingBodyInput {
  title: String!
  description: String!
  descriptionFormatting: String!
  location: LocationInput!
  benefits: [String!]!
}

input LocationInput {
  cityRegionPostal: String!
  country: String
}

input SourcedJobPostingMetadataInput {
  jobSource: JobSourceInput!
  jobPostingId: String!
  jobRequisitionId: String
  taxonomyClassification: taxonomyClassificationInput
  datePublished: String!
  url: String!
  numberOfHires: Int
  expirationDate: String
  contacts: [ContactInput!]!
}

input JobSourceInput {
  companyName: String
  sourceName: String
  sourceType: String!
}

input ContactInput {
  contactType: [String!]!
  contactInfo: ContactInfoInput!
}

input ContactInfoInput {
  contactEmail: String!
  contactName: String!
}

input taxonomyClassificationInput {
  categories: [String]
  remoteType: String
  education: String
}

input SourcedJobPostingIndeedApplyInput {
  indeedApply: indeedApplyData
}

input indeedApplyData {
  apiToken: String!
  resumeRequired: String
  postUrl: String!
  applyQuestions: String
  phoneRequired: String
}

type listWorkScheduleDetailsResponse {
  errorCode: String
  message: String
  workScheduleDetails: [listWorkScheduleDetails]
}
type listWorkScheduleDetails {
  workScheduleId: Int
  workSchedule: String
  businessHoursStartTime: String
  businessHoursEndTime: String
  breakScheduleStartTime: String
  breakScheduleEndTime: String
  targetEffortInHoursPerWeek: Float
}
type addUpdateRecruitmentStatusResponse{
  errorCode :String
  message :String
}
type addUpdateJobPostJobStreetDetails{
 errorCode :String
  message :String
}
type closeJobPostResponse{
  errorCode :String
  message :String
}
type publishJobPostToIndeedResponse{
  errorCode :String
  results: String
}
type commonResponse {
  errorCode: String
  message: String
  data: String
  validationError: String
}
type newPositionResponse {
  errorCode: String
  message: String
  validationError: String
  positionRequestId:Int
}
type closeJobpostToIndeedResponse{
  errorCode :String
  results: String
}


input foreCasteInput {
  hiringForecastId: Int!
  year: Int!
  month: Int!
  noOfPosition: Int!
}

input candidateData {
    rowNo: Int
    salutation: String
    firstName: String!
    lastName: String!
    knownAs: String
    genderIdentityId: Int
    genderExpressionId: Int
    physicallyChallenged: Int
    middleName: String
    genderId: Int
    gender: String!
    dob: String!
    bloodGroup: String
    maritalStatus: Int
    nationality: String
    nationalityId:Int
    emailId: String!
    languagesKnown: [languagesKnown]
    mobileNo: String!
    apartmentName: String
    street: String
    barangay: String
    region: String
    emergencyContactName: String
    emergencyContactNo: String
    emergencyContactRelation: String
    city: String
    state: String
    country: String
    pincode: String
    Suffix: String
    preferredLocation: [Int]
    candidateEducation: [educationDetails]
    candidateExperience: [experienceDetails]
    candidateSkills: [skillDetails]
    passportNo: String
    candidateDependent: [String]
    candidateCertification: [certificationDetails]
    candidateWorkPermit: [Int]
    candidateOtherWorkPermit: String
    verifierName: String
    verifierPhoneNo: String
    verifierEmailId: String
    jobPost: Int
    currentEmployer: String
    noticePeriod: Int
    currentCTC: Float
    expectedCTC: Float
    currency: Int
    resume: String!
    resumeFileSize: String!
    nationalIdentificationNumber: String
    candidateProfilePicture: String
    status: Int!
    totalExperienceInYears: Int,
    totalExperienceInMonths:Int,
    source: String,
    skillSet: [String]
    mobileNoCountryCode: String
    genderOrientations: String
    pronoun: String
    statutoryInsuranceNumber: String
    pranNo: String
    customFieldInputs: customFieldInputs
    dataPrivacyStatement: Int
}

input customFieldInputs {
  Form_Id: Int
  Field_Value: String!
}

input skillDetails {
  skills: String!
  description: String
  primary: String!
  proficiency: String!
}

input languagesKnown {
  langKnown: Int
  langSpoken: Int
  langReadWrite: Int
  langProficiency: String
}

input educationDetails {
  educationType: Int!
  speacialisation: String
  specializationId: Int
  institute: String
  institutionId: Int
  yearOfStart: Int
  yearOfPassing: Int
  percentage: Float
  grade: String
  startDate: Date
  endDate: Date
  city: String
  state: String
  country: String
}

input experienceDetails {
  companyName: String
  companyLocation: String
  designation: String
  startDate: String
  endDate: String
  duration: String
  years: Int
  months: Int
  referenceDetails: [candidateExperienceReference]
}

input candidateExperienceReference {
  Reference_Name: String
  Reference_Email: String
  Reference_Number: String
}

input certificationDetails {
  certificationName: String
  receivedDate: String
  certificateReceivedFrom: String
  ranking: String
  certificationFileName: String
  subTypeId: Int
  documentName: String 
}

schema {
  query: Query
  mutation: Mutation
}