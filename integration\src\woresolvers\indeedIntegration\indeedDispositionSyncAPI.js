// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const {formId} = require('../../../common/appConstants');
const axios = require('axios');


module.exports.indeedDispositionSyncAPI = async (args, context) => {
    console.log('Inside indeedDispositionSyncAPI function');
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let indeedApplyData = await getIndeedApplyId(organizationDbConnection, args.jobpostId);
        let indeedApplyId = indeedApplyData && indeedApplyData.Indeed_Apply_Id ? indeedApplyData.Indeed_Apply_Id : "";
        args.input.dispositions[0].identifiedBy.indeedApplyID = indeedApplyId;
        let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false,  formId.jobpost);
        if(Object.keys(checkRightsForForm).length > 0  && ((checkRightsForForm.Role_Add === 1) || (checkRightsForForm.Role_Update === 1))){
            const indeedUrl = process.env.indeedPublishJobAPI;
            query = `mutation Send($input : SendPartnerDispositionInput !) {
              partnerDisposition {
                send(input : $input) {
                  numberGoodDispositions 
                  failedDispositions {
                    identifiedBy {
                      indeedApplyID 
                      ittk 
                      alternateIdentifier {
                        jobIdentifier {
                          indeedJobKey 
                          atsApplicationIdentifier{
                            requisitionId 
                            companyName
                          }
                        }
                        jobSeekerIdentifier 
                          { 
                            indeedJobSeekerKey 
                            emailAddress 
                          }
                      }
                    }
                    rationale
                  }
                }
              }
            }
            `,
            variables = args.input;
            let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: indeedUrl,
            headers: { 
                'Content-Type': 'application/json', 
                'Authorization': context.indeed_access_token, 
            },
            data: JSON.stringify({
                query: query,
                variables: {
                input: args.input
                }
              })
            };

            try {
                // Make the request using Axios
                let response = await Promise.resolve(axios.request(config));
                const responseMessage = JSON.stringify(response.data);
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode: "", results: responseMessage };
              } catch (error) {
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (error.response) {
                  // The request was made and the server responded with a status code
                  console.error('Response Error:', error.response.data);
                  console.error('Status Code:', error.response.status);
                } else if (error.request) {
                  // The request was made but no response was received
                  console.error('Request Error:', error.request);
                } else {
                  // Something else happened in setting up the request
                  console.error('Other Error:', error.message);
                }
                throw 'SET0018';
              }
        }
        else {
            console.log('This employee do not have add or edit access rights');
            throw '_DB0111';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in indeedDispositionSyncAPI function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0018');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function getIndeedApplyId(organizationDbConnection, jobpostId) {
  try {
    return organizationDbConnection('candidate_recruitment_info')
      .select('Indeed_Apply_Id')
      .modify(function (queryBuilder) {
        if (jobpostId) {
          queryBuilder.where('Job_Post_Id', jobpostId);
        }
      })
      .first()
      .then((data) => {
        return data
      })
      .catch(function (err) {
        console.log('Error in getIndeedApplyId  catch() block', err)
        throw err
      })
  } catch (err) {
    console.log('Error in getIndeedApplyId main catch() block', err)
    throw err
  }
}


