//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require knex to make DB connection
const knex = require('knex')
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda')
//Require table alias
const { ehrTables } = require('../../../common/tablealias')
const { formId } = require('../../../common/appConstants')
const {
  validateJobStreetCommonRuleInput,
  checkJobStreetRecordExist,
  jobPostDetailss,
  mapErrorToCustomCodeSeek,
  checkJobStreetRecordExistJobPostId
} = require('../../common/commonFunction')
const moment = require('moment')
const axios = require('axios')
const { v4: uuidv4 } = require('uuid')
const {
  postPosition,
  updateJobProfile,
  createQuestions
} = require('../../queries/jobStreetQueries')

module.exports.addUpdateJobPostJobStreet = async (
  parent,
  args,
  context,
  info
) => {
  let organizationDbConnection
  let errResult
  let validationError = {}
  const loginEmployee_Id = context.Employee_Id
  const formIds = formId.jobpost
  organizationDbConnection = knex(context.connection.OrganizationDb)
  let checkRights = await commonLib.func.checkEmployeeAccessRights(
    organizationDbConnection,
    loginEmployee_Id,
    '',
    '',
    'UI',
    false,
    formIds
  )
  try {
    console.log('inside addUpdateJobPostJobStreet')
    organizationDbConnection = knex(context.connection.OrganizationDb)
    let orgCode = context.Org_Code
    const {
      jobStreetId,
      roleCode,
      email,
      recruiterName,
      jobId,
      documentId,
      videoPositionCode,
      videoUrl,
      positionLocationId,
      seekAdvertisementProductId,
      subCategoryId,
      categoryId,
      payDescription,
      searchBulletPointsArray,
      seekWorkTypeCode,
      advertisementBranding,
      seekBillingReference,
      searchSummaryDescription,
      profileId,
      phoneNo,
      hirerId,
      jobTitle,
      seekWorkArrangementCodes,
      recruiterNoCountryCode
    } = args
    if (
      Object.keys(checkRights).length > 0 &&
      ((jobStreetId === 0 && checkRights.Role_Add === 1) ||
        (jobStreetId > 0 && checkRights.Role_Update === 1)) &&
      checkRights.Is_Recruiter.toLowerCase() === 'yes'
    ) {
      if (args.jobStreetId) {
        let existingRecord = await checkJobStreetRecordExist(
          organizationDbConnection,
          jobStreetId,
          ['Applied']
        )
        if (!existingRecord || existingRecord.length <= 0) {
          throw 'EI00135'
        }
      } else {
        let existingRecord = await checkJobStreetRecordExistJobPostId(
          organizationDbConnection,
          jobId,
          ['Applied']
        )
        if (existingRecord && existingRecord.length > 0) {
          throw 'EI00168'
        }
      }
      const fieldValidations = {
        email: 'IVE0052',
        searchSummaryDescription: 'IVE0444',
        recruiterName: 'IVE0445',
        jobTitle: 'IVE0446'
      }
      if (payDescription & payDescription.length) {
        fieldValidations.payDescription = 'IVE0446'
      }
      if (videoUrl && videoUrl.length) {
        fieldValidations.videoUrl = 'IVE0443'
      }
      validationError = await validateJobStreetCommonRuleInput(
        args,
        fieldValidations
      )
      if (Object.keys(validationError).length > 0) {
        throw 'IVE0000'
      }
      const idempotencyId = uuidv4()
      const ACCESS_TOKEN = context.jobstreet_access_token
      const BROWSER_TOKEN = context.jobStreet_bowser_token
      const GRAPHQL_ENDPOINT = process.env.jobStreetTokenAPI
      let questionerId
      // Fetch existing Questioner_Id and Questions
      if(!jobStreetId){
      const existingData = await organizationDbConnection(
        ehrTables.seekHirerList
      )
        .select('Questioner_Id', 'Questions')
        .where('Hirer_ID', hirerId)
        .first()
        let payrollCountry = ''
        const payrollData = await organizationDbConnection(
          'payroll_general_settings'
        )
          .select('Payroll_Currency')
          .first()
        if (payrollData && payrollData.Payroll_Currency) {
          payrollCountry = payrollData.Payroll_Currency
        }

        let questionHtml = `What is your expected monthly basic salary (in ${payrollCountry})?`;
        const response = await axios.post(
          GRAPHQL_ENDPOINT,
          {
            query: createQuestions,
            variables: {
              input: {
                applicationQuestionnaire: {
                  hirerId: hirerId,
                  components: [
                    {
                      componentTypeCode: 'Question',
                      question: {
                        value: idempotencyId,
                        questionHtml: questionHtml, // Use extracted questionHtml
                        responseTypeCode: 'FreeText',
                        componentTypeCode: 'Question'
                      }
                    }
                  ]
                }
              }
            }
          },
          {
            headers: {
              Authorization: `Bearer ${ACCESS_TOKEN}`,
              'Content-Type': 'application/json'
            }
          }
        )
        questionerId =
          response?.data?.data?.createApplicationQuestionnaire
            ?.applicationQuestionnaire?.id?.value
        if (questionerId) {
          await organizationDbConnection(ehrTables.seekHirerList)
            .update({ Questioner_Id: questionerId })
            .where('Hirer_ID', hirerId)
        }
       else {
        questionerId = existingData.Questioner_Id
      }
    }
      const requestJSON = await getJobstreetData(
        args,
        idempotencyId,
        orgCode,
        organizationDbConnection,
        questionerId
      )
      let jobOpeningData = {
        Job_Post_Id: jobId,
        Role_Code: roleCode,
        Recruiter_Email_Id: email,
        Recruiter_Name: recruiterName,
        Document_Id: documentId,
        Recruiter_Phone_No: phoneNo,
        Recruiter_No_Country_Code: recruiterNoCountryCode
      }

      let jobProfileData = {
        Job_Post_Id: jobId,
        Position_Location_Id: positionLocationId,
        Job_Title: jobTitle,
        Seek_Advertisement_Product_Id: seekAdvertisementProductId,
        Sub_Category_Id: subCategoryId,
        Category_Id: categoryId,
        Seek_Work_Type_Code: seekWorkTypeCode,
        Seek_Work_Arrangement_Codes: seekWorkArrangementCodes && seekWorkArrangementCodes.length ? JSON.stringify(seekWorkArrangementCodes) : null,
        Advertisement_Branding: advertisementBranding,
        Profile_Id: profileId,
        Search_Summary_Description: searchSummaryDescription,
        Hirer_Id: hirerId,
        Pay_Description: payDescription
      }
      if (jobStreetId) {
        jobProfileData.Applied_Status = 'Update Progress'
        jobOpeningData.Applied_Status = 'Update Progress'
      } else {
        jobProfileData.Applied_Status = 'Pending'
        jobOpeningData.Applied_Status = 'Pending'
      }
      if (searchBulletPointsArray && searchBulletPointsArray.length) {
        jobProfileData['Search_Bullet_Points_Array'] = JSON.stringify(
          searchBulletPointsArray
        )
      }
      // Check if videoUrl is present, then add it to the object
      if (videoUrl) {
        jobProfileData['Video_Url'] = videoUrl
      }

      // Check if videoPositionCode is present, then add it to the object
      if (videoPositionCode) {
        jobProfileData['Video_Position_Code'] = videoPositionCode
      }

      // Check if seekBillingReference is present, then add it to the object
      if (seekBillingReference) {
        jobProfileData['Seek_Billing_Reference'] = seekBillingReference
      }
      const query = jobStreetId ? updateJobProfile : postPosition
      const variables = requestJSON

      await axios
        .post(
          GRAPHQL_ENDPOINT,
          {
            query,
            variables
          },
          {
            headers: {
              Authorization: `Bearer ${ACCESS_TOKEN}`,
              'Content-Type': 'application/json'
            }
          }
        )
        .then(async (response) => {
          if (
            response.data &&
            response.data.errors &&
            response.data.errors.length
          ) {
            console.log(
              'Error in addUpdateJobPostJobStreet function catch block.',
              response.data.errors
            )
            let errorCode = await mapErrorToCustomCodeSeek(response.data)
            if (errorCode === 'CH0001') {
              console.log('unathentcated request check the tokens')
            } else if (errorCode === 'CH0003') {
              console.log('check the validations bad_input error')
            }

            throw 'EI00152'
          }
          if (
            (response.data.data.postPosition &&
              response.data.data.postPosition.positionProfile &&
              response.data.data.postPosition.positionProfile.profileId &&
              response.data.data.postPosition.positionProfile.profileId
                .value) ||
            (response.data.data.updatePostedPositionProfile &&
              response.data.data.updatePostedPositionProfile.positionProfile &&
              response.data.data.updatePostedPositionProfile.positionProfile
                .profileId &&
              response.data.data.updatePostedPositionProfile.positionProfile
                .profileId.value)
          ) {
            jobProfileData.Applied_Status = 'Applied'
            jobOpeningData.Applied_Status = 'Applied'
            if (jobStreetId) {
              jobProfileData.Profile_Id =
                response.data.data.updatePostedPositionProfile.positionProfile.profileId.value
            } else {
              jobProfileData.Profile_Id =
                response.data.data.postPosition.positionProfile.profileId.value
            }
            if (
              !args.jobStreetId &&
              response.data.data.postPosition.positionOpening.documentId &&
              response.data.data.postPosition.positionOpening.documentId.value
            ) {
              jobOpeningData.Document_Id =
                response.data.data.postPosition.positionOpening.documentId.value
            } else if (!args.jobStreetId) {
              throw 'EI00154'
            }
          } else {
            throw 'EI00153'
          }
        })
        .catch(async (error) => {
          if (error && error.length && Array.isArray(error)) {
            let errorCode = await mapErrorToCustomCodeSeek(error.response.data)
            if (errorCode === 'CH0001') {
              console.log(
                'unathentcated request check the tokens in catch block'
              )
            }

            throw 'EI00152'
          } else {
            throw error
          }
        })
      return await organizationDbConnection.transaction(async (trx) => {
        let existingRecord = await checkJobStreetRecordExistJobPostId(
          organizationDbConnection,
          jobId
        )
        if (jobStreetId) {
          jobProfileData.Updated_On = moment()
            .utc()
            .format('YYYY-MM-DD HH:mm:ss')
          jobProfileData.Updated_By = loginEmployee_Id
        } else {
          jobOpeningData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
          jobOpeningData.Added_By = loginEmployee_Id
          jobProfileData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
          jobProfileData.Added_By = loginEmployee_Id
        }
        if (
          !jobStreetId &&
          existingRecord &&
          existingRecord.length &&
          existingRecord[0].Job_Street_Id
        ) {
          jobOpeningData.Job_Street_Id = existingRecord[0].Job_Street_Id
          jobProfileData.Job_Street_Id = existingRecord[0].Job_Street_Id
          jobProfileData.Updated_On = null
          jobProfileData.Updated_By = null
          jobOpeningData.Updated_On = null
          jobOpeningData.Updated_By = null
          jobOpeningData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
          jobOpeningData.Added_By = loginEmployee_Id
          jobProfileData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
          jobProfileData.Added_By = loginEmployee_Id
        }
        if (!jobStreetId) {
          let updateResult = await organizationDbConnection(
            ehrTables.jobStreetJobOpenings
          )
            .transacting(trx)
            .modify((queryBuilder) => {
              if (
                existingRecord &&
                existingRecord.length &&
                existingRecord[0].Job_Street_Id
              ) {
                queryBuilder.update(jobOpeningData)
                queryBuilder.where(
                  'Job_Street_Id',
                  existingRecord[0].Job_Street_Id
                )
              } else {
                queryBuilder.insert(jobOpeningData)
              }
            })
            .catch((error) => {
              console.log(
                'Error in addUpdateJobPostJobStreet .catch() block',
                error
              )
              throw error
            })
          jobProfileData.Job_Street_Id = updateResult[0]
        }
        if (args.minimumAmount && args.maximumAmount && args.currency) {
          let currencyValue = await getCurrencyValueWithCode(
            organizationDbConnection,
            trx,
            args.currency
          )
          if (currencyValue && currencyValue.length) {
            await organizationDbConnection(ehrTables.jobPost)
              .transacting(trx)
              .update({
                Min_Payment_Frequency: args.minimumAmount,
                Max_Payment_Frequency: args.maximumAmount,
                Currency: currencyValue[0]
              })
              .where('Job_Post_Id', jobId)
          }
        }
        jobStreetId
          ? await organizationDbConnection(ehrTables.jobStreetJobProfile)
              .transacting(trx)
              .where('Job_Street_Id', jobStreetId)
              .update(jobProfileData)
              .catch((error) => {
                console.log(
                  'Error in addUpdateJobPostJobStreet .catch() block',
                  error
                )
                throw error
              })
          : await organizationDbConnection(ehrTables.jobStreetJobProfile)
              .transacting(trx)
              .modify((queryBuilder) => {
                if (
                  existingRecord &&
                  existingRecord.length &&
                  existingRecord[0].Job_Street_Id
                ) {
                  queryBuilder.update(jobProfileData)
                  queryBuilder.where(
                    'Job_Street_Id',
                    existingRecord[0].Job_Street_Id
                  )
                } else {
                  queryBuilder.insert(jobProfileData)
                }
              })
              .catch((error) => {
                console.log(
                  'Error in addUpdateJobPostJobStreet .catch() block',
                  error
                )
                throw error
              })
        let systemLogParam = {
          userIp: context.User_Ip,
          employeeId: loginEmployee_Id,
          organizationDbConnection: organizationDbConnection,
          message: `Job post ${jobStreetId ? 'updated' : 'added'} sucessfuly`
        }
        await commonLib.func.createSystemLogActivities(systemLogParam)
        organizationDbConnection ? organizationDbConnection.destroy() : null
        return {
          errorCode: '',
          message: `Job post ${jobStreetId ? 'updated' : 'added'} sucessfuly`
        }
      })
    } else {
      if (
        Object.keys(checkRights).length < 0 ||
        checkRights.Role_Add !== 1 ||
        checkRights.Role_Update !== 1
      )
        if (jobStreetId) {
          console.log('The employee does not have edit access.')
          throw '_DB0102'
        } else {
          console.log('The employee does not have add access.')
          throw '_DB0101'
        }
      else {
        throw '_DB0115'
      }
    }
  } catch (e) {
    console.log(
      'Error in addUpdateJobPostJobStreet  function main catch block.',e?.response?.data?.errors
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    if (e === 'IVE0000') {
      console.log(
        'Validation error in the addUpdateJobPostJobStreet  function',
        validationError
      )
      errResult = commonLib.func.getError('', 'IVE0000')
      throw new UserInputError(errResult.message, {
        validationError: validationError
      })
    } else if (e === 'EI00152') {
      throw new ApolloError('', 'EI00152')
    } else {
      errResult = commonLib.func.getError(e, 'EI00138')
      throw new ApolloError(errResult.message, errResult.code)
    }
  }
}
async function getJobstreetData(
  args,
  idempotencyId,
  orgCode,
  organizationDbConnection,
  questionerId
) {
  const {
    jobStreetId,
    roleCode,
    email,
    recruiterName,
    videoPositionCode,
    videoUrl,
    positionLocationId,
    seekAdvertisementProductId,
    categoryId,
    payDescription,
    seekWorkTypeCode,
    searchSummaryDescription,
    profileId,
    jobTitle,
    jobSummary,
    phoneNo,
    searchBulletPointsArray,
    seekBillingReference,
    advertisementBranding,
    hirerId,
    seekWorkArrangementCodes,
    jobId,
    recruiterNoCountryCode
  } = args
  let existingJobPostDetails = await jobPostDetailss(
    organizationDbConnection,
    jobId
  )
  if (!existingJobPostDetails || !existingJobPostDetails.length) {
    console.log('existing job post data corresponding to job post id not found')
    throw 'EI00151'
  }
  let { basiscode, minimumAmount, maximumAmount, intervalcode, currency } =
    existingJobPostDetails[0]
  if (args.minimumAmount && args.maximumAmount && args.currency) {
    minimumAmount = +args.minimumAmount
    maximumAmount = +args.maximumAmount
    currency = args.currency
  }
  let phoneNumberArray = []
  if (phoneNo && phoneNo.length) {
    phoneNumberArray.push({
      formattedNumber: `+${recruiterNoCountryCode}${phoneNo}`
    })
  }
  let payDescriptionArray = []
  if (payDescription && payDescription.length) {
    payDescriptionArray.push(payDescription)
  }
  let positionFormattedDescriptions = [
    {
      descriptionId: 'SearchSummary',
      content: searchSummaryDescription
    },
    {
      descriptionId: 'AdvertisementDetails',
      content: jobSummary
    }
  ]
  if (searchBulletPointsArray && searchBulletPointsArray.length) {
    searchBulletPointsArray.forEach((element) => {
      positionFormattedDescriptions = [
        ...positionFormattedDescriptions,
        {
          descriptionId: 'SearchBulletPoint',
          content: element
        }
      ]
    })
  }
  let postingInstructions = {
    seekAdvertisementProductId: seekAdvertisementProductId,
    brandingId:
      advertisementBranding && advertisementBranding.length
        ? advertisementBranding
        : null
  }
  if (!seekAdvertisementProductId && jobStreetId) {
    postingInstructions = {}
  }
  let data
  if (!jobStreetId) {
    data = {
      input: {
        positionOpening: {
          postingRequester: {
            roleCode: 'Company',
            id: hirerId,
            personContacts: [
              {
                name: { formattedName: recruiterName },
                roleCode: roleCode,
                communication: {
                  email: [{ address: email }],
                  phone: phoneNumberArray
                }
              }
            ]
          }
        },
        positionProfile: {
          positionTitle: jobTitle,
          positionOrganizations: hirerId,
          seekBillingReference: seekBillingReference,
          seekHirerJobReference: `${orgCode}-${jobId}`,
          positionFormattedDescriptions: positionFormattedDescriptions,
          offeredRemunerationPackage: {
            basisCode: basiscode,
            ranges: {
              minimumAmount: {
                value: minimumAmount ? minimumAmount : 1,
                currency: currency
              },
              maximumAmount: {
                value: maximumAmount,
                currency: currency
              },
              intervalCode: intervalcode
            },
            descriptions: payDescriptionArray
          },
          seekAnzWorkTypeCode: seekWorkTypeCode,
          jobCategories: categoryId,
          positionLocation: positionLocationId,
          postingInstructions: {
            brandingId:
              advertisementBranding && advertisementBranding.length
                ? advertisementBranding
                : null,
            seekAdvertisementProductId: seekAdvertisementProductId,
            idempotencyId: idempotencyId
          },
          seekApplicationQuestionnaireId: questionerId
        }
      }
    }
  } else {
    data = {
      input: {
        positionProfile: {
          profileId: profileId,
          positionTitle: jobTitle,
          positionOrganizations: hirerId,
          positionFormattedDescriptions: [
            {
              descriptionId: 'SearchSummary',
              content: searchSummaryDescription
            },
            {
              descriptionId: 'AdvertisementDetails',
              content: jobSummary
            }
          ],
          offeredRemunerationPackage: {
            basisCode: basiscode,
            ranges: {
              minimumAmount: {
                value: minimumAmount ? minimumAmount : 1,
                currency: currency
              },
              maximumAmount: {
                value: maximumAmount,
                currency: currency
              },
              intervalCode: intervalcode
            },
            descriptions: payDescriptionArray
          },
          seekBillingReference: seekBillingReference,
          seekHirerJobReference: `${orgCode}-${jobId}`,
          seekAnzWorkTypeCode: seekWorkTypeCode,
          jobCategories: categoryId,
          positionLocation: positionLocationId,
          postingInstructions: postingInstructions
        }
      }
    }
  }
  if(seekWorkArrangementCodes && seekWorkArrangementCodes.length){
    data.input.positionProfile.seekWorkArrangementCodes=seekWorkArrangementCodes
  }
  // Add seekVideo only if both videoPositionCode and videoUrl are present
  if (videoPositionCode && videoUrl) {
    data.input.positionProfile.seekVideo = {
      seekAnzPositionCode: videoPositionCode,
      url: videoUrl
    }
  }
  return data
}

async function getCurrencyValueWithCode(
  organizationDbConnection,
  trx,
  currency
) {
  try {
    return await organizationDbConnection('currency')
      .transacting(trx)
      .where('Currency_Code', currency)
      .pluck('Currency_Id')
  } catch (err) {
    console.log(
      'Error in getCurrencyValueWithCode  function main catch block.',
      err
    )
    throw err
  }
}
