let ehrTables = {
    workSchedule: 'work_schedule',
    workscheduleWeekoff: 'workschedule_weekoff',
    weekdays: 'weekdays',
    timezone: 'timezone',
    empPersonalInfo: 'emp_personal_info',
    empJob: 'emp_job',
    shiftType: 'shift_type',
    empShiftType: 'emp_shift_type',
    employeeMonitorSettings: 'employee_monitor_settings',
    empAttendance: 'emp_attendance',
    shiftEmpMapping: 'shift_emp_mapping',
    documentTemplateEngine: 'document_template_engine',
    empGeneratedDocuments: 'emp_generated_documents',
    generatedDocumentCustomComponents: 'generated_document_custom_components',
    orgDetails: "org_details",
    contactDetails: "contact_details",
    empDependent: "emp_dependent",
    employeeType: "employee_type",
    designation: "designation",
    empEducation: "emp_education",
    cusEmpGroupEmployees: 'custom_employee_group_employees',
    cusEmpGroup: 'custom_employee_group',
    employeeInfoTimestampLog: "employee_info_timestamp_log",
    externalApiIntegrationLog: 'external_api_integration_log',
    camuEmployeeResignationManager: "camu_employee_resignation_manager",
    recruitmentIntegration: "recruitment_integration",
    jobPostLocation: 'job_post_location',
    location: "location",
    city: "city",
    state: "state",
    country: "country",
    jobPost: "job_post",
    candidatePersonalInfo: "candidate_personal_info",
    candidateContactDetails: "candidate_contact_details",
    candidateRecruitmentInfo: "candidate_recruitment_info",
    jobPostIndeedIntegration: "job_post_indeed_integration",
    serviceProvider: "service_provider",
    jobStreetJobOpenings: 'job_street_job_openings',
    jobStreetJobProfile: 'job_street_job_profile',
    organizationGroup: 'organization_group',
    SFWPOrganizationStructure: 'SFWP_Organization_Structure',
    empPassport: 'emp_passport',
    empDrivingLicense: 'emp_drivinglicense',
    employeeAccreditationDetails: 'employee_accreditation_details',
    accreditationCategoryAndType: 'accreditation_category_and_type',
    empDocumentCategory: 'emp_document_category',
    documentCategory: 'document_category',
    documentType: 'document_type',
    documentSubType: 'document_sub_type',
    empDocuments: 'emp_documents',
    eduSpecialization: 'edu_specialization',
    eduInstitution: 'edu_institution',
    religion: 'religion',
    nationality: 'nationality',
    maritalStatus: 'marital_status',
    careerPIC: 'career_pic',
    timekeepingPIC: 'timekeeping_pic',
    sfwpJobFamily: 'SFWP_job_family',
    branchEmailAddress: 'branch_email_address',
    taxDetails: 'tax_details',
    courseDetails: 'course_details',
    accreditationCategoryType: 'accreditation_category_and_type',
    jobStreetJobOpenings: 'job_street_job_openings',
    jobStreetJobProfile: 'job_street_job_profile',
    seekHirerList: 'seek_hirer_list',
    currency: 'currency',
    jobPostRecruiters: 'job_post_recruiters',
    mppHiringForecast: 'mpp_hiring_forecast',
    mppForecastSettings: 'mpp_forecast_settings',
    mppForecastSettingsRole: 'mpp_forecast_settings_role',
    dynamicFormBuilder: 'dynamic_form_builder',
    mppRecruitmentRequest: 'mpp_recruitment_request',
    mppPositionRequest: 'mpp_position_request',
    mppWorkingConditions: 'mpp_working_conditions',
    mppDutiesResponsibilities: 'mpp_duties_responsibilities',
    mppExperience: 'mpp_experience_requirements',
    sfwpJobDescriptionMaster: 'job_description_master',
    mppEducationRequirements: 'mpp_education_requirements',
    mppEducationRequirementsDescriptions: 'mpp_education_requirements_descriptions',
    workflows: 'workflows',
    talentPool: 'talent_pool',
    interviewRounds: 'interview_rounds',
    interviewCandidates: 'interview_candidates',
    candidateReference: 'candidate_reference',
    candidatePassport: 'candidate_passport',
    atsStatusTable: 'ats_status_table',
    atsStatusStage: 'ats_status_stage',
    positionLevel: 'position_level',
    candidateArchiveNotification: 'candidate_archive_notification',
    archiveReason: 'archive_reasons',
    archiveReasonsMappingStage: 'archive_reasons_mapping_stage',
    employeeLevelMicrosoftIntegration: 'employee_level_microsoft_integration',
    interviews: 'interviews',
    candidateCustomFieldValues: 'candidates_custom_field_values',
    empBankdetails: 'emp_bankdetails',
    department: 'department',
    businessUnit: 'business_unit',
    accountType: 'account_type',
    bankDetails: 'bank_details',
    empGrade: 'emp_grade',
    empLanguage: 'emp_language',
    jobPostHiringManagers: 'job_post_hiring_managers',
    empExperience: 'emp_experience',
    empExperienceReference: 'emp_experience_reference',
    genderPronoun: 'gender_pronoun',
    genderOrientations: 'gender_orientations',
    genderIdentity: 'gender_identity',
    genderExpression: 'gender_expression',
    empCertifications: 'emp_certifications',
    teamSummaryCustomFieldValues: 'team_summary_custom_field_values',
    gender: 'gender',
    externalApiCredentials: 'external_api_credentials',
    externalApiSyncDetails: 'external_api_sync_details',
    empResignation: 'emp_resignation',
    esicReason: 'esic_reason',
    empLeaves: 'emp_leaves',
    externalAPISyncStatus: 'external_api_sync_status',
    leaveTypes: 'leave_types',
    employeeFullAndFinalSettlement: 'employee_full_and_final_settlement',
    finalSettlementEarningsDeductions: 'final_settlement_earnings_deductions',
    salaryPayslipExtended: 'salary_payslip_extended',
    airTicketSettlementSummary: 'air_ticket_settlement_summary',
    empAirTicketPolicy: 'emp_air_ticket_policy',
    dataIntegrationFailureLogs: 'data_integration_failure_logs',
    empEligibleLeaves: 'emp_eligible_leave',
    hmcEmpSalaryPayslip: 'hmc_emp_salary_payslip',
    bloodGroup: 'blood_group'
};
let appManagerTables = {
    orgRateChoice: 'org_rate_choice',
    activityTrackerUpdates: 'activity_tracker_release_updates',
    appActivitySummarizationManager: 'app_activity_summarization_manager',
    urlActivitySummarizationManager: 'url_activity_summarization_manager',
    appurlActivitySummarizationManager: 'appurl_activity_summarization_manager',
    employeeActivitySummarizationManager: 'employee_activity_summarization_manager',
    stealthActivityTrackerReleaseUpdates: 'stealth_activity_tracker_release_updates',
    insightsNotificationManager: 'insights_notification_manager',
    dataIntegrationSchedule: 'data_integration_schedule',
    orgSecretDetails: 'org_secret_details',
    hrappPlanDetails: 'hrapp_plan_details',
    domainPurgeManager: 'domain_purge_manager',
    appPurgeManager: 'app_purge_manager',
    weekOffDatesManager: 'week_off_dates_manager',
    hrappRegisteruser: 'hrapp_registeruser',
    resignationManager: 'resignation_manager',
};

/** Alias name for ehr tables */
module.exports = {
    ehrTables,
    appManagerTables
}