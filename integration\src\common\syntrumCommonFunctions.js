//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { syntrumValues } = require('../../common/appConstants');
//Require table names
const { ehrTables } = require('../../common/tablealias');
//Require moment
const moment = require('moment');

async function handleErrorLog(organizationDbConnection,errorInputs){
    try{
        let logDetailsArray = [];
        let { inputDetails, uniqueIds, entityType, action, errorCodesListToHandle, apiError }= errorInputs;
        let apiResponse = await handleErrors(apiError,errorCodesListToHandle);

        for(let entityId of uniqueIds){
            let logDetailsJson = { 
                Status: 'Failed', 
                Action: action,
                Integration_Type: syntrumValues.integrationType, 
                Entity_Type: entityType,
                Entity_Id: entityId, 
                Form_Data: inputDetails, 
                Failure_Reason:  JSON.stringify(apiResponse),
                Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
            };
            logDetailsArray.push(logDetailsJson);
        }
        if(logDetailsArray.length){
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(logDetailsArray);
        }
        return true;
    } catch (catchError) {
        console.log('Error in formFailureRecordsInputs function main catch block. ', catchError,errorInputs);
        return false;
    }
}

//Function to handle the errors and form the error response JSON to store in the error log table
async function handleErrors(apiError,errorCodesListToHandle){
    let errorResponseJSON = {};
    let responseStatus = '';
    let responseData = '';
    let responseStatusText = '';
    
    try {
        console.error("Inside handleErrors function",apiError);

        if(errorCodesListToHandle.includes(apiError)){
            console.error("Internal error in handleErrors function");
            let {code,message} = commonLib.func.getError('', apiError);
            responseStatus = code;
            responseStatusText = message;
        } else if (apiError.errorCode) {
            console.error("Custom error in handleErrors function");
            // Handling custom Errors
            let { errorCode, message,response } = apiError;

            responseStatus = errorCode;
            responseStatusText = message;
            responseData = response;
        } else if (apiError.response) {
            console.error("API error in handleErrors function");
            // Handling API apiErrors (e.g., Axios)
            let { status, data } = apiError.response;
            responseStatus = status;
            responseData = data;
        } else if (apiError.sqlMessage) {
            console.error("SQL error in handleErrors function");
            // Handling SQL errors (e.g., MySQL, Knex)
            let { code, sqlMessage, sql } = apiError;

            responseStatus = code;
            responseStatusText = sqlMessage;
            responseData = sql;
        } else {
            // Handling Unknown Errors
            console.error("Unknown Error in handleErrors function");
            responseData = apiError;
        }
        errorResponseJSON = {
            status: false, 
            message: `${responseStatus || ' Unknown Status Code '} - ${responseStatusText || 'Unknown Status Text'}`,
            data:JSON.stringify(responseData)
        };
        console.log('responseData',responseData,"errorResponseJSON",errorResponseJSON)
        return errorResponseJSON;
    } catch (error) {
        // Catching any unexpected errors while processing the original error
        console.error("Error in handleError function main catch block", error);
        errorResponseJSON = {
            status: false,
            message: `${responseStatus || ' Unknown Status Code '} - ${responseStatusText || 'Unknown Status Text'}`,
            data: error.message
        };
        return errorResponseJSON;
    }
}

//Log the failure records in the table
async function addExternalIntegrationFailureRecords(organizationDbConnection, { status, action, entityType, uniqueIds, formData, apiResponse }) {
    try {
        const failureData=  uniqueIds.map((uniqueId) => {
            return {
                Status: status ? 'Success' : 'Failed', 
                Action: action, 
                Entity_Type: entityType,
                Integration_Type: syntrumValues.integrationType,
                Entity_Id: uniqueId,
                Form_Data: formData || {},
                Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                Added_By: 1,
                Failure_Reason: apiResponse || null
            }
        });

        if(failureData.length){
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(failureData)
        }
    } catch (error) {
        console.error('Error in the addExternalIntegrationFailureRecords function main catch block ', error);
        return false;
    }
}

function extractResponse(response, key) {
    if (response === null || typeof response !== "object") return null;

    if (response.hasOwnProperty(key)) return response[key];

    for (const k in response) {
        const found = extractResponse(response[k], key);
        if (found !== null) return found;
    }
    return null;
}

function replaceNullValues(objectValue, sameKeyValueObject = []) {
    if (typeof objectValue === undefined || objectValue === null) {
        return '';
    }
    if (Array.isArray(objectValue)) {
        return objectValue.map(item => replaceNullValues(item, sameKeyValueObject));
    }
    if (typeof objectValue === 'object') {
        const newObj = {};
        for (const key in objectValue) {
            if (objectValue.hasOwnProperty(key)) {
                if (sameKeyValueObject.includes(key)) {
                    newObj[key] = null;
                    continue;
                }
                newObj[key] = replaceNullValues(objectValue[key], sameKeyValueObject);
            }
        }
        return newObj;
    }
    return objectValue;
}


//Function to call the syntrum API
async function callSyntrumAPI(endpointName, inputDetails, type , organizationDbConnection) { 
    try {
        console.log(`Inside callSyntrumAPI ${type} function`);

        const axios = require('axios');
        const { getSyntrumAuthToken } = require('../woresolvers/syntrum/getSyntrumAuthToken');

        let {apiUrl,authToken} = await getSyntrumAuthToken(organizationDbConnection);
        const cookies = `refreshToken=${authToken}`;

        let url = apiUrl+endpointName;

        //call API to update the integration details
        const config = {
            method: 'post', 
            url: url,
            maxBodyLength: Infinity,
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer '+ authToken,
                "Cookie": cookies
            },
            data: JSON.stringify(inputDetails),
            timeout: 60000 //1minute
        };

        const { data: response } = await axios.request(config);
        console.log('Integration response ', JSON.stringify(response),config);

        // Return success status with the response data
        return { status: true, message: 'Success Response', response: response };
    } catch (error) {
        console.error('Error in callSyntrumAPI function main catch block',error,inputDetails,type);

        // Destructure error response details, if available
        const { status, statusText, data } = error.response || error || {};
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
            return { status: false, message: `408 - Request Timeout - The server took longer than 1 minute to respond.`, response: null };
        }
        // Return failure status with error details
        return { status: false, message: `${status || ' Unknown Status Code '} - ${statusText || 'Unknown Status Text'}`, response: data };
    }
}

module.exports={
    handleErrors,
    handleErrorLog,
    addExternalIntegrationFailureRecords,
    extractResponse,
    replaceNullValues,
    callSyntrumAPI
}