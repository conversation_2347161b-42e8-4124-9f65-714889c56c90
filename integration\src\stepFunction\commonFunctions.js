// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;



async function getConnection(stageName,dbPrefix,dbSecretName,region,orgCode)
{
    try{
        let connection = await commonLib.func.getDataBaseConnection(
        {
            stageName: stageName, dbPrefix: dbPrefix, dbSecretName: dbSecretName,
            region: region, orgCode: orgCode
        })
        return connection;
    } catch (catchError) {
        console.log('Error in getConnection function main catch block', catchError);
        return {};
    }
}

 // function to insert in master table
 async function insertInMasterTable(appmanagerDbConnection,inputParams,tableName){
    try{
        return(
            appmanagerDbConnection(tableName)
            .insert(inputParams)
            .then(async (insertData) => {
                return true;
            })
            .catch(catchError=>{
                console.log('Error in insertInMasterTable .catch block.', catchError);
                return false;
            })
        );
    }
    catch(error){
        console.log('Error in insertInMasterTable function main catch block',error);
        return false;
    }    
}

 // function to update in master table
 async function updateInMasterTable(appmanagerDbConnection,inputParams,tableName,orgCode){
    try{
        return(
            appmanagerDbConnection(tableName)
            .update(inputParams)
            .where('Org_Code',orgCode)
            .then((updatedData) => {
                return 'success';
            })
            .catch(catchError=>{
                console.log('Error in updatedInMasterTable .catch block.', catchError);
                return 'error';
            })
        );
    }
    catch(error){
        console.log('Error in updateInMasterTable function main catch block',error);
        return 'error';
    }    
}

// function to remove user from master table where  master orgcode not exist in orgCodeList
async function removeBulkInstancesFromAppManager(appmanagerDbConnection,orgCodeList,masterTable){
    try{
        console.log('Inside  removeBulkInstancesFromAppManager function for ');
        return(
            // based on orgcode delete the user details in manager table
            appmanagerDbConnection(masterTable)
            .del()
            .whereNotIn('Org_Code',orgCodeList)
            .then(deleteUser =>{
                console.log('Instances deleted from app manager table',deleteUser);
                return 'success';
            })
            .catch(catchError=>{
                console.log('Error in removeBulkInstancesFromAppManager function .catch block.', catchError);
                return '';
            })
        );
    }
    catch(error){
        console.log('Error in removeBulkInstancesFromAppManager function main catch block.', error);
        return '';
    }
};

async function getOpenStatusDataFromMasterTable(appmanagerDbConnection,table,columnName)
{
    try{
        return(
            appmanagerDbConnection(table)
            .select('*')
            .where(columnName,"Open")
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log('Error in getOpenStatusDataFromMasterTable function .catch block.', e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log('Error in getOpenStatusDataFromMasterTable function main catch block.', e);
        return false;
    }
}
module.exports = {
    getConnection,
    insertInMasterTable,
    updateInMasterTable,
    removeBulkInstancesFromAppManager,
    getOpenStatusDataFromMasterTable
    
};