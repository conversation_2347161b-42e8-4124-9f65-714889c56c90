// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../../../common/tablealias')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')

module.exports.listDetailsBasedOnOrgPosId = async (
  parent,
  args,
  context,
  info
) => {
  console.log('Inside listDetailsBasedOnOrgPosId function.')
  let organizationDbConnection

  try {
    let employeeId = context.Employee_Id
    organizationDbConnection = knex(context.connection.OrganizationDb)

    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      '',
      '',
      'UI',
      false,
      args.formId
    )

    if (
      Object.entries(checkRights).length > 0 &&
      checkRights.Role_View === 1
    ) {
      const positionDetails = await organizationDbConnection(
        ehrTables.SFWPOrganizationStructure + ' as OS'
      )
        .select(
          'OS.Parent_Path as parentPath',
          'OS.Cost_Code as costCode',
          'OS.Approved_Position as approvedPosition',
          'OS.Warm_Bodies as warmBodies',
          'PL.Position_Level as positionLevel',
          'PL.Position_Level_Id as positionLevelId'
        )
        .leftJoin(ehrTables.positionLevel + ' as PL', 'OS.Position_Level', 'PL.Position_Level_Id')
        .where('OS.Organization_Structure_Id', args.organizationStructureId);
      let parentStructureIds = positionDetails[0].parentPath
        ? positionDetails[0].parentPath.split(',')
        : [];
      let parentStructureDetails = await organizationDbConnection(
        ehrTables.SFWPOrganizationStructure
      )
        .select('Pos_Code', 'Pos_Name', 'Org_Level',
          organizationDbConnection.raw(
            "CASE WHEN Pos_Code IS NOT NULL THEN CONCAT(Pos_Name,' - ',Pos_Code) ELSE Pos_Name END AS Pos_full_Name"
          )
        )
        .whereIn('Originalpos_Id', parentStructureIds)
        if(parentStructureDetails &&parentStructureDetails.length<=1 &&parentStructureIds.length===1 &&parentStructureIds[0]==0){
          parentStructureDetails = await organizationDbConnection(
            ehrTables.SFWPOrganizationStructure
          )
            .select('Pos_Code', 'Pos_Name', 'Org_Level',
              organizationDbConnection.raw(
                "CASE WHEN Pos_Code IS NOT NULL THEN CONCAT(Pos_Name,' - ',Pos_Code) ELSE Pos_Name END AS Pos_full_Name"
              )
            )
            .whereIn('Organization_Structure_Id', [args.organizationStructureId]);
        }
        let result;
        if(args.originalPosId){
        result = await organizationDbConnection(ehrTables.mppRecruitmentRequest)
        .sum('No_Of_Position')
        .whereNot('Status', 'Rejected')
        .where('Original_Position_Id', args.originalPosId);
       }
      const totalRecruitmentCount = result && result[0]?.['sum(`No_Of_Position`)'] || 0;
      let positionDetailsObject = {}
      positionDetailsObject.approvedPosition=positionDetails[0].approvedPosition
      positionDetailsObject.warmBodies=positionDetails[0].warmBodies;
      positionDetailsObject.costCode = positionDetails[0].costCode
      positionDetailsObject.positionLevel = positionDetails[0].positionLevel
      positionDetailsObject.totalRecruitmentCount = totalRecruitmentCount

      if (parentStructureDetails && parentStructureDetails.length) {
        positionDetailsObject.groupCode = parentStructureDetails.filter(
          (grp) => grp.Org_Level === 'GRP'
        )[0]?.Pos_Code
        positionDetailsObject.groupName = parentStructureDetails.filter(
          (grp) => grp.Org_Level === 'GRP'
        )[0]?.Pos_Name
        positionDetailsObject.groupFullName = parentStructureDetails.filter(
          (grp) => grp.Org_Level === 'GRP'
        )[0]?.Pos_full_Name
        positionDetailsObject.divisionCode = parentStructureDetails.filter(
          (div) => div.Org_Level === 'DIV'
        )[0]?.Pos_Code
        positionDetailsObject.divisionName = parentStructureDetails.filter(
          (div) => div.Org_Level === 'DIV'
        )[0]?.Pos_Name
        positionDetailsObject.divisionFullName = parentStructureDetails.filter(
          (div) => div.Org_Level === 'DIV'
        )[0]?.Pos_full_Name
        positionDetailsObject.sectionCode = parentStructureDetails.filter(
          (sec) => sec.Org_Level === 'SEC'
        )[0]?.Pos_Code
        positionDetailsObject.sectionName = parentStructureDetails.filter(
          (sec) => sec.Org_Level === 'SEC'
        )[0]?.Pos_Name
        positionDetailsObject.sectionFullName = parentStructureDetails.filter(
          (sec) => sec.Org_Level === 'SEC'
        )[0]?.Pos_full_Name
        positionDetailsObject.deptCode = parentStructureDetails.filter(
          (dept) => dept.Org_Level === 'DEPT'
        )[0]?.Pos_Code
        positionDetailsObject.deptName = parentStructureDetails.filter(
          (dept) => dept.Org_Level === 'DEPT'
        )[0]?.Pos_Name
        positionDetailsObject.deptFullName = parentStructureDetails.filter(
          (dept) => dept.Org_Level === 'DEPT'
        )[0]?.Pos_full_Name
        
      }
      organizationDbConnection ? organizationDbConnection.destroy() : null
      return {
        errorCode: '',
        message: 'Position details retrieved successfully.',
        positionDetails: positionDetailsObject
      }
    } else {
      throw '_DB0100'
    }
  } catch (err) {
    //Destroy DB connection
    console.error(
      'Error in listDetailsBasedOnOrgPosId function main catch block.',
      err
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    let errResult = commonLib.func.getError(err, 'EI00178')
    throw new ApolloError(errResult.message, errResult.code)
  }
}
