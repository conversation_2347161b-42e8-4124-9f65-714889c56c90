const { CommonLib } = require("@cksiva09/hrapp-corelib");
const { getConnection } = require('../../stepFunction/commonFunctions')
//Require knex to make DB connection
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../../../common/tablealias');

module.exports.retrieveScreenerQuestionsEndpoint = async (event, context) => {
    console.log('Inside retrieveScreenerQuestionsEndpoint function');
    let jobpostQueryObject = event?.queryStringParameters;
    let demographicQuestions = {
      "questions": [
        {
          "id": "disabilities53422",
          "type": "select",
          "question": "Do you have a disability?",
          "options": [
            {
              "label": "false",
              "value": "0"
            },
            {
              "label": "true",
              "value": "1"
            }
          ],
          "required": false
        }
      ]
    };
    let organizationDbConnection
    try {
      if(jobpostQueryObject && jobpostQueryObject.jobpostId) {
        let jobpostIdData =  jobpostQueryObject.jobpostId;
        let orgCodeAndJobpostId = jobpostIdData.split('-');
        let orgCode = orgCodeAndJobpostId[0] ? orgCodeAndJobpostId[0] : null;
        let hrappJobpostId = orgCodeAndJobpostId[1] ? orgCodeAndJobpostId[1] : null
        let databaseConnection = await getConnection(
          process.env.stageName,
          process.env.dbPrefix,
          process.env.dbSecretName,
          process.env.region,
          orgCode
        )
        // check whether data exist or not
        if (databaseConnection && Object.keys(databaseConnection).length) {
          organizationDbConnection = knex(databaseConnection.OrganizationDb)
            return (
              organizationDbConnection(ehrTables.jobPostIndeedIntegration + " as JPII")
              .select('DFB.Template_Name', 'DFB.Form_Template')
              .innerJoin(ehrTables.dynamicFormBuilder +' as DFB','JPII.Dynamic_Form_Id','DFB.Template_Id')
              .where('JPII.Job_Post_Id', hrappJobpostId)
              .then(async (data) => {
                  let formTemplate = data[0].Form_Template;
                  let jsonData = JSON.parse(formTemplate);
                  let screeningQuestions = transformData(jsonData);
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  return {
                      statusCode: 200,
                      body: JSON.stringify( {
                          schemaVersion: "1.0",
                          screenerQuestions: {
                            questions: screeningQuestions
                          },
                          demographicQuestions: demographicQuestions
                      })
                  };
              })
                /**check and return if any error occured */
                .catch(function (err) {
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  console.log('Error in retrieveScreenerQuestionsEndpoint .catch function', err);
                  // Handle the error response with a 500 status code
                  return {
                    statusCode: 500,
                    body: JSON.stringify({
                        errorCode: 'InternalServerError',
                        message: 'An error occurred while processing the request.'
                    })
                  };
                })
            )
          } else {
            console.log('Error while creating database connection')
          }
        } else {
          console.log("Jobpost Id is not present.");
          return {
            statusCode: 400,
            body: JSON.stringify({
                errorCode: 'InvalidRequest',
                message: 'Invalid jobpost id or jobpost id does not exist.'
            })
          };
        } 
    } catch (err) {
      console.error('Error in the retrieveScreenerQuestionsEndpoint() function main catch block.', err);
      // destroy DB connection
      organizationDbConnection ? organizationDbConnection.destroy() : null
      // Handle the error response with a 500 status code
      return {
        statusCode: 500,
        body: JSON.stringify({
            errorCode: 'InternalServerError',
            message: 'An error occurred while processing the request.'
        })
      };
    }
  }

  function transformData(jsonData){
    try {
      const allowedTypes = ['textarea', 'text', 'select', 'checkbox-group', 'date', 'number', 'radio-group'];
      const transformedData = jsonData
      .filter(item => allowedTypes.includes(item.type))
      .map(item => {
        // Create a new object to store the transformed data
        let newItem = { ...item };
        // Change 'name' to 'id'
        if (newItem.hasOwnProperty('name')) {
            newItem.id = newItem.name;
            delete newItem.name;  // Remove the old 'name' key
        }
        // Change 'label' to 'question'
        if (newItem.hasOwnProperty('label')) {
            newItem.question = newItem.label;
            delete newItem.label;  // Remove the old 'label' key
        }
        // Change 'values' to 'options'
        if (newItem.hasOwnProperty('values')) {
          newItem.options = newItem.values.map(option => {
              let newOption = { ...option };
              delete newOption.selected; // Remove the 'selected' key from options
              return newOption;
          });
          delete newItem.values;  // Remove the old 'values' key
        }
          // Change 'type' value from 'checkbox-group' to 'multiselect'
          if (newItem.type.toLowerCase() === 'checkbox-group') {
            newItem.type = 'multiselect';
          }
          // Change 'type' value from 'radio-group' to 'select'
          if (newItem.type.toLowerCase() === 'radio-group') {
            newItem.type = 'select';
          }
          // If type is 'number', change it to 'text' and add 'format' as 'decimal'
          if (newItem.type.toLowerCase() === 'number') {
            newItem.type = 'text';
            newItem.format = 'decimal';
          }
          if (newItem.type.toLowerCase() === 'date') {
            newItem.format = 'dd/MM/yyyy';
          }
        // Reorder keys to make 'id' the first key
        if (newItem.hasOwnProperty('id')) {
          newItem = {
              id: newItem.id, // Place 'id' first
              ...newItem // Spread the rest of the properties
          };
        }
        // Remove extra keys
        delete newItem.className;
        delete newItem.access;
        delete newItem.toggle;
        delete newItem.inline;
        delete newItem.other;
        delete newItem.subtype;
        delete newItem.multiple;
        delete newItem.step;
        return newItem;
      });
      return transformedData;
    } catch (error) {
      throw error;
    }
  }