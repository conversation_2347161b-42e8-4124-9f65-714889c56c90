//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make database connection
const knex = require('knex');
//Require crypto for encryption
const crypto = require('crypto');

async function encryptInput(secretKey,textToEncrypt,encryptionAlgorithm){
    try{
        const key = Buffer.from(secretKey, 'base64'); // Convert secret key to buffer
        const cipher = crypto.createCipheriv(encryptionAlgorithm, key, null); // ECB mode does not require IV
    
        let encrypted = cipher.update(textToEncrypt, 'utf8', 'base64');
        encrypted += cipher.final('base64');
    
        return encrypted;
    }catch(error){
        console.log('Error in encryptInput function main catch block.',error);
        throw error;
    }
}
//Function to encrypt the keys
module.exports.encryptKeys = async (event, context) => {
    let encryptedString = '';
    try{
        console.log("Inside encryptKeys function");
        // parse event body
        let args = JSON.parse(event.body);
        let isBase64SecretKey = args.isBase64SecretKey;//if 1 then encryption secret key is base64 encoded
        let isInputEncoded = args.isInputEncoded;//if 1 then encrypted string is URL encoded
        let textToEncrypt = args.textToEncrypt;//text to encrypt
        let encryptionAlgorithm = args.encryptionAlgorithm;//algorithm used for encryption
        let encryptionSecretKeyName = args.encryptionSecretKeyName;//key name that is used in secrets to store encryption key
        if(encryptionAlgorithm === 'aes-256-ecb'){
            //Get the encryption secret key
            let secretDetails = await commonLib.func.getCredentials(process.env.region,process.env.dbSecretName);
            let secretKey = secretDetails[encryptionSecretKeyName];
            if(secretKey){
                if(isBase64SecretKey){
                    // Decode the Base64-encoded key
                    secretKey = Buffer.from(secretKey, 'base64');
                }
            
                let encryptedString = textToEncrypt;
                if(isInputEncoded){
                    let urlDecodedString = decodeURIComponent(encryptedString);
                    encryptedString = urlDecodedString;
                }console.log("encryptedString",encryptedString)
                
                encryptedString = await encryptInput(secretKey,encryptedString,encryptionAlgorithm);
                return(callbackResponse('',encryptedString));
            }else{
                console.log("Secret key does not exist.",secretKey,args);
                return(callbackResponse('SM0004',encryptedString));
            }
        }else{
            console.log('Invalid encryption algorithm',encryptionAlgorithm,args);
            return(callbackResponse('_DB0119',encryptedString));
        }
    }catch(mainCatchError){
        console.log('Error in the encryptKeys() main catch block.',mainCatchError);
        return(callbackResponse('_DB0120',encryptedString));
    }
}

function callbackResponse(errorCode,encryptedString){
     // Form and return response
     const response = {
        errorCode: 200,
        headers: {
            'Content-Type': '*/*',
            'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
            errorCode: errorCode,
            encryptedString: encryptedString
        })
    };
    return (response);
}