// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const { approvedPositionValidationSkip } = require('../../common/commonFunction');

module.exports.retrieveRecruitmentSettings = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, context.Employee_Id, '', '', 'UI', false, formId.reqruitmentRequest);
        if (Object.entries(checkRights).length > 0 && checkRights.Role_View === 1) {
            const recruitmentSettings = await approvedPositionValidationSkip(organizationDbConnection);
            return { errorCode: "", message: "Recruitment Settings retrieved successfully.", 
                warmBodiesIncludeNoticePeriod: recruitmentSettings ? recruitmentSettings.Warm_Bodies_Include_Notice_Period : 'No', 
                allowHireWithoutApprovedVacancy: recruitmentSettings ? recruitmentSettings.Allow_Hire_Without_Approved_Vacancy: 'No' };
        } else {
            throw '_DB0100';
        }
    } catch (err) {
        console.error('Error in retrieveRecruitmentSettings main catch block ', err);
        let errorResult = commonLib.func.getError(err, 'EI00183');
        throw new ApolloError(errorResult.message, errorResult.code);
    } finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}
