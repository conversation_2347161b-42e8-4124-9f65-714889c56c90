// Require common libraries and modules as before
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { validatCommonRuleInput} = require('../../common/commonFunction');
const { formId } = require('../../../common/appConstants');
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');

module.exports.updateExtIntNewPosition = async (parent, args, context) => {
  let validationError = {};
  let organizationDbConnection; 
  try {
    const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check access rights for the employee
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formId.newPosition
    );
    if (Object.keys(checkRights).length <= 0 || checkRights.Role_Update === 0) {
      throw '_DB0102';
    }

    // Destructure input arguments
    const {
      positionRequestId,
      internalOperatingNetwork,
      externalOperatingNetwork,
      licenseCertificate,
      licenseCertificateDetails,
      comments,
    } = args;

    // Field validation logic
    const fieldValidations = {};
   
    if (comments && comments.length) {
      fieldValidations.comments = 'IVE0496';
    }
    if(licenseCertificateDetails && licenseCertificateDetails.length){
      fieldValidations.licenseCertificateDetails= 'IVE0504';
    }

    // Validate input based on rules
    validationError = await validatCommonRuleInput(args, fieldValidations);

    if (internalOperatingNetwork && internalOperatingNetwork.length) {
      //fieldValidations.internalOperatingNetwork = 'IVE0494';
      internalOperatingNetwork.forEach(element => {
        const validation = validateWithRules(element, 'internalOperatingNetwork');
        if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
            validationError['internalOperatingNetwork'] = validation.length ? commonLib.func.getError('', "IVE0494").message : commonLib.func.getError('', "IVE0494").message1;
        }
      });
    }

    if (externalOperatingNetwork && externalOperatingNetwork.length) {
      // fieldValidations.externalOperatingNetwork = 'IVE0495';
      externalOperatingNetwork.forEach(element => {
        const validation = validateWithRules(element, 'externalOperatingNetwork');
        if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
            validationError['externalOperatingNetwork'] = validation.length ? commonLib.func.getError('', "IVE0495").message : commonLib.func.getError('', "IVE0495").message1;
        }
      });
    }

    if (Object.keys(validationError).length > 0) {
      throw 'IVE0000';
    }

    // Prepare the update data
    const updateData = {
      Internal_Operating_Network: internalOperatingNetwork ? JSON.stringify(internalOperatingNetwork) : null,
      External_Operating_Network: externalOperatingNetwork ? JSON.stringify(externalOperatingNetwork) : null,
      License_Certificate: licenseCertificate || null,
      License_Certificate_Details:licenseCertificateDetails || null,
      Comments: comments || null,
      Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
      Updated_By: loginEmployeeId,
    };
    // Start a transaction
    return await organizationDbConnection.transaction(async (trx) => {
      const updateResult = await organizationDbConnection(ehrTables.mppPositionRequest)
        .where('Position_Request_Id', positionRequestId)
        .transacting(trx)
        .update(updateData);

      if (!updateResult) throw 'EI00179';
      // Log activity
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Position details updated for request ${positionRequestId}`,
      });

      // Return success message
      return { errorCode: '', message: 'New Position & Additional Headcount details updated successfully' };
    });
  } catch (error) {
    console.log('Error in updateExtIntNewPosition function main catch block.', error);
    if (error === 'IVE0000') {
      console.log('Validation error in updateExtIntNewPosition function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      const errResult = commonLib.func.getError(error, 'EI00189');
      throw new ApolloError(errResult.message, errResult.code);
    }
  }
  finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};
