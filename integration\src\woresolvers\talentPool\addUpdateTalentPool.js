// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formId} = require('../../../common/appConstants');
const { validatCommonRuleInput } = require('../../common/commonFunction');
const moment = require('moment');

let organizationDbConnection;
module.exports.addUpdateTalentPool = async (parent, args, context, info) => {
    console.log('Inside addUpdateTalentPool function');
    let validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const talentPoolId = args.talentPoolId;
        // Validate inputs
         const fieldValidations = {};
        if (args.talentPool) {
            fieldValidations.talentPool = 'IVE0500'
        } else{
            validationError['IVE0000'] = 'Talent pool name is required';
            throw 'IVE0000';
        }
        validationError = await validatCommonRuleInput(args, fieldValidations)
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formId.talentPoolCandidate);
           if (Object.keys(checkRights).length > 0 && ((talentPoolId === 0 && checkRights.Role_Add === 1) || (talentPoolId > 0 && checkRights.Role_Update === 1))) {
                    if(Object.keys(validationError).length == 0){
                        return (
                            organizationDbConnection(ehrTables.talentPool)
                                .select('Talent_Pool_Id', 'Talent_Pool')
                                .modify(function (queryBuilder) {
                                    if (args.talentPoolId) {
                                        queryBuilder.where('Talent_Pool_Id','!=', args.talentPoolId)
                                    }
                                    if (args.talentPool) {
                                        queryBuilder.where(organizationDbConnection.raw("LOWER(REPLACE(Talent_Pool, ' ', ''))"), args.talentPool.toLowerCase().replace(/\s+/g, ''));
                                    }
                                })
                                .then((data) => {
                                    if (data && data.length == 0) {
                                        if(args.talentPoolId){
                                            return (
                                                organizationDbConnection(ehrTables.talentPool)
                                                .update({
                                                    Talent_Pool_Id: args.talentPoolId,
                                                    Talent_Pool: args.talentPool,
                                                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                    Updated_By: loginEmployeeId
                                                })
                                                .where('Talent_Pool_Id', args.talentPoolId)
                                                    .then(async(data) => {
                                                        if(data){
                                                            let systemLogParam = {
                                                                userIp: context.User_Ip,
                                                                employeeId: loginEmployeeId,
                                                                changedData: args,
                                                                organizationDbConnection: organizationDbConnection,
                                                                message: `Talent pool ${args.talentPool} of talent pool id ${args.talentPoolId} was updated by ${loginEmployeeId}`
                                                            };
                                                            await commonLib.func.createSystemLogActivities(systemLogParam);
                                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                            return { errorCode: "", message: "Talent pool details have been updated successfully." };
                                                        }
                                                        else {
                                                            throw 'TAP0103'
                                                        }
                                                    })
                                                    .catch((catchError) => {
                                                        //Destroy DB connection
                                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                        let errResult = commonLib.func.getError(catchError, 'TAP0104');
                                                        throw new ApolloError(errResult.message, errResult.code)
                
                                                    })
                                            )
                                        }
                                        else{
                                            return (
                                                organizationDbConnection(ehrTables.talentPool)
                                                    .insert({
                                                        Talent_Pool: args.talentPool,
                                                        Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                                        Added_By: loginEmployeeId
                                                    })
                                                    .then(async(data) => {
                                                        if(data){
                                                            let systemLogParam = {
                                                                userIp: context.User_Ip,
                                                                employeeId: loginEmployeeId,
                                                                changedData: args,
                                                                organizationDbConnection: organizationDbConnection,
                                                                message: `Talent pool ${args.talentPool} of talent pool id ${args.talentPoolId}  was added by ${loginEmployeeId}`
                                                            };
                                                            await commonLib.func.createSystemLogActivities(systemLogParam);
                                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                            return { errorCode: "", message: "Talent pool details have been added successfully." };
                                                        }
                                                        else {
                                                            throw 'TAP0105'
                                                        }
                                                    })
                                                    .catch((catchError) => {
                                                        //Destroy DB connection
                                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                                        let errResult = commonLib.func.getError(catchError, 'TAP0106');
                                                        throw new ApolloError(errResult.message, errResult.code)
                                                    })
                                            ) 
                                        }
                                    } else {
                                        throw 'TAP0113'
                                    }
                                })
                                .catch((catchError) => {
                                    console.log('Error in addUpdateTalentPool .catch() block', catchError);
                                    let errorCode = typeof catchError === 'string' ? catchError : '';
                                    let errResult = commonLib.func.getError(errorCode, 'TAP0107');
                                    //Destroy DB connection
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    //Return error response
                                    throw new ApolloError(errResult.message, errResult.code);
                                })
                        )
                    }
                    else{
                        console.log('Invalid input request', validationError);
                        throw 'IVE0000';
                    }
            }
            else {
                if (talentPoolId) {
                    console.log("The employee does not have edit access.");
                    throw '_DB0102';
                } else {
                    console.log("The employee does not have add access.");
                    throw '_DB0101';
                }
            }
    }
    catch (mainCatchError) {
        console.log('Error in addUpdateTalentPool function main catch block.', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addUpdateTalentPool function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        else{
            let errResult = commonLib.func.getError(mainCatchError, 'TAP0005');
            throw new ApolloError(errResult.message, errResult.code)
        }
       
    }
}