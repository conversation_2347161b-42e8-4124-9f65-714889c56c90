// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');

module.exports.updateWorkflowApprovalSetting = async (parent, args, context) => {
    console.log("Inside updateWorkflowApprovalSetting function.");
    let organizationDbConnection;
    
    try {
        const { Employee_Id: employeeId } = context;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check update rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            employeeId,
            '', 
            '',
            'UI',
            false,
            args.formId
        );

        if (!checkRights || checkRights.Role_Update !== 1) {
            throw '_DB0102';
        }

        // Get existing settings
        const existingSettings = await organizationDbConnection(ehrTables.mppForecastSettings)
            .first()
            .select('Forecast_Settings_Id', 'Enable_Workflow_Approval');

        if (!existingSettings) {
            throw '_DB0103';
        }

        // Update the record
        await organizationDbConnection(ehrTables.mppForecastSettings)
            .where('Forecast_Settings_Id', existingSettings.Forecast_Settings_Id)
            .update({
                Enable_Workflow_Approval: args.enableWorkflowApproval,
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                Updated_By: employeeId
            });

        return {
            errorCode: "",
            message: "Workflow approval setting updated successfully"
        };

    } catch (err) {
        console.error('Error in updateWorkflowApprovalSetting:', err);
        const errResult = commonLib.func.getError(err, 'EI00229');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        organizationDbConnection?.destroy();
    }
};
