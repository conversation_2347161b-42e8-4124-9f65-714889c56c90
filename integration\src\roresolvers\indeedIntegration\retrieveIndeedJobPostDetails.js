// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formId } = require('../../../common/appConstants');

// const formatMinMaxDate = (date) => moment(date).format('YYYY-MM-DD');
const retrieveJobPostData = async (organizationDbConnection, jobPostId) => {
    return organizationDbConnection(ehrTables.jobPostIndeedIntegration)
    .select('Integration_Id as integrationId', 'Job_Post_Id as jobPostId', 'Dynamic_Form_Id as dynamicFormId', 'Benefits as benefits', 
    'Contact_Type as contactType', 'Contact_Email as contactEmail', 'Contact_Phone as contactPhone', 
    'Contact_Name as contactName', 'Employer_Job_Id as employerJobId', 'Status as status', 'City_Id as cityId', 'City_Name as cityName', 
    'State_Id as stateId', 'State_Name as stateName', 'Workplace_Type as workplaceType')
    .where('Job_Post_Id', jobPostId);
};

module.exports.retrieveIndeedJobPostDetails = async (parent, args, context, info) => {
    console.log('Inside retrieveIndeedJobPostDetails function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formId.jobpost);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const jobPostDetails = await retrieveJobPostData(organizationDbConnection, args.jobPostId);

            // Destroy DB connection
            organizationDbConnection && organizationDbConnection.destroy();
            return {
                errorCode: '',
                message: 'Indeed jobpost details have been retrieved successfully.',
                data: jobPostDetails && jobPostDetails.length? jobPostDetails: []
            };
        } else {
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection && organizationDbConnection.destroy();
        console.log('Error in retrieveIndeedJobPostDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0014');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
