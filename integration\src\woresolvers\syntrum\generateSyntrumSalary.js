// require common table alias
const { ehrTables } = require('../../../common/tablealias');
const { getSyntrumAuthToken } = require('./getSyntrumAuthToken');

module.exports.generateSyntrumSalary = async (organizationDbConnection, context) => {
    console.log('Inside generateSyntrumSalary function started');
    try {

        let employeeId = context.Employee_Id;

        const employeeJob = await organizationDbConnection(ehrTables.empJob).select('User_Defined_EmpId')
            .where('Employee_Id', employeeId).first();

        const params = {
            EmpCode: "" + employeeJob.User_Defined_EmpId,
            Status: "APPROVED"
        }

        let authAndUrlDetails = await getSyntrumAuthToken(organizationDbConnection);
        const response = await handleIntegrationRequest(params, authAndUrlDetails)
        console.log('Inside generateSyntrumSalary response => ', JSON.stringify(response));
        return { errorCode: '', message: 'Syntrum salary information retrieved successfully', data: JSON.stringify(response) };

    } catch (e) {
        console.error('Error in generateSyntrumSalary function main catch block.', e);
        throw e
    }
}

async function handleIntegrationRequest(inputDetails, authAndUrlDetails) {
    try {
        const axios = require('axios');

        let { apiUrl, authToken } = authAndUrlDetails;

        if (!authToken) {
            throw 'SYN0101';
        }

        const config = {
            method: 'post',
            url: apiUrl + '/employee.getSalaryInformation',
            maxBodyLength: Infinity,
            headers: {
                'Content-Type': 'application/json',
                Authorization: 'Bearer ' + authToken,
                "Cookie": `refreshToken=${authToken}`
            },
            data: JSON.stringify({
                'json': inputDetails
            }),
            timeout: 20000
        };
        console.log('Inside generateSyntrumSalary input request => ', config);
        const { data: response } = await axios.request(config);
        return response;
    } catch (error) {
        console.error('Error in handleIntegrationRequest function main catch block', error?.response?.data || error?.response);
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
            throw 'SYN0108'
        }
        if (error?.response && error?.response?.data) {
            return error.response.data;
        }
        throw 'SYN0019'
    }
}