const { CommonLib } = require("@cksiva09/hrapp-corelib");
const { asyncIndeedWebHookFunction } = require("../sfapiIntegration/asyncIndeedWebHookFunction");
// require common function
const commonFunction = require('../../common/initiateStepFunction');

module.exports.indeedWebHookEndpoint = async (event, context) => {
  try {
      // Log the incoming event for debugging
      const applicationData = JSON.parse(event.body);
      console.log("inside indeedWebHookEndpoint", applicationData);

      // Validate the event and its body
      if (!applicationData || Object.keys(applicationData).length === 0) {
          console.log('Error: Invalid event or empty body in indeedWebHookEndpoint function.');
          return {
              statusCode: 400,
              body: JSON.stringify( {
                  errorCode: 'InvalidRequest',
                  message: 'Invalid event or empty request body.'
              })
          };
      }

      //Validate the request signature
      let verificationValue=await validateRequestSignature(event, applicationData);
      if(!verificationValue){
        console.log("not valid signature",verificationValue)
        return {
          statusCode: 401,
          body: JSON.stringify( {
              errorCode: 'InvalidRequest',
              message: 'Missing or Invalid X-Indeed-Signature value'
          })
      };
      }

      // Prepare arguments for the asyncIndeedWebHookFunction function
      let args = { response: applicationData };
      await asyncIndeedWebHookFunction(args);

      // Return a 200 status code with an empty body as required by the webhook documentation
      return {
          statusCode: 200,
          body: JSON.stringify({
            errorCode: '',
            message: 'Candidate details added successfully.',
            validationError: null,
          })
      };
  } catch (error) {
      console.log('Error in indeedWebHookEndpoint function in main catch block.', error, error.code);
      if(error === 'InvalidJobpost'){
        return {
            statusCode: 404,
            body: JSON.stringify( {
                errorCode: error,
                message: 'Job is invalid, does not exist.'
            })
        };
      }
      if(error === 'JobPostClosed'){
        return {
            statusCode: 410,
            body: JSON.stringify( {
                errorCode: error,
                message: 'The job is expired or no longer available'
            })
        };
      }

      else if(error?.code === 'ER_DUP_ENTRY'){
        return {
            statusCode: 409,
            body: JSON.stringify( {
                errorCode: error.code,
                message: 'Duplicate Application already in the system.'
            })
        };
      }

      // Handle the error response with a 500 status code
      else{
        return {
            statusCode: 500,
            body: JSON.stringify( {
                errorCode: 'InternalServerError',
                message: 'An error occurred while processing the request.'
            })
        };
     }
  }
};

const validateRequestSignature = async (event, applicationData) => {
    // Dependencies
    const xIndeedSignature = event.headers['X-Indeed-Signature'];
    const crypto = require('crypto');
    const AWS = require('aws-sdk');
            
    // Create client for secrets manager
    let client = new AWS.SecretsManager({
        region: process.env.region
    });
    // Get secrets from aws secrets manager
    let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
    secretKeys = JSON.parse(secretKeys.SecretString);
    const secret = secretKeys.indeedia_clientsecret;
    const data = JSON.stringify(applicationData);
    // Encode data in base64
    const encodedStr = new Buffer.from(data).toString('base64');

    // Compute the hash
    const signature = crypto.createHmac('SHA1', secret).update(encodedStr, 'base64').digest('base64');

    // Check the computed signature against the X-Indeed-Signature 
    // provided in the request_headers of your application
    if (signature !== xIndeedSignature) {
        console.log(`invalid signature: ${signature}`, `\nX-Indeed-Signature: ${xIndeedSignature}`);
        return false;
    } else {
        console.log(`Computed signature matches: ${signature}`);
        return true;
    }
  };
  