//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const {formName} = require('../../common/appConstants');

let organizationDbConnection;
module.exports.updateIrukkaJobPostStatus = async (parent, args, context, info) => {
    try {
        console.log("Inside updateIrukkaJobPostStatus function.")
        organizationDbConnection = knex(context.connection.OrganizationDb);
        
        if (args.jobPostStatus == "Pending" || args.jobPostStatus == "Approved"|| args.jobPostStatus == "Rejected") {
            return (
                await organizationDbConnection(ehrTables.jobPost)
                .update({
                    Irukka_Job_Post_Status:args.jobPostStatus,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                }).where('Job_Post_Id', args.jobPostId)
                .then((data) => {
                    if (data) {
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Irukka IrukkaJobPostStatus has been updated successfully." };
                    } else {
                        throw 'IN0101'
                    }
                })
                .catch((catchError) => {
                    console.log('Error in IrukkaJobPostStatus .catch() block', catchError);
                    let errResult = commonLib.func.getError(catchError, 'IN0102');
                    //Destroy DB connection
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    //Return error response
                    throw new ApolloError(errResult.message, errResult.code);
                })

            )
        }
        else{
            console.log('Please provide valid jobpost status');
            throw 'IN0103';
        }

    }
    catch (e) {
        //Destroy DB connection
        console.log('Error in updateIrukkaJobPostStatus function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'IN0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
