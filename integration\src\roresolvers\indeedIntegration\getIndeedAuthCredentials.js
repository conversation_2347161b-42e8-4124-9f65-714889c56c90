// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const {formId} = require('../../../common/appConstants')


module.exports.getIndeedAuthCredentials = async (parent, args, context, info) => {
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false,  formId.jobpost);
        if(Object.keys(checkRightsForForm).length > 0  && ((checkRightsForForm.Role_Add === 1) || (checkRightsForForm.Role_Update === 1))){
            let authCredentials = {};
            const AWS = require('aws-sdk');
            
            // Create client for secrets manager
            let client = new AWS.SecretsManager({
                region: process.env.region
            });
            // Get secrets from aws secrets manager
            let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
            secretKeys = JSON.parse(secretKeys.SecretString);

            authCredentials.clientId = secretKeys.indeed_clientid;
            authCredentials.secretKey = secretKeys.indeed_clientsecret;
            authCredentials.indeedApplyToken = secretKeys.indeedia_clientid;
            //Destroy DB connection
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return{ message: "Indeed auth credentials retrieved successfully.", data:authCredentials}
        }
        else {
            console.log('This employee do not have add or edit access rights');
            throw '_DB0111';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getIndeedAuthCredentials function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0011');
        throw new ApolloError(errResult.message, errResult.code);
    }
}