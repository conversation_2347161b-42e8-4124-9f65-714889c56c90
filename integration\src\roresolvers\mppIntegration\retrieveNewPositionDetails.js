// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');

module.exports.retrieveNewPositionDetails = async (parent, args, context, info) => {

    console.log("Inside retrieveNewPositionDetails function.")
    let organizationDbConnection;

    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

       let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId.newPosition);

      if (Object.entries(checkRights).length > 0 && checkRights.Role_View === 1) {
            const positionRequestQuery = organizationDbConnection(ehrTables.mppPositionRequest + ' as MPR')
                .select(
                    'MPR.*',
                    'SFWP.Pos_Code',
                    'WO.Event_Id',
                    'ET.Employee_Type AS Employee_Type_Name',
                    'SFWP1.Pos_Name as Group_Name',
                    'SFWP2.Pos_Name as Division_Name',
                    'SFWP3.Pos_Name as Department_Name',
                    'SFWP4.Pos_Name as Section_Name',
                    'PL.Position_Level',
                    'PL.Position_Level_Id',
                    'MPR.Reason_For_Replacement',
                    "CEG.Group_Name as CustomGroupName",
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as approvedByName"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By"),
                    organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By")
                )
                .leftJoin( ehrTables.positionLevel + ' as PL', 'MPR.Position_Level', 'PL.Position_Level_Id')
                .leftJoin(ehrTables.employeeType + " as ET",'MPR.Employee_Type','ET.EmpType_Id')
                .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "MPR.Added_By")
                .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "MPR.Updated_By")
                .leftJoin(ehrTables.empPersonalInfo + " as EPI3", 'EPI3.Employee_Id', 'MPR.Approver_Id')
                .leftJoin(ehrTables.workflows + " as WO",'WO.Workflow_Id','MPR.Workflow_Id')
                .leftJoin('SFWP_Organization_Structure as SFWP', 'MPR.Organization_Structure_Id', 'SFWP.Organization_Structure_Id')
                .leftJoin('SFWP_Organization_Structure as SFWP1', 'MPR.Group_Code', 'SFWP1.Pos_Code')
                .leftJoin('SFWP_Organization_Structure as SFWP2', 'MPR.Division_Code', 'SFWP2.Pos_Code')
                .leftJoin('SFWP_Organization_Structure as SFWP3', 'MPR.Department_Code', 'SFWP3.Pos_Code')
                .leftJoin('SFWP_Organization_Structure as SFWP4', 'MPR.Section_Code', 'SFWP4.Pos_Code')
                .leftJoin(
                    "custom_employee_group" + " as CEG",
                    "CEG.Group_Id",
                    "MPR.Custom_Group_Id"
                  )
                .where('MPR.Position_Request_Id', args.positionRequestId);

            const experienceQuery = organizationDbConnection(ehrTables.mppExperience + ' as ME')
                .select(
                    'ME.Experience_Id',
                    'ME.Type_Of_Jobs',
                    'ME.Months',
                    'ME.Years',
                    'ME.Position_Request_Id'
                )
                .where('ME.Position_Request_Id', args.positionRequestId);
                const educationQuery = organizationDbConnection(ehrTables.mppEducationRequirementsDescriptions+ " as MERS")
                .innerJoin(ehrTables.mppEducationRequirements+ " as MER",'MERS.Mpp_Education_Requirements_Id','MER.Mpp_Education_Requirements_Id')
                .select(
                    'MERS.Mpp_Education_Requirements_Descriptions_Id',
                   'MERS.Description',
                   'MER.Education_Type',
                    'MERS.Position_Request_Id'
                )
                .where('MERS.Position_Request_Id', args.positionRequestId);

            const workingConditionsQuery = organizationDbConnection(ehrTables.mppWorkingConditions + ' as MWC')
                .select(
                    'MWC.Working_Condition_Id',
                    'MWC.Working_Area',
                    'MWC.Time_Spent',
                    'MWC.Position_Request_Id'
                )
                .where('MWC.Position_Request_Id', args.positionRequestId);

            const dutiesResponsibilitiesQuery = organizationDbConnection(ehrTables.mppDutiesResponsibilities + ' as MDR')
                .select(
                    'MDR.Competency',
                    'MDR.Duties_Responsibility_Id',
                    'MDR.Position_Request_Id',
                    'MDR.Regular_Duties',
                    'MDR.No_Of_Hours_Period',
                    'MDR.Period',
                    'MDR.Competencies_Required',
                    'MDR.Rating_Of_Competency'
                )
                .where('MDR.Position_Request_Id', args.positionRequestId);

            let [positionRequestDetails, experienceDetails, workingConditionsDetails, dutiesResponsibilitiesDetails,educationDetails] = await Promise.all([
                positionRequestQuery,
                experienceQuery,
                workingConditionsQuery,
                dutiesResponsibilitiesQuery,
                educationQuery
            ]);

            if(positionRequestDetails && positionRequestDetails.length > 0){
                positionRequestDetails[0].Internal_Operating_Network = positionRequestDetails[0]?.Internal_Operating_Network ? JSON.parse(positionRequestDetails[0].Internal_Operating_Network) : [];
                positionRequestDetails[0].External_Operating_Network = positionRequestDetails[0]?.External_Operating_Network ? JSON.parse(positionRequestDetails[0].External_Operating_Network) : [];
            }

            const result = {
                ...positionRequestDetails[0],
                Experience: experienceDetails,
                WorkingConditions: workingConditionsDetails,
                DutiesResponsibilities: dutiesResponsibilitiesDetails,
                education:educationDetails
            };
            // Now `result` contains the structured data

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "New Position & Additional Headcount details retrieved successfully.", openPositiontRequestRetrieveDetails: result };

        } else {
            throw '_DB0100';
        }

    } catch (err) {
        //Destroy DB connection
        console.error('Error in retrieveNewPositionDetails function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'EI00182');
        throw new ApolloError(errResult.message, errResult.code);
    }
}