//get common functions
const{getConnection, updateInMasterTable}=require("./commonFunctions");
//get tablealias
const{defaultValues}=require("../../common/appConstants")
// Organization database connection
const knex = require('knex');
//require tablealias
const{appManagerTables,ehrTables}=require("../../common/tablealias")
//Require axios
const axios = require('axios');
//Require moment
const moment = require('moment-timezone');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

//variable declaration
let appmanagerDbConnection;
let organizationDbConnection;
let updateParams;
let masterTable=appManagerTables.resignationManager;
let status="Open";

// Function to get the active subscribed user to call camu exit api.
module.exports.processCamuResignation  = async(event,context) =>{
    try{
        let inputStatus=event.status;
        if(inputStatus && inputStatus.toLowerCase()==='failed')
        {
            status="Failed";
        }
        let databaseConnection=await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
        // check whether connection exist or not
        if(Object.keys(databaseConnection).length){
            // get app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
            let activeInstances= await getOrgCodeFromMasterTable(appmanagerDbConnection,appManagerTables.resignationManager,"Complete_Status",status);
            if(activeInstances)
            {
                if(activeInstances.length>0)
                {
                    let instanceToBeProcessed=(activeInstances.length>defaultValues.activeInstanceToBeProcessed)?(defaultValues.activeInstanceToBeProcessed):(activeInstances.length);
                    for(let i=0;i<instanceToBeProcessed;i++)
                    {
                        let orgCode=activeInstances[i];
                        
                        let connection=await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode);
                        if(Object.keys(connection).length)
                        {
                            organizationDbConnection=knex(connection.OrganizationDb);
                            let inactiveEmployees= await getEmployeeFromOrgTable(organizationDbConnection,status);
                            if(inactiveEmployees)
                            {
                                if(inactiveEmployees.length>0)
                                {
                                    let camuIntegrationDetails=await commonLib.employees.getCamuClientToken(appmanagerDbConnection,orgCode,organizationDbConnection,inactiveEmployees);
                                    let camuToken;
                                    let camuBaseUrl;
                                    let camuExitStaffEndPoint;
                                    if(camuIntegrationDetails && Object.keys(camuIntegrationDetails).length>0)
                                    {
                                        if(camuIntegrationDetails.fieldForceEnabled == 1){
                                            camuToken = '';
                                        }else{
                                            camuToken=camuIntegrationDetails['empCamuDetails']['Client_Access_Token'];
                                            camuBaseUrl = camuIntegrationDetails['empCamuDetails']['Camu_Base_Url'];
                                            camuExitStaffEndPoint=camuBaseUrl+process.env.camuExitStaffEndPoint;
                                        }
                                        console.log(camuToken,camuBaseUrl);
                                    }
                                    
                                    let checkFailed=false;
                                    for(let j=0;j<inactiveEmployees.length;j++)
                                    {
                                        if(camuIntegrationDetails.fieldForceEnabled == 1){
                                            camuToken='';
                                            camuExitStaffEndPoint='';
                                            camuBaseUrl = '';
                                            let employeeCamuDetails = camuDetails['empCamuDetails'][inactiveEmployees[j]];
                                            console.log('employeeCamuDetails',employeeCamuDetails);
                                            if(employeeCamuDetails && employeeCamuDetails.length){
                                                camuToken = employeeCamuDetails[0]['Client_Access_Token'];
                                                camuBaseUrl = employeeCamuDetails[0]['Camu_Base_Url'];
                                                camuExitStaffEndPoint=camuBaseUrl+process.env.camuCreateStaffEndPoint;
                                            }
                                        }

                                        let employeeCamuData= await commonLib.employees.getCamuResignationEmployeeInfo(organizationDbConnection,inactiveEmployees[j]);
                                        if(employeeCamuData)
                                        {
                                            let exitCallResponse=await commonLib.employees.callCamuExitStaffApi(employeeCamuData[0],camuExitStaffEndPoint,camuToken);
                                            if(exitCallResponse)
                                            {
                                                updateParams={
                                                    Status : 'Success'
                                                }
                                                await updateInCamuResignationTable(organizationDbConnection,updateParams,inactiveEmployees[j])
                                            }
                                            else{
                                                checkFailed=true;
                                                updateParams={
                                                    Status : 'Failed'
                                                }
                                                await updateInCamuResignationTable(organizationDbConnection,updateParams,inactiveEmployees[j])
                                            }
                                        } 
                                        else
                                        {
                                            checkFailed=true;
                                        }
                                    }
                                    if(checkFailed)
                                    {
                                        updateParams={
                                            Complete_Status : 'Failed'
                                        }
                                        await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                    }
                                    else
                                    {
                                        updateParams={
                                            Complete_Status : 'Success'
                                        }
                                        await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                    }
                                }
                                else{
                                    updateParams={
                                        Complete_Status : 'Success'
                                    }
                                    await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                                }
                            }
                            else
                            {
                                updateParams={
                                    Complete_Status : 'Failed'
                                }
                                await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable,orgCode);
                            }
                            organizationDbConnection?organizationDbConnection.destroy():null;
                        }
                        else{
                            updateParams={
                                Complete_Status : 'Failed'
                            }
                            await updateInMasterTable(appmanagerDbConnection,updateParams,masterTable);
                        }
                    }
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Execution Completed.'
                    };
                    return response;   
                }
                else{
                    console.log('No open record found in resignation manager table. So quit the execution');
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response = 
                    {
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'No open instances found.'
                    };
                    return response;
                }
            }
            else{
                console.log('Error while getting open instances.');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response = 
                {
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Error while getting open instances in step3.'
                };
                return response;
            }
         }
         else{
            console.log('Error while creating app manager database connection in step3');
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
            let response = 
            {
                nextStep:'End',
                input:{'status':inputStatus},
                message:'Error in creating app manager database connection in step3.'
            };
            return response;
         }
    }
    catch(e)
    {
        console.log("Error in processCamuResignation function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        organizationDbConnection?organizationDbConnection.destroy():null; 
        let response ={
            nextStep:'End',
            input:{'status':"Failed"},
            message:'Error Occured in processCamuResignation.'
        };
        return response;
    }
}

async function getOrgCodeFromMasterTable(appmanagerDbConnection,table,columnName,status)
{
    try{
        return(
            appmanagerDbConnection(table)
            .pluck('Org_Code')
            .where(columnName,status)
            .then(orgCode=>{
                return orgCode;
            })
            .catch(e=>{
                console.log('Error in getOpenOrgCodeFromMasterTable function .catch block.', e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log('Error in getOpenOrgCodeFromMasterTable function main catch block.', e);
        return false;
    }
}

async function getEmployeeFromOrgTable(organizationDbConnection,status)
{
    try{
        return(
            organizationDbConnection(ehrTables.camuEmployeeResignationManager)
            .pluck('Employee_Id')
            .where('Status',status)
            .then(empId=>{
                return empId;
            })
            .catch(e=>{
                console.log('Error in getOpenEmployeeFromOrgTable function .catch block.', e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log('Error in getOpenEmployeeFromOrgTable function main catch block.', e);
        return false;
    }
}

 // function to update in camu_employee_resignation_manager`
 async function updateInCamuResignationTable(organizationDbConnection,updateParams,employeeId){
    try{
        console.log('Inside updateInCamuResignationTable function');
        return(
            organizationDbConnection(ehrTables.camuEmployeeResignationManager)
            .update(updateParams)
            .where('Employee_Id',employeeId)
            .then((updatedData) => {
                return true;
            })
            .catch(catchError=>{
                console.log('Error in updateInCamuResignationTable .catch block.', catchError);
                return false;
            })
        );
    }
    catch(error){
        console.log('Error in updateInCamuResignationTable function main catch block',error);
        return false;
    }    
}