const { CommonLib } = require("@cksiva09/hrapp-corelib");
const axios = require('axios');
const moment = require('moment');
const { ehrTables } = require('../../../common/tablealias');
const { getUserData, getJobStreetAccessToken, mapErrorToCustomCodeSeek, formBodyForEmail, getRecruiterEmailAddress } = require("../../common/commonFunction");
const { positionProfile } = require("../../queries/jobStreetQueries");
const { getConnection } = require("../../stepFunction/commonFunctions");
const AWS = require('aws-sdk');
//Require knex to make DB connection
const knex = require('knex');

module.exports.asyncJobStreetCloseWebHookFunction = async (args) => {
    try{
        console.log('Inside asyncJobStreetCloseWebHookFunction function started ', args)
        let organizationDbConnection;
      if (
        args &&
        args.status.response
      ) {
        let eventsjson = JSON.parse(args.status.response)
          if (eventsjson.events && eventsjson.events.length) {
            console.log(eventsjson, 'eventsjson');
            const GRAPHQL_ENDPOINT = process.env.jobStreetTokenAPI;
      
            for (const element of eventsjson.events) {
                console.log(element,"element")
              const positionProfileId = element.positionProfileId;
      
              try {
                const { SeekClientID, SeekClientSecret } = await getUserData();
                const ACCESS_TOKEN = await getJobStreetAccessToken(SeekClientID, SeekClientSecret);
      
                const response = await fetchPositionProfile(GRAPHQL_ENDPOINT, ACCESS_TOKEN, positionProfileId);
                console.log(response,"response../")
                if (response.errors && response.errors.length) {
                  handleErrorResponse(response.errors);
                }
                let { orgCode, jobPostId } = parsePositionProfile(response.data.positionProfile);
                organizationDbConnection = await createDbConnection(orgCode);
                if (!organizationDbConnection) {
                  return createErrorResponse(500, 'InternalServerError', 'Error while creating database connection.');
                }
      
                await updateProfileAndJobOpening(organizationDbConnection, positionProfileId,jobPostId,orgCode);
              } catch (error) {
                console.error('Error in processing event:', error);
              }
            }
          }
          organizationDbConnection.destroy();
          return createSuccessResponse();
        }
        } catch (error) {
          console.error('Error in jobStreetCloseWebhook function:', error);
          return createErrorResponse(500, 'InternalServerError', 'An error occurred while processing the request.');
        };
      
      
      async function fetchPositionProfile(endpoint, accessToken, positionProfileId) {
        console.log("inside fetchPositionProfile")
        const config = {
          method: 'post',
          url: endpoint,
          maxBodyLength: Infinity,
          data: JSON.stringify({
            query: positionProfile,
            variables: { id: positionProfileId }
          }),
          headers: {
            Authorization: `Bearer ${accessToken.access_token}`,
            'Content-Type': 'application/json',
            'User-Agent': 'YourPartnerService/1.2.3'
          }
        };
        const response = await axios.request(config);
        console.log(response.data, 'actualresponse', response.data.positionProfile);
        return response.data;
      }
      
      function parsePositionProfile(positionProfile) {
        if (!positionProfile || !positionProfile.seekHirerJobReference) {
          throw 'EI00169';
        }
      
        const seekHirerJobReferenceArray = positionProfile.seekHirerJobReference;
        const orgCode = seekHirerJobReferenceArray.split('-')[0];
        const jobPostId = seekHirerJobReferenceArray.split('-')[1];
      
        return { orgCode, jobPostId };
      }
      
      async function createDbConnection(orgCode) {
        const databaseConnection = await getConnection(
          process.env.stageName,
          process.env.dbPrefix,
          process.env.dbSecretName,
          process.env.region,
          orgCode
        );
      
        if (databaseConnection && Object.keys(databaseConnection).length) {
          return knex(databaseConnection.OrganizationDb);
        } else {
          console.log('Error while creating database connection');
          return null;
        }
      }
      
      async function updateProfileAndJobOpening(dbConnection, positionProfileId,jobPostId,orgCode) {
        try {
        const positionProfileUpdateObject = {
          Applied_Status: "Closed",
          Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
          Updated_By: 1
        };
      
        return await dbConnection.transaction(async (trx) => {
            await dbConnection(ehrTables.jobStreetJobProfile)
              .update(positionProfileUpdateObject)
              .where('Profile_Id', positionProfileId)
              .transacting(trx);
              const updatedValue = await dbConnection(ehrTables.jobStreetJobProfile)
              .where('Profile_Id', positionProfileId)
              .transacting(trx)
      
              .select('Job_Street_Id') // Replace 'column_name' with the actual column you want to retrieve
              .first();
            if (!updatedValue || !updatedValue.Job_Street_Id) {
              throw 'EI00170';
            }
      
            await dbConnection(ehrTables.jobStreetJobOpenings)
              .update(positionProfileUpdateObject)
              .where('Job_Street_Id', updatedValue.Job_Street_Id)
              .transacting(trx);
      
              let jobPostDetails= await getClosedJobPostDetails(jobPostId,dbConnection,trx);
                if(!jobPostDetails || !jobPostDetails.length){
                  console.log("job post details not found:",jobPostId)
                }
                else{
                  let jobPostLink="https://" + orgCode + "." + process.env.domainName + process.env.webAddress + "/v3/recruitment/job-posts";
                let CcAddresses=await getRecruiterEmailAddress(dbConnection,jobPostId,trx)
                let notificationParams=await formBodyForEmail(CcAddresses,dbConnection,trx,orgCode,jobPostDetails[0],jobPostId,jobPostLink)
                const ses = new AWS.SES({ region: process.env.sesRegion });   
                let emailResponse = await ses.sendTemplatedEmail(notificationParams).promise();
                console.log(emailResponse,"emailResponse")
                console.log(notificationParams,"notificationParams")
                if(!emailResponse){
                  console.log('email not sent due to some issue')
                }
              }
              
            return true;
        })
      }catch(err){
        console.log(err)
      }
      }
      async function getClosedJobPostDetails(jobPostId,dbConnection,trx){
        try{
        let value =await dbConnection(ehrTables.jobPost+ ' as JP')
        .where('JP.Job_Post_Id', jobPostId)
        .select('EJ.Emp_Email',
          dbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Job_Post_Owner_Name"),
          'JP.Job_Post_Name'
        
        )
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI','JP.Added_By','EPI.Employee_Id')
        .leftJoin('emp_job'+' as EJ','EJ.Employee_Id','JP.Added_By')
        .whereNotNull('EJ.Emp_Email')
        .whereNot('EJ.Emp_Email',"");
      
        console.log(value,"value");
        return value;
        }
        catch(err){
          throw err;
        }
      }
      async function handleErrorResponse(errors) {
        const errorCode = await mapErrorToCustomCodeSeek(errors);
        if (errorCode === 'CH0001') {
          console.log('Unauthenticated request, check the tokens');
        } else if (errorCode === 'CH0003') {
          console.log('Validation error: bad_input');
        }
      
        throw 'EI00152';
      }
      
      function createErrorResponse(statusCode, errorCode, message) {
        return {
          statusCode,
          body: {
            errorCode,
            message
          }
        };
      }
      
      function createSuccessResponse() {
        return {
          statusCode: 200,
          body: null
        };
      }
      
      async function securelyRetrieveSecret() {
        return await getWebHookSecret();
      }
      

}