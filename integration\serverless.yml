service: INTEGRATION # service name

plugins:
  - serverless-domain-manager
  - serverless-prune-plugin # Plugin to maintain lambda versioning
  - serverless-offline
  - serverless-step-functions
  # - '@haftahave/serverless-ses-template' # Plug-In to deploy SES templates

provider:
  name: aws
  runtime: nodejs18.x #nodejs run time
  stage: ${opt:stage} # get current stage name
  region: ${opt:region} #region in which to be deployed
  role: ${file(config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
  vpc:
    securityGroupIds: ${file(./config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(./config.${self:provider.stage}.json):subnetIds}
custom:
  sesTemplatesConfigFile: './corehr-ses-email-templates/index.js'
  sesTemplatesRegion: ${file(./config.${self:provider.stage}.json):sesTemplatesRegion}
  customDomain:
    domainName: ${file(./config.${self:provider.stage}.json):customDomainName}
    basePath: 'integration'
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: 'edge'
  prune:
    automatic: true
    number: 3

# Lambda functions
functions:
  jobStreetWebHookEndpoint:
    handler: src/woresolvers/jobStreetIntegrationMutation/jobStreetWebHookEndpoint.jobStreetWebHookEndpoint
    events:
        - http:
            path: jobStreetWebHookEndpoint
            method: post
            cors: true
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      region: ${self:provider.region}
      jobStreetAuthTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthTokenAPI}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      jobStreetAuthBrowserTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthBrowserTokenAPI}
      jobStreetTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetTokenAPI}
      asyncJobStreetWebHookFunction: ${file(config.${self:provider.stage}.json):asyncJobStreetWebHookFunction}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}

  indeedWebHookEndpoint:
    handler: src/woresolvers/indeedIntegration/indeedWebHookEndpoint.indeedWebHookEndpoint
    events:
        - http:
            path: indeedWebHookEndpoint
            method: post
            cors: true
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      region: ${self:provider.region}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      atsNameForIndeed: ${file(config.${self:provider.stage}.json):atsNameForIndeed}

  indeedApplyQuestions:
    handler: src/roresolvers/indeedIntegration/retrieveScreenerQuestionsEndpoint.retrieveScreenerQuestionsEndpoint
    events:
        - http:
            path: indeedApplyQuestions
            method: get
            cors: true
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      region: ${self:provider.region}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}

  jobStreetCloseWebhook:
    handler: src/woresolvers/jobStreetIntegrationMutation/jobStreetCloseWebhook.jobStreetCloseWebhook
    events:
        - http:
            path: jobStreetCloseWebhook
            method: post
            cors: true
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      region: ${self:provider.region}
      jobStreetAuthTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthTokenAPI}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      jobStreetAuthBrowserTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthBrowserTokenAPI}
      jobStreetTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetTokenAPI}
      asyncJobStreetCloseWebHookFunction: ${file(config.${self:provider.stage}.json):asyncJobStreetCloseWebHookFunction}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}

  rographql:
    handler: src/rohandler.graphql
    timeout: 29 # Lambda timeout
    events:
      - http:
          path: rographql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
              - jobstreet_access_token
              - jobStreet_bowser_token
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      sesTemplatesRegion: ${file(config.${self:provider.stage}.json):sesTemplatesRegion}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      offlineReportBucket: ${file(config.${self:provider.stage}.json):offlineReportBucket}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      camuCreateStaffEndPoint: ${file(config.${self:provider.stage}.json):camuCreateStaffEndPoint}
      camuExitStaffEndPoint: ${file(config.${self:provider.stage}.json):camuExitStaffEndPoint}
      indeedAuthTokenAPI: ${file(config.${self:provider.stage}.json):indeedAuthTokenAPI}
      jobStreetAuthTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthTokenAPI}
      jobStreetAuthBrowserTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthBrowserTokenAPI}
      jobStreetTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetTokenAPI}
      
  wographql:
    handler: src/wohandler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: wographql
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
              - irukkaidtoken
              - jobstreet_access_token
              - jobStreet_bowser_token
              - indeed_access_token
              - microsoft_access_token
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      irukkaCloseJobUrl: ${file(config.${self:provider.stage}.json):irukkaCloseJobUrl}
      irukkaPartnerId: ${file(config.${self:provider.stage}.json):irukkaPartnerId}
      indeedPublishJobAPI: ${file(config.${self:provider.stage}.json):indeedPublishJobAPI}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      sesTemplatesRegion: ${file(config.${self:provider.stage}.json):sesTemplatesRegion}
      logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
      jobStreetTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetTokenAPI}
      asyncSunFishAPIPushFunction: ${file(config.${self:provider.stage}.json):asyncSunFishAPIPushFunction}
      asyncPAGTAPIPushFunction: ${file(config.${self:provider.stage}.json):asyncPAGTAPIPushFunction}
      recruitBucketName: ${file(config.${self:provider.stage}.json):recruitBucketName}
      customDomainName: ${file(config.${self:provider.stage}.json):customDomainName}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      offlineReportBucket: ${file(config.${self:provider.stage}.json):offlineReportBucket}
      jobStreetAuthTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthTokenAPI}
      pagtAPIURL: ${file(config.${self:provider.stage}.json):pagtAPIURL}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      decryptionAlgorithm: aes-256-ecb     
      decryptionSecretKeyName: entomo_decryption_key

  externalauth:
    handler: src/externalauth.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: externalauth
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          # authorizer:
          #   arn: ${file(config.${self:provider.stage}.json):authorizerARN}
          #   resultTtlInSeconds: 0
          #   type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      firebaseApiKey: ${file(config.${self:provider.stage}.json):firebaseApiKey}
      signInAPI: ${file(config.${self:provider.stage}.json):signInAPI}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      decryptionAlgorithm: aes-256-ecb     
      decryptionSecretKeyName: entomo_decryption_key
      
  woexternal:
    handler: src/woexternalresolver/woexternal.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: woexternal
          method: post
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(config.${self:provider.stage}.json):authorizerARN}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      stageName: ${self:provider.stage}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      source: BE
      region: ${self:provider.region}
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      
  initiateCamuResignationFunction:
    handler: src/initiateCamuResignation.initiateCamuResignation
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: integration-daily-camu-resignation-${self:provider.stage}
          description: 'This rule is used to trigger the process to call camu resignation api for all the instances'
          rate: cron(30 20 * * ? *) #2AM
          enabled: true
      - schedule:
          name: integration-daily-camu-resignation-continuous-trigger-${self:provider.stage}
          description: 'This rule is used to process the next set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 21-23 * * ? *) #2.30 AM to 4.30 AM
          enabled: true
          input:
            status: Open
      - schedule:
          name: integration-daily-camu-resignation-failed-trigger-${self:provider.stage}
          description: 'This rule is used to process the failed set of instances continuously at a interval of 10 minutes'
          rate: cron(0/10 23-0 * * ? *) #4.30 AM to 5.30 AM
          enabled: true
          input:
            status: Failed
    environment:
      stateMachineArn: ${file(config.${self:provider.stage}.json):dailyCamuResignationArn}

  insertCamuActiveInstances: #Step 1
    handler: src/stepFunction/insertCamuActiveInstances.insertCamuActiveInstances
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  insertResignationEmployee: #Step 2
    handler: src/stepFunction/insertResignationEmployee.insertResignationEmployee
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}

  processCamuResignation: #Step 3
    handler: src/stepFunction/processCamuResignation.processCamuResignation
    memorySize: 3008
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      camuExitStaffEndPoint: ${file(config.${self:provider.stage}.json):camuExitStaffEndPoint}
      stageName: ${self:provider.stage}
  
  initiateSunfishIntegrationFunction:
    handler: src/stepFunction/initiateSunfishActiveInstance.initiateSunfishActiveInstance
    timeout: 900 # Lambda timeout
    events:
      - schedule:
          name: integration-initiateSunfishActiveInstance-${self:provider.stage}
          description: 'This rule is used to trigger the process to call sunfish integration api for all the instances'
          rate: cron(0 0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22 * * ? *) 
          enabled: false
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      asyncSunFishAPIPushFunction: ${file(config.${self:provider.stage}.json):asyncSunFishAPIPushFunction}

  asyncSunFishAPIPushFunction:
    handler: src/woresolvers/sfapiIntegration/asyncSunFishAPIPushFunction.asyncSunFishAPIPushFunction
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      decryptionAlgorithm: aes-256-ecb     
      decryptionSecretKeyName: entomo_decryption_key

  asyncPAGTAPIPushFunction:
    handler: src/stepFunction/asyncPAGTAPIPushFunction.asyncPAGTAPIPushFunction
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      pagtAPIURL: ${file(config.${self:provider.stage}.json):pagtAPIURL}

  asyncJobStreetWebHookFunction:
    handler: src/woresolvers/sfapiIntegration/asyncJobStreetWebHookFunction.asyncJobStreetWebHookFunction
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      region: ${self:provider.region}
      jobStreetAuthTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthTokenAPI}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      jobStreetAuthBrowserTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthBrowserTokenAPI}
      jobStreetTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetTokenAPI}
      asyncJobStreetWebHookFunction: ${file(config.${self:provider.stage}.json):asyncJobStreetWebHookFunction}
      domainName: ${file(config.${self:provider.stage}.json):domainName}

  asyncJobStreetCloseWebHookFunction:
    handler: src/woresolvers/sfapiIntegration/asyncJobStreetCloseWebHookFunction.asyncJobStreetCloseWebHookFunction
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      stageName: ${self:provider.stage}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      region: ${self:provider.region}
      jobStreetAuthTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthTokenAPI}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      jobStreetAuthBrowserTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetAuthBrowserTokenAPI}
      jobStreetTokenAPI: ${file(config.${self:provider.stage}.json):jobStreetTokenAPI}
      asyncJobStreetCloseWebHookFunction: ${file(config.${self:provider.stage}.json):asyncJobStreetCloseWebHookFunction}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      emailFrom: ${file(config.${self:provider.stage}.json):emailFrom}
      sesRegion: ${file(config.${self:provider.stage}.json):sesRegion}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}

  asyncSyntrumAPIFunction:
    handler: src/stepFunction/asyncSyntrumAPIFunction.asyncSyntrumAPIFunction
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      decryptionAlgorithm: aes-256-ecb
      decryptionSecretKeyName: entomo_decryption_key

  pushLeaveToSyntrum:
    handler: src/stepFunction/pushLeaveToSyntrum.pushLeaveToSyntrum
    timeout: 900
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      webAddress: ${file(config.${self:provider.stage}.json):webAddress}
      decryptionAlgorithm: aes-256-ecb
      decryptionSecretKeyName: entomo_decryption_key
        
  getSyntrumAuthToken:
    handler: src/woresolvers/syntrum/getSyntrumAuthToken.getSyntrumAuthToken
    name: getSyntrumAuthToken

  payrollEventWebhook:
    handler: src/webhooks/payrollEventWebhook.payrollEventWebhook
    name: payrollEventWebhook
    events:
        - http:
            path: payrollEventWebhook
            method: post
            cors: 
              origin: '*'
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
      stageName: ${self:provider.stage}
      documentsBucket: ${file(config.${self:provider.stage}.json):documentsBucket}
      domainName: ${file(config.${self:provider.stage}.json):domainName}
      decryptionAlgorithm: aes-256-gcm
      decryptionSecretKeyName: welcome2caprice 

  encryptKeys:
    handler: src/roresolvers/Encryption/encryptKeys.encryptKeys
    name: encrypt-keys
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: encryptKeys # API path
          method: post # Method type
          cors: true
    environment:
      dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
      decryptionAlgorithm: aes-256-ecb     
      region: ${self:provider.region}
      decryptionSecretKeyName: entomo_decryption_key

stepFunctions:
  stateMachines:
    integrationCamuResignation: # step function for daily camu resignation
      name: ${opt:stage}-integrationCamuResignation
      events:
        - http:
            path: integrationCamuResignationStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'We will call camu exit api for all inactive employees.'
        StartAt: insertCamuActiveInstances
        States:
          insertCamuActiveInstances:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-insertCamuActiveInstances
            Next: FirstStepChoiceState
          FirstStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step2
                Next: insertResignationEmployee
            Default: EndFunction
          insertResignationEmployee:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-insertResignationEmployee
            Next: SecondStepChoiceState
          SecondStepChoiceState:
            Type: Choice
            Choices:
              - Variable: '$.nextStep'
                StringEquals: Step3
                Next: processCamuResignation
            Default: EndFunction
          processCamuResignation:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-processCamuResignation
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: 'camu resignation step function execution completed.'
            End: true

    asyncSunFishAPIPushFunction:
      name: ${opt:stage}-asyncSunFishAPIPushFunction
      events:
        - http:
            path: asyncSunFishAPIPushFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate aync step function."
        StartAt: processAsyncSunFishAPIPushFunction
        States:
          processAsyncSunFishAPIPushFunction:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-asyncSunFishAPIPushFunction
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Async step function execution completed."
            End: true

    asyncPAGTAPIPushFunction:
      name: ${opt:stage}-asyncPAGTAPIPushFunction
      events:
        - http:
            path: asyncPAGTAPIPushFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate aync step function."
        StartAt: processAsyncPAGTAPIPushFunction
        States:
          processAsyncPAGTAPIPushFunction:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-asyncPAGTAPIPushFunction
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Async step function execution completed."
            End: true

    asyncJobStreetWebHookFunction:
      name: ${opt:stage}-asyncJobStreetWebHookFunction
      events:
        - http:
            path: asyncJobStreetWebHookFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate aync step function."
        StartAt: processasyncJobStreetWebHookFunction
        States:
          processasyncJobStreetWebHookFunction:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-asyncJobStreetWebHookFunction
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Async step function execution completed."
            End: true

    asyncJobStreetCloseWebHookFunction:
      name: ${opt:stage}-asyncJobStreetCloseWebHookFunction
      events:
        - http:
            path: asyncJobStreetCloseWebHookFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate aync step function."
        StartAt: processasyncJobStreetCloseWebHookFunction
        States:
          processasyncJobStreetCloseWebHookFunction:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-asyncJobStreetCloseWebHookFunction
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Async step function execution completed."
            End: true


    asyncSyntrumAPIFunction:
      name: ${opt:stage}-asyncSyntrumAPIFunction
      events:
        - http:
            path: asyncSyntrumAPIFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate aync step function."
        StartAt: processAsyncSyntrumAPIFunction
        States:
          processAsyncSyntrumAPIFunction:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-asyncSyntrumAPIFunction
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Async step function execution completed."
            End: true

    pushLeaveToSyntrum:
      name: ${opt:stage}-pushLeaveToSyntrum
      events:
        - http:
            path: pushLeaveToSyntrum
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: "Initiate syntrum leave integration async step function."
        StartAt: processPushLeaveToSyntrum
        States:
          processPushLeaveToSyntrum:
            Type: Task
            Resource: ${file(./config.${self:provider.stage}.json):resourceArnPrefix}-pushLeaveToSyntrum
            Next: EndFunction
          EndFunction:
            Type: Pass
            Result: "Syntrum leave integration step function execution completed."
            End: true

resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: '401' # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series error code
      Type: 'AWS::ApiGateway::GatewayResponse'
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: 'ApiGatewayRestApi'
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
