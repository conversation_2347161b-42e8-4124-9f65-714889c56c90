const { ApolloServer, gql } = require('apollo-server-lambda');
const { resolvers } = require('./roresolver');
const path = require("path");
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(path.resolve()+'/src/'+'roschema.graphql', 'utf8'));
// require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');


module.exports.graphql = (event, context, callback) => {
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes
    // get customAuthorizerData from firebase authorize and  return it to resolver if exists.
    let idToken = event.requestContext.authorizer.idToken ? event.requestContext.authorizer.idToken : '';
    let refreshToken = event.requestContext.authorizer.refreshToken ? event.requestContext.authorizer.refreshToken : '';
    let partnerId = (event.headers.partnerId && event.headers.partnerId !== 'null' && event.headers.partnerId !== 'undefined') ? event.headers.partnerId : '-';
    let jobStreet_bowser_token = event.headers.jobStreet_bowser_token?event.headers.jobStreet_bowser_token:'';
    let jobstreet_access_token = event.headers.jobstreet_access_token?event.headers.jobstreet_access_token:'';
    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            // // get the orgCode from http headers
            // let Org_Code = await commonLib.func.getOrgCode(event);
            
            // // Append User_Ip in context
            // let User_Ip = event.headers.user_ip; 

            // // get database connection object ( pass stageName,dbPrefix,dbSecretName,region and Org_Code as params )
            // let connection = Org_Code ? await commonLib.func.getDataBaseConnection(
            // {
            //     stageName: process.env.stageName, dbPrefix: process.env.dbPrefix, dbSecretName: process.env.dbSecretName,
            //     region: process.env.region, orgCode: Org_Code
            // }) : null;
            // // for orgDbConnection
            // let orgDbConnection = knex(connection.OrganizationDb);
            // // get token from headers
            // let authToken = (event.headers.Authorization && event.headers.Authorization !== 'null' && event.headers.Authorization !== 'undefined') ? event.headers.Authorization : idToken;
            // // get the employeeId from token (Pass Authorization and organizationDb connection as input )
            // let Employee_Id = (authToken && Org_Code) ? await commonLib.func.getEmployeeByToken(authToken, orgDbConnection) : null;
            // // close db connection
            // orgDbConnection.destroy();
            // // return header to resolver function
            // return { Org_Code, User_Ip, connection, Employee_Id,partnerId};

            let authDetails ={
                idToken: idToken,
                refreshToken: refreshToken
            };

            let contextData = await commonLib.func.getContextDataWithEmployeeId(event,authDetails,'wo');
            contextData.partnerId = contextData.partnerid;
            // return header to resolver function
            if(jobstreet_access_token){
                return {jobstreet_access_token,jobStreet_bowser_token,...contextData};
            }
            return {...contextData};
        }
    });
    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    
    function callbackFilter(error, output) {
        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if (process.env.stageName !== 'local') {
            // parse the response data
            let responseData = JSON.parse(output.body);
            // push idToken and refresh token into an json object
            let identityToken = {
                idToken: idToken,
                refreshToken: refreshToken
            }
            // return the idToken and refreshTOken to UI
            responseData.identityToken = identityToken;
            output.body = JSON.stringify(responseData);
        }
        
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};
