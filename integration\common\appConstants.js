const defaultValues = {
    activeInstanceToBeProcessed: 20
}
const s3DefaultValues = {
    s3DefaultEncryption: "AES256",
    contentType: 'image/png',
    contentEncoding: 'base64'
}
const formName = {
    userAccounts: "User Accounts",
    recruitment: "Recruitment",
    corehr: "Core HR",
    jobpost: "Job Posts"
}
const formId = {
    jobpost: 15,
    jobCandidate: 16,
    hiringForeCastSettings: 285,
    hiringForeCast: 287,
    tableOfOrganization: 288,
    reqruitmentRequest: 291,
    newPosition: 290,
    toStatusApproval: 296,
    hiringFlow: 282,
    talentPoolCandidate: 297,
    myIntegrations: 308,
    leaves: 31,
    myPayslip: 345
};
const awsSesTemplates = {
    CommonNotificationEngine: 'CommonNotificationEngine',
    RecruitmentEmail: "RecruitmentEmail",
    EntomoRecruitmentEmail: "EntomoRecruitmentEmail"

}
const defaultURL = {
    microsoftAPIUrl: 'https://graph.microsoft.com/v1.0'
}

const syntrumValues = {
    apiUrl: 'https://dev-hrms-staging-middleware-entomo.azurewebsites.net/trpc/',
    signInEndpointName: '/auth.signIn',
    payrollEndpoint: '/employee.createEmployeePayroll',
    authenticationTokenType: 'Bearer',
    employeePayrollKey: 'payrolls',
    monthlyPayroll: 'Monthly',
    flag: 'Yes',
    integrationType: 'Syntrum',
    payrollEvents: ['payslip', 'salary', 'air ticketing'],
    payrollStatusMapping: {
        'payslip': ['create', 'delete'],
        'salary': ['create', 'revised'],
        'air ticketing': ['settled', 'yet to be settled']
    }
};

const syntrumActions = {
    'LOP': { direction: 'Push', action: 'Approve', entityType: 'Payroll' },
    'LOP_REVERSAL': { direction: 'Push', action: 'Cancel', entityType: 'Payroll' }
};
const s3FileUpload = {
    contentType: 'image/png',
    contentEncoding:'base64',
    txtFileType: 'text/plain',
    defaultEncryption:'AES256',
    binaryFile:'application/octet-stream'
}

module.exports.s3FileUpload = s3FileUpload;
module.exports.defaultValues = defaultValues;
module.exports.formName = formName;
module.exports.formId = formId;
module.exports.s3DefaultValues = s3DefaultValues;
module.exports.awsSesTemplates = awsSesTemplates;
module.exports.defaultURL = defaultURL;
module.exports.syntrumValues = syntrumValues;
module.exports.syntrumActions = syntrumActions;