
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants')

module.exports.retrieveForeCastSettings = async (parent, args, context, info) => {

    console.log("Inside retrieveForeCastSettings function.")
    let organizationDbConnection;

    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, args.formId);
        
        if (
            (Object.entries(checkRights).length > 0 &&
              checkRights.Role_View === 1 &&
              args.formId === formId.hiringForeCastSettings &&
              (checkRights.Employee_Role.toLowerCase() === 'admin' ||
                (checkRights.Is_Recruiter &&
                  checkRights.Is_Recruiter.toLowerCase() === 'yes'))) ||
            args.formId === formId.hiringForeCast
          ) {
            const [foreCastSettings, countResult] = await Promise.all([
                organizationDbConnection(ehrTables.mppForecastSettings + ' as FCS')
                  .select('FCS.Forecast_Settings_Id', 'FCS.Release_Date', 'FCS.End_Month', organizationDbConnection.raw('GROUP_CONCAT(FCSR.Role_Id) as Roles_Ids'))
                  .leftJoin(ehrTables.mppForecastSettingsRole + ' as FCSR', 'FCSR.Forecast_Settings_Id', 'FCS.Forecast_Settings_Id')
                  .groupBy('FCS.Forecast_Settings_Id')
                  .first(),
              
                organizationDbConnection(ehrTables.mppHiringForecast)
                  .count({ totalRecords: "*" })
              ]);
              
              const totalRecords = countResult[0].totalRecords;
              
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Hiring forecast settings retrieved successfully.", settings: foreCastSettings,totalHiringForeCastRecords:totalRecords };

        } else {
            throw '_DB0100';
        }

    }catch(err){
        //Destroy DB connection
        console.error('Error in retrieveForeCastSettings function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'EI00176'); //Sorry!, an error occured while retrieve the hiring forecast settings. Please try after some time.
        throw new ApolloError(errResult.message, errResult.code);
    }
}


