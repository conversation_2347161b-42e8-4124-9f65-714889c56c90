// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const {ehrTables} = require('../../../common/tablealias');
//Require apollo server to return error message
const {ApolloError} = require('apollo-server-lambda');
// require common constant files
const {formId} = require('../../../common/appConstants')

const retrieveTalentPoolList = async (organizationDbConnection) => {
    try {
        return organizationDbConnection(ehrTables.talentPool + ' as TP')
            .select(
                'TP.Talent_Pool_Id as talentPoolId',
                'TP.Talent_Pool as talentPool',
                'TP.Added_On as addedOn',
                'TP.Updated_On as updatedOn',
                'TP.Added_By as addedBy',
                'TP.Updated_By as updatedBy',
                organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as addedByName"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as updatedByName"),
                organizationDbConnection.raw(`(SELECT COUNT(*) FROM candidate_recruitment_info as CRI
                            INNER JOIN candidate_personal_info as CPI ON CRI.Candidate_Id = CPI.Candidate_Id 
                            WHERE CPI.Talent_Pool_Id = TP.Talent_Pool_Id AND CRI.Archived = 'No') as candidateCount`)
                )
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'TP.Added_By', 'EPI.Employee_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'TP.Updated_By', 'EPI2.Employee_Id')
            .orderBy('TP.Talent_Pool', 'asc');
    } catch (error) {
        console.error("Error in retrieveTalentPoolList catch block", error);
        throw error;
    }
};

module.exports.getTalentPoolList = async (parent, args, context, info) => {
    console.log('Inside getTalentPoolList function');
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmployeeId = context.Employee_Id;
        const checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const talentPoolList = await retrieveTalentPoolList(organizationDbConnection);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return {
                errorCode: '',
                message: 'Talent pool list has been retrieved successfully.',
                talentPoolListData: talentPoolList
            };
        } else {
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }
    } catch (e) {
       
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getTalentPoolList function main catch block.', e);
        const errResult = commonLib.func.getError(e, 'TAP0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
};
