const postPosition = `mutation ($input: PostPositionInput!) {
  postPosition(input: $input) {
    ... on PostPositionPayload_Success {
      positionOpening {
        documentId {
          value
        }
      }
      positionProfile {
        profileId {
          value
        }
      }
    }
    ... on PostPositionPayload_Conflict {
      conflictingPositionOpening {
        documentId {
          value
        }
      }
      conflictingPositionProfile {
        profileId {
          value
        }
      }
    }
  }
}
  `
const updateJobProfile = `mutation ($input: UpdatePostedPositionProfileInput!) {
    updatePostedPositionProfile(input: $input) {
      positionProfile {
        profileId {
          value
        }
      }
    }
  }`

const closeJobProfile = `mutation ($input: ClosePostedPositionProfileInput!) {
    closePostedPositionProfile(input: $input) {
      positionProfile {
        profileId {
          value
        }
      }
    }
  }`

const updatePositionOpeningPerson = `mutation ($input: UpdatePositionOpeningPersonContactsInput!) {
    updatePositionOpeningPersonContacts(input: $input) {
      positionOpening {
        documentId {
          value
        }
      }
    }
  }`

const deletePositionOpening = `mutation ($input: DeletePositionOpeningInput!) {
    deletePositionOpening(input: $input) {
      positionOpening {
        documentId {
          value
        }
      }
    }
  }`

const retrievePosition = `query ($hirerId: String!, $statusCode: String) {
    positionOpenings(hirerId: $hirerId, filter: { statusCode: $statusCode }) {
      edges {
        node {
          paginatedPositionProfiles {
            edges {
              node {
                profileId {
                  value
                }
              }
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }`
const seekIdToHirerId = `query ($legacyId: Int!) {
    seekAnzAdvertiser(id: $legacyId) {
      id {
        value
      }
    }
  }`
const hiringOrganization = `query ($id: String!) {
    hiringOrganization(id: $id) {
      name
      seekApiCapabilities {
        relationshipTypeCodes
      }
    }
  }`
const hiringOrgLegacy = `query ($legacyId: Int!) {
  seekAnzAdvertiser(id: $legacyId) {
  name
    id {
      value
    }
  }
}`
const candidateProfile = `query ($id: String!) {
  candidateProfile(id: $id) {
    profileId {
      value
    }
      seekQuestionnaireSubmission {
      questionnaire {
        id {
          value
        }
      }
      responses {
        componentTypeCode
        ... on ApplicationQuestionResponse {
          score
          component {
            value
            questionHtml
          }
          answers {
            answer
            choice {
              value
            }
          }
        }
        ... on ApplicationPrivacyConsentResponse {
          component {
            value
            descriptionHtml
          }
          consentGivenIndicator
        }
      }
      score

    }
    attachments {
      descriptions
      seekRoleCode
      url
    }
    createDateTime
    candidate {
      person {
        name {
          given
          family
        }
        communication {
          address {
            formattedAddress
            countryCode
            postalCode
            city
          }
          phone {
            formattedNumber
          }
          email {
            address
          }
        }
      }
    }
    associatedPositionProfile {
      seekHirerJobReference
      positionOrganizations {
        id {
          value
        }
        name
      }
      positionUri
    }
    employment {
      organization {
        name
      }
      positionHistories {
        start
        end
        title
      }
    }
    education {
      institution {
        name
      }
      educationDegrees {
        name
        degreeGrantedStatus
        date
      }
      descriptions
    }
    certifications {
      name
      issued
      effectiveTimePeriod {
        validTo
      }
      issuingAuthority {
        name
      }
      descriptions
    }
    qualifications {
      competencyName
    }
    positionPreferences {
      locations {
        referenceLocation {
          formattedAddress
          city
          postalCode
          countryCode
        }
      }
      seekAnzWorkTypeCodes
      seekWorkArrangementCodes
      seekSalaryExpectations {
        amount {
          currency
          value
        }
        countryCode
        intervalCode
      }
    }
    personAvailability {
      immediateStartIndicator
      noticePeriodMeasure {
        unitCode
        value
      }
    }  
  }
}
`
const positionProfile= `query ($id: String!) {
  positionProfile(id: $id) {
    profileId {
      value
    }
    positionTitle
    positionUri
    postingInstructions {
      start
      end
    }
    seekHirerJobReference
  }
}
  `

 const createQuestions = ` mutation($input: CreateApplicationQuestionnaireInput!) {
    createApplicationQuestionnaire(input: $input) {
      applicationQuestionnaire {
        id {
          value
        }
  
      }
  
    }
  
  }`

module.exports = {
  postPosition,
  updateJobProfile,
  closeJobProfile,
  updatePositionOpeningPerson,
  deletePositionOpening,
  retrievePosition,
  hiringOrganization,
  seekIdToHirerId,
  hiringOrgLegacy,
  candidateProfile,
  positionProfile,
  createQuestions
}
