# defining custom data type
scalar Date

type Query {
  camuCreateStaff(employeeId: [Int]!): camuCreateStaffResponse

  encryptDecryptCamuData(camuToken: String): encryptDecryptCamuDataResponse
  getStaffSchedulesByDateRange(
    staffId: String!
    fromDate: String!
    toDate: String!
    duration: String
    leavePeriod: String
  ): getStaffSchedulesByDateRangeResponse!
  getCamuStatus: camuStatusResponse
  jobBoardIntegrationStatus(form_Id: Int!): irukkaStatusResponse
  getLocationDetails: locationDetailsResponse
  getCompanySignUpDetails : getCompanySignUpDetailsResponse
  getAWSCognitoIdentities(Type : String): getAWSCognitoIdentitiesResponse
  getAuthToken : getAuthTokenResponse
  getAuthTokenJobStreet
  (hirer_Id: String
  form_Id: Int!
  isPublish: Int):getAuthTokenJobStreetResponse
  verifyJobStreetRelation(
  companyId: Int!
  form_Id: Int!
  ):commonResponse
  getJobStreetHirerList(status: String):getJobStreetHirerListResponse
  getIndeedAuthCredentials: getIndeedAuthCredentialsResponse
  getIndeedAuthToken(
    scope: String!
    grantType: String!
  ): getIndeedAuthTokenResponse
  retrieveIndeedJobPostDetails(jobPostId: Int!): retrieveIndeedJobPostDetailsResponse
  retrieveJobStreetJobPostDetails(jobPostId: Int!): retrieveJobStreetJobPostDetailsResponse
  getEncryptionKey: getEncryptionKeyResponse
  listDetailsBasedOnOrgPosId(formId: Int!, originalPosId: String,organizationStructureId:Int): listDetailsBasedOnOrgPosIdResponse
  listDetailsBasedOnGroupCode(formId: Int!, postionParentId: String,offset:Int,limit:Int):listDetailsBasedOnGroupCodeResponse
  listForecastPosition(formId: Int!, postionParentId: String, forecastingYear: Int, action:String,originalPositionId: String,offset:Int,limit:Int,alexport:Boolean,parentPath: String): listForecastPositionResponse
  listHiringForecast(forecastingYear: Int!, postionParentId: String,offset:Int,limit:Int,alexport:Boolean,groupFilter: OrgFilterInput,divisionFilter: OrgFilterInput,departmentFilter: OrgFilterInput,sectionFilter: OrgFilterInput):listHiringForecastResponse
  retrieveForeCastSettings(formId: Int!): retrieveForeCastSettingsResponse
  retrieveEmployeeRoleEmail(roleIds: [Int],formId: Int): retrieveEmployeeEmailResponse
  listReqruitmentRequest(postionParentCode: String,formId:Int,positionParentId:String,alexport:Boolean,groupFilter: OrgFilterInput,divisionFilter: OrgFilterInput,departmentFilter: OrgFilterInput,sectionFilter: OrgFilterInput):listReqruitmentRequestResponse
  listNewPositionRequest(postionParentCode: String,formId:Int,positionParentId:String,alexport:Boolean,groupFilter: OrgFilterInput,divisionFilter: OrgFilterInput,departmentFilter: OrgFilterInput,sectionFilter: OrgFilterInput):listNewPositionRequestResponse
  retrieveNewPositionDetails(positionRequestId:Int!):retrieveNewPositionDetailsResponse
  retrievePositionJobSummary(originalPositionId:String!, formId:Int!,mppPositionType: String,positionRequestId: Int,jobPostId:Int,source: String): retrievePositionJobSummaryResponse
  retrieveEducationLabelList:retrieveEducationLabelListResponse
  retrievePositionLevel:retrievePositionLevelResponse
  getTalentPoolList(formId: Int!): getTalentPoolListResponse
  retrieveRecruitmentSettings : retrieveRecruitmentSettingsResponse
  getArchiveReasonsList(formId: Int!, stageId: Int): getArchiveReasonsListResponse
  retrieveMicrosoftIntegration(formId: Int!) : getMicrosoftIntegrationResponse
  executeSyntrumIntegrationRequest(uniqueIds:[Int]!,elementHeader:String!,integrationRequestType:String!,baseElementHeader:String!,payrollDetails:String!,action:String!,uniqueIdDetails:String!):commonResponse
  listSalaryPayslip(formId: Int, year: Int!): listSalaryPayslipResponse
  retrieveWorkflowApprovalSetting(formId: Int!): retrieveWorkflowApprovalSettingResponse
}
type retrieveWorkflowApprovalSettingResponse {
  errorCode: String
  message: String
  enableWorkflowApproval: String
}
type listSalaryPayslipResponse {
  errorCode: String
  message: String
  payslipDetails: String
  employeeDateOfJoin: String
  payrollStartDate: String
}

type camuCreateStaffResponse {
  errorCode: String
  message: String
}
type commonResponse {
 errorCode: String
  message: String
}

type encryptDecryptCamuDataResponse {
  errorCode: String
  message: String
  encryptedToken: String
  decryptedData: DecryptedData
}

type DecryptedData {
  uniqueID: String
  staffId: String
  email: String
  iat: Int
  exp: Int
}

type getStaffSchedulesByDateRangeResponse {
  errorCode: String
  message: String
  allSchedules: [allSchedules]
  processedSchedules : [processedSchedule]
}

type processedSchedule {
  eventDate: String
  isScheduled: Boolean
  isScheduledPartially: Boolean
  colorCode: String
}

type allSchedules {
  eventDate: String
  schedules: [schedules]
}

type schedules {
  id: String
  academicYear: String
  courseName: String
  day: String
  departmentName: String
  eventDate: String
  fromTime: String
  locationName: String
  programName: String
  sectionName: String
  semester: String
  slotDuration: String
  staffID: String
  staffName: String
  subject: String
  toTime: String
  type: String
}

type camuStatusResponse {
  errorCode: String
  message: String
  camuStatus: [camuStatus]
}

type camuStatus{
  Employee_Id: Int
  Action: String
  Log_Timestamp: String
  Camu_Push_Status: String
  Failure_Reason: String
}

type irukkaStatusResponse{
  errorCode :String
  message : String
  getStatus :[getStatus]
}
type getStatus{
  Integration_Id: Int
  Integration_Type: String
  Integration_Status: String
  Indeed_Client_Id: String
  Indeed_Secret_Key: String
  Indeed_Source_Name: String
  Company_Id: String
  Hirer_ID: String

}
type locationDetailsResponse{
  errorCode :String
  message : String
  locationDetails :[locationDetails]
}
type locationDetails{
  Location_Id: Int
  Location_Name: String,
    Pincode: String,
    City_Id: Int,
    City_Name: String,
    State_Id: Int,
    State_Name: String,
    Country_Code: String,
    Country_Name: String

}
type getCompanySignUpDetailsResponse{
  errorCode :String
  message : String
  getPersonalDetails : [getPersonalDetails]
  getCompanyDetails :[getCompanyDetails]
}
type getPersonalDetails{
  Employee_Id: String,
  DOB: Date,
  Emp_Email: String,
  Mobile_Number: String
  Updated_By_Name:String

}
type getCompanyDetails {
   Org_Name: String
   Org_Description: String
}
type getAWSCognitoIdentitiesResponse {
  errorCode:String
  message : String
  data: irukkaCognitoIdentites
}
type irukkaCognitoIdentites{
  userPoolId:String
  clientSecret: String
  secretPassword:String
  workwiselymstenantID: String
  workwiselymsapplicationID: String

}
type getAuthTokenResponse{
  errorCode :String
  message :String
  getData: tokens
}
type getAuthTokenJobStreetResponse{
 errorCode :String
  message :String
  getData: jobStreetTokens
}
type jobStreetTokens{
 browserToken:String
accessToken: String
}
type getJobStreetHirerListResponse{
errorCode :String
  message :String
  hirerList:[hirerList]
}
type hirerList{
  Hirer_List_Id: Int
  Integration_Type: String
  Hirer_ID: String
  Status:String
  Company_Id: String
  Company_Name: String
}
type tokens {
  authToken: String
  refreshToken: String
}
type getIndeedAuthCredentialsResponse {
  errorCode:String
  message : String
  data: indeedAuthCredentials
}
type indeedAuthCredentials{
  clientId: String
  secretKey:String
  indeedApplyToken: String
}
type getIndeedAuthTokenResponse {
  errorCode:String
  message : String
  data: String
  sourceName: String
}

type retrieveIndeedJobPostDetailsResponse {
  errorCode:String
  message : String
  data: [indeedJobPostDetails]
}
type retrieveJobStreetJobPostDetailsResponse {
 errorCode:String
  message : String
  data: [jobStreetJobPostDetails]
}
type jobStreetJobPostDetails {
jobStreetId: Int
JobPostId: Int
roleCode: String
recruiterEmailId: String
recruiterName: String
documentId: String
videoPositionCode: String
jobStreetIdOpening:Int
videoUrl: String
positionLocationId: String
seekAdvertisementProductId: String
subCategoryId: String
categoryId: String
seekWorkTypeCode: String
seekWorkArrangementCodes: String
advertisementBranding: String
seekBillingReference: String
currencyName:String
currencyId: Int
currencyCode:String
payDescription: String
profileId: String
appliedStatus: String
searchSummaryDescription: String
minimumAmount: Float
maximumAmount: Float
jobTitle: String
jobSummary: String
searchBulletPointsArray:String
phoneNo:String
recruiterNoCountryCode:String
hirerId: String
}
type indeedJobPostDetails{
  integrationId: Int
  jobPostId: Int
  dynamicFormId: Int
  employerJobId: String
  benefits: String
  contactName: String
  contactType: String
  contactPhone: String
  contactEmail: String
  status: String
  cityId: Int
  cityName: String
  stateId: Int
  stateName: String
  workplaceType: String
}

type getEncryptionKeyResponse{
  errorCode:String
  message : String
  indeedEncryptionKeyData: String
}

type listForecastPositionResponse{
  errorCode:String
  message : String
  parentGroup: String
  totalCountResult:Int
  orgLevel:String
  positionList: [forecastPositionData]
  s3Url: String
  s3Path: String
}
type listDetailsBasedOnOrgPosIdResponse{
  errorCode:String
  message : String
  positionDetails: positionDetails
}
type groupPositionCode{
divisionList:[position]
sectionList:[position]
deptList:[position]
costCodeList:[String]
}
type position{
 Originalpos_Id: String
 Pos_Code: String
 Pos_Name: String
 Pos_full_Name: String
}
type positionDetails {
  costCode: String
  groupCode: String
  divisionCode: String
  sectionCode: String
  deptCode: String
  groupName: String
  divisionName: String
  sectionName: String
  deptName: String
  groupFullName: String
  deptFullName: String
  sectionFullName: String
  divisionFullName: String
  totalRecruitmentCount:Int
  warmBodies:Int
  approvedPosition:Int
  positionLevel:String
  positionLevelId:Int
}
type listDetailsBasedOnGroupCodeResponse{
errorCode:String
  message : String
  positionDetails: groupPositionCode
  totalRecords:Int
}

type forecastPositionData {
  Originalpos_Id: Int
  Organization_Structure_Id:Int
  Pos_Code: String
  Pos_Name: String
  Status: String
  Job_Title_Code: String
  Cost_Code: String
  Global_Grade: String
  Pos_full_Name:String
  Approved_Position: Int
  orgLevel:String
  Warm_Bodies: Int
  To_Be_Hired: Int
  Parent_Id: Int
  Parent_Path: String
}

type listHiringForecastResponse{
  errorCode:String
  message : String
  totalCountResult:Int
  result: String
  groupId: String
  orgLevel:String
  s3Url: String
  s3Path: String
}

type retrieveForeCastSettingsResponse {
  errorCode: String
  message : String
  totalHiringForeCastRecords:Int
  settings: foreCastSettingsData
}

type foreCastSettingsData{
  Forecast_Settings_Id: Int
  Release_Date: Date
  End_Month: Int
  Roles_Ids: String
}
type listReqruitmentRequestResponse{
  errorCode: String
  message : String
  orgLevel:String
  groupCode:String
  positionParentId:String
  s3Url: String
  s3Path: String
  reqruitmentRequestDetails: [reqruitmentRequestDetails]
}
type listNewPositionRequestResponse{
  errorCode: String
  message : String
  groupCode:String
  positionParentId:String
  orgLevel:String
  s3Url: String
  s3Path: String
  openPositiontRequestDetails: [openPositiontRequestDetails]
}
type openPositiontRequestDetails{
  Position_Request_Id: Int
  Original_Position_Id: String
  Organization_Structure_Id: Int
  Position_Title: String
  Group_Code: String
  Group_Name: String
  Pos_Code:String
  Division_Code: String
  Division_Name: String
  Department_Code: String
  Department_Name: String
  Section_Code: String
  Section_Name: String
  Position_Level:String
  Position_Level_Id:Int
  Cost_Center: String
  Custom_Group_Id : Int
  Employee_Type: String
  Employee_Type_Name:String
  No_Of_Position: Int
  Reason_For_Replacement:String
  Comments: String
  Workflow_Id: Int
  Status: String
  Added_By:String
  Added_On:String
  approvedByName:String
  Approver_Id:Int
  Updated_On:String
  Updated_By:String
  Added_By_Email: String
  Request_Type: String
}
type retrieveNewPositionDetailsResponse{
  errorCode: String
  message : String
  openPositiontRequestRetrieveDetails: openPositiontRequestRetrieveDetails
}
type openPositiontRequestRetrieveDetails{
  Position_Request_Id: Int
  Original_Position_Id: String
  Organization_Structure_Id:Int
  Position_Title: String
  Custom_Group_Id: Int
  Pos_Code:String
  Group_Code: String
  Group_Name: String
  Position_Level:String
  Position_Level_Id:Int
  CustomGroupName: String
  Reason_For_Replacement:String
  Division_Name: String
  Department_Name: String
  Division_Code: String
  Department_Code: String
  Section_Name:String
  Section_Code: String
  Cost_Center: String
  Employee_Type: String
  Employee_Type_Name:String
  No_Of_Position: Int
  Comments: String
  Workflow_Id: Int
  Event_Id:String
  Status: String
  Added_By: String
  Added_On: String
  Approver_Id:Int
  approvedByName:String
  Updated_On: String
  Updated_By: String
  Internal_Operating_Network: [String]
  External_Operating_Network: [String]
  License_Certificate:String
  License_Certificate_Details: String
  Request_Type: String
  Experience:[Experience]
  WorkingConditions:[WorkingConditions]
  DutiesResponsibilities:[DutiesResponsibilities]
  education:[education]
}
type Experience {
  Experience_Id: Int
  Type_Of_Jobs: String
  Months: Int
  Years: Int
  Position_Request_Id: Int
}
type education {
  Mpp_Education_Requirements_Descriptions_Id:Int
  Description :String
  Education_Type: String,
  Position_Request_Id: Int
}

type WorkingConditions {
  Working_Condition_Id: Int
  Working_Area: String
  Time_Spent: Int
  Position_Request_Id: Int
}

type DutiesResponsibilities {
  Duties_Responsibility_Id: Int
  Position_Request_Id: Int
  Regular_Duties: String
  No_Of_Hours_Period: String
  Period: String
  Competency: String
  Competencies_Required: String
  Rating_Of_Competency: Float
}
type reqruitmentRequestDetails{
  Recruitment_Id: Int
  Original_Position_Id: String
  Custom_Group_Id: Int
  Group_Code: String
  Approved_Position:Int
  Warm_Bodies:Int
  Division_Code: String
  Employee_Type_Name:String
  Department_Code: String
  Section_Code: String
  Cost_Center: String
  Employee_Type: String
  No_Of_Position: Int
  totalRecords:Int
  Workflow_Id: Int
  CustomGroupName: String
  Status: String
  Pos_Code: String
  Pos_Name:String
  Added_By:String
  Replacement_For:String
  candidateCount:Int
  Reason_For_Replacement:String
  approvedByName:String
  Approver_Id:Int
  jobPostStatus:String
  Added_On:String
  Updated_On:String
  Updated_By:String
  Position_Level:String
  Position_Level_Id:Int
  Group_Name: String
  Division_Name: String
  Department_Name: String
  Section_Name:String
}

type retrieveEmployeeEmailResponse {
  errorCode: String
  message : String
  emailList: [employeeEmailData]
  nonRoleEmailList:[employeeEmailData]
}

type employeeEmailData {
  User_Defined_EmpId: String
  Employee_Id: Int
  Emp_Email: String
  Employee_Name: String
}

type retrievePositionJobSummaryResponse {
  errorCode: String
  message: String
  summary: jobSummaryData
  vacancyAvailable:Int
}

type jobSummaryData {
  Job_Description: String
  Company_Id: Int
  Career_Band: String
  Job_Summary: String
  Skills_Competencies: String
  Education: String
  Experience: String
  Working_Conditions: String
  Working_Relationship_Internal: [String]
  Working_Relationship_External: [String]
  Minimum_Requirements: String
}
type retrievePositionLevelResponse{
errorCode: String
  message: String
  positionLevelList: [positionLevelList]
}
type positionLevelList{
  Position_Level_Id:Int
  Position_Level: String
}
type retrieveEducationLabelListResponse{
errorCode: String
  message: String
  educationLabelList: [educationLabelList]
}
type educationLabelList{
  Mpp_Education_Requirements_Id:Int
  Education_Type: String
}

type getTalentPoolListResponse{
  message: String
  errorCode: String
  talentPoolListData: [talentPoolList]
}

type talentPoolList {
  talentPoolId: Int
  talentPool: String
  addedOn: String
  addedBy: Int
  updatedOn: String
  updatedBy: Int
  addedByName: String
  updatedByName: String
  candidateCount: Int
}

type retrieveRecruitmentSettingsResponse{
  message: String
  errorCode: String
  warmBodiesIncludeNoticePeriod: String
  allowHireWithoutApprovedVacancy: String
}

type getArchiveReasonsListResponse {
  message: String
  errorCode: String
  archiveReasonList: [archiveReasonsData]
}

type archiveReasonsData {
  Reason_Id: Int
  Reason: String
  Stage: String
}

type getMicrosoftIntegrationResponse {
  message: String
  errorCode: String
  microsoftEmail: String
  calendarStatus: String
  teamsStatus: String
}

input OrgFilterInput {
  Org_Level: String
  code: String
  id: String
}

schema {
  query: Query
}