{"dbSecretName": "hrapp-stage", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::************:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:************:function:firebase-lambda-authorizer", "customDomainName": "", "firebaseApiKey": "AIzaSyB-QCDxis2HG3hHIreLPiidSlN_eCyi3m8", "securityGroupIds": "", "subnetIds": "", "logoBucket": "s3.hrapp-dev-public-images", "emailFrom": "<EMAIL>", "sesTemplatesRegion": "us-east-1", "documentsBucket": "caprice-dev-stage", "offlineReportBucket": "offlinereports.hrapp.co.in", "webAddress": ".co.in", "emailTo": "<EMAIL>", "sesRegion": "us-east-1", "camuCreateStaffEndPoint": "external/staff", "camuExitStaffEndPoint": "external/staff/exit", "dailyCamuResignationArn": "arn:aws:states:ap-south-1:************:stateMachine:dev-integrationCamuResignation", "irukkaCloseJobUrl": "https://aju0i48d0f.execute-api.ap-south-1.amazonaws.com/uat/partner/api/v1/partnerIntegration/closeJob/a4ae0c94-0051-4f8f-9122-1e7cbb4bf03e", "irukkaPartnerId": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:************:function:INTEGRATION-dev", "signInAPI": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=", "indeedAuthTokenAPI": "https://apis.indeed.com/oauth/v2/tokens", "indeedPublishJobAPI": "https://apis.indeed.com/graphql", "sunFishesAPI": "https://sf7dev-api.dataon.com/sfapi/", "jobStreetAuthTokenAPI": "https://auth.seek.com/oauth/token", "jobStreetAuthBrowserTokenAPI": "https://graphql.seek.com/auth/token", "jobStreetTokenAPI": "https://graphql.seek.com/graphql", "pagtAPIURL": "https://testentomoapi.punongbayan-araullo.com/api", "asyncSunFishAPIPushFunction": "arn:aws:states:ap-south-1:************:stateMachine:dev-asyncSunFishAPIPushFunction", "asyncPAGTAPIPushFunction": "arn:aws:states:ap-south-1:************:stateMachine:dev-asyncPAGTAPIPushFunction", "asyncJobStreetWebHookFunction": "arn:aws:states:ap-south-1:************:stateMachine:dev-asyncJobStreetWebHookFunction", "asyncJobStreetCloseWebHookFunction": "arn:aws:states:ap-south-1:************:stateMachine:dev-asyncJobStreetCloseWebHookFunction", "recruitBucketName": "recruit.hrapp.co.in", "atsNameForIndeed": "Flowtrack"}