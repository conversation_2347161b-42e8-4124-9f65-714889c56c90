const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formId, syntrumValues } = require('../../../common/appConstants');
const moment = require('moment');

module.exports.listSalaryPayslip = async (parent, args, context) => {
    let organizationDbConnection;
    try {
        console.log("Inside listSalaryPayslip function.");
        const employeeId = context.Employee_Id;
        const accessFormId = args.formId || formId.myPayslip;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // STEP 1: Check employee access rights
        const hasAccess = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, employeeId, null, 'Role_View', 'Back-end', null, accessFormId
        );
        if (!hasAccess) throw '_DB0100';

        // STEP 2: Get month list and necessary dates
        const { monthList, employeeDateOfJoin, payrollStartDate } = await getMonthList(
            organizationDbConnection, employeeId, args, context
        );

        if (!monthList.length) return { errorCode: "", message: "No salary data available.", payslipDetails: "[]", employeeDateOfJoin, payrollStartDate };

        // STEP 3: Retrieve salary payslip details
        const salaryPayslipList = await organizationDbConnection(ehrTables.salaryPayslipExtended + ' as SP')
            .select(
                'SP.*',
                organizationDbConnection.raw(
                    "DATE_FORMAT(STR_TO_DATE(CONCAT(SP.Salary_Year, '-', SP.Salary_Month, '-01'), '%Y-%m-%d'), '%b, %Y') AS Salary_Period"
                ),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) AS Added_By"),
                organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) AS Updated_By")
            )
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'SP.Added_By')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'SP.Updated_By')
            .where('SP.Employee_Id', employeeId)
            .where('SP.Salary_Year', args.year)
            .whereIn('SP.Salary_Month', monthList)
            .orderBy('SP.Salary_Month', 'desc');

        const externalApiSyncDetail = await organizationDbConnection(ehrTables.externalApiSyncDetails).first()
        .where({  Integration_Type: syntrumValues.integrationType, Sync_Direction: 'Pull', Action: 'Retrieve',  Entity_Type: 'Refetch-Payslip', Status:'Active' });

        // Convert salaryPayslipList into a map for fast lookup
        const salaryPayslipMap = new Map(salaryPayslipList.map(s => [s.Salary_Month, s]));

        // STEP 4: Map the monthList to response
        const response = monthList.map(month => {

            let salaryPaySlip = salaryPayslipMap.get(month) || {
                Payslip_Id: null,
                Employee_Id: employeeId,
                Salary_Month: month,
                Salary_Year: args.year,
                Salary_Period: moment(month, 'MM').format('MMM') + ', ' + args.year,
                Total_Earnings: 0,
                Total_Deductions: 0,
                Net_Pay: 0,
                File_Path: null,
                Added_On: null,
                Added_By: null,
                Updated_On: null,
                Updated_By: null
            };
            salaryPaySlip.File_Path = !externalApiSyncDetail && salaryPaySlip?.File_Path ? salaryPaySlip.File_Path : null;
            return salaryPaySlip;
        });

        return { errorCode: "", message: "Salary Payslip details retrieved successfully.", payslipDetails: JSON.stringify(response), employeeDateOfJoin, payrollStartDate };
    } catch (error) {
        console.error('Error in listSalaryPayslip function:', error);
        let errResult = commonLib.func.getError(error, 'SYN0019');
        throw new ApolloError(errResult.message, errResult.code, { actualError: error?.message ? error.message : null });
    } finally {
        if (organizationDbConnection) {
            await organizationDbConnection.destroy();
        }
    }
};

/**
 * Retrieves a list of months for which salary payslip is available for given employee and year.
 * This function takes into account the employee's join date, payroll start date, and the current year.
 * If the current year is provided, it will only return months up to the current month.
 * @param {Object} organizationDbConnection - An instance of knex connection to the organization database.
 * @param {number} employeeId - The ID of the employee.
 * @param {Object} args - An object containing the year.
 * @param {Object} context - An object containing the organization code and other context information.
 * @returns {Promise<Object>} An object containing the list of months, employee date of join, and payroll start date.
 * @throws {Error} If the employee join date or payroll start date is missing.
 */
async function getMonthList(organizationDbConnection, employeeId, args, context) {
    try {
        const [employeeJob, orgDetails] = await Promise.all([
            organizationDbConnection(ehrTables.empJob).select('Date_of_Join').where('Employee_Id', employeeId).first(),
            organizationDbConnection(ehrTables.orgDetails).select('Payroll_Start_Date').first()
        ]);

        
        if (!employeeJob?.Date_of_Join || !orgDetails?.Payroll_Start_Date) {
            throw !employeeJob?.Date_of_Join ? 'SYN0026' : 'SYN0027'
        }

        const yearStart = moment(`${args.year}-01-01`, 'YYYY-MM-DD');
        const minMonth = moment.max([moment(employeeJob.Date_of_Join), yearStart, moment(orgDetails.Payroll_Start_Date)]).month() + 1;

        // let maxMonth = 12;
        // if (args.year === moment().year()) {
        //     const salaryMonthYear = `${moment().month() + 1},${moment().year()}`;
        //     const { Last_SalaryDate } = await commonLib.func.retrieveSalaryDates(
        //         context.Org_Code, salaryMonthYear, 0, moment().format('YYYY-MM-DD'), process.env.domainName, process.env.webAddress
        //     );

        //     if (!Last_SalaryDate) throw new Error("Last Salary Date not found.");

        //     maxMonth = moment(Last_SalaryDate, 'YYYY-MM-DD').isBefore(moment()) ? moment().month() + 1 : moment().month();
        // }

        // Max month should be the current month
        const maxMonth = moment().month() + 1;

        return {
            monthList: Array.from({ length: maxMonth - minMonth + 1 }, (_, i) => minMonth + i),
            employeeDateOfJoin: employeeJob.Date_of_Join,
            payrollStartDate: orgDetails.Payroll_Start_Date
        };
    } catch (error) {
        console.error("Error while getting month list:", error);
        throw error;
    }
}