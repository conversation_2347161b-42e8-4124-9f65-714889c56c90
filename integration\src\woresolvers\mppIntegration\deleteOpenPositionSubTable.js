// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { getWorkflowProcessInstanceId, getWorkflowProcessInstanceData, initaiteWorkflow } = require('../../common/commonFunction');
const { formId } = require('../../../common/appConstants');

module.exports.deleteOpenPositionSubTable = async (parent, args, context) => {
  let validationError = {};
  let organizationDbConnection;
  try {
    const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
    organizationDbConnection = knex(context.connection.OrganizationDb);
    // Destructure args for table keyword and ID
    const { deleteId, tableKeyword,positionRequestId,eventId,status } = args;

    let selectedTable;
    let idField;
    let messageKeyWord;

    // Use switch to map table keyword to actual table names and ID fields
    switch (tableKeyword) {
      case 'duties':
        selectedTable = ehrTables.mppDutiesResponsibilities;
        idField = 'Duties_Responsibility_Id';
        messageKeyWord='Duties & Responsibilities';
        break;
      case 'education':
        selectedTable = ehrTables.mppEducationRequirementsDescriptions;
        idField = 'Mpp_Education_Requirements_Descriptions_Id';
        messageKeyWord='Education Details';
        break;
      case 'experience':
        selectedTable = ehrTables.mppExperience;
        idField = 'Experience_Id';
        messageKeyWord='Experience Details';
        break;
      case 'workingConditions':
        selectedTable = ehrTables.mppWorkingConditions;
        idField = 'Working_Condition_Id';
        messageKeyWord='Working Conditions Details';
        break;
      default:
        throw 'EI00198';
    }

    // Check access rights for deletion
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId.newPosition
    );

    if (Object.keys(checkRights).length <= 0 || checkRights.Role_Update === 0) {
      throw '_DB0102';
    }
    let responseObject={}
    if(positionRequestId && eventId &&  status.toLowerCase() === "waiting for approval"){
    responseObject = await getWorkflowProcessInstanceId(organizationDbConnection, positionRequestId);
    }
    return await organizationDbConnection.transaction(async (trx) => {
      const result = await trx(selectedTable)
        .where(idField, deleteId) // Use the appropriate ID field
        .del();
        if (responseObject && responseObject[0]?.Process_Instance_Id) {
            const instanceDataArray = await getWorkflowProcessInstanceData(organizationDbConnection, responseObject[0].Process_Instance_Id, trx);
            const instanceData = JSON.parse(instanceDataArray[0].instance_data);
    
            if (eventId &&  status.toLowerCase() === "waiting for approval") {
              await initaiteWorkflow(eventId, instanceData, orgCode, formId.newPosition, organizationDbConnection, loginEmployeeId, trx);
              await commonLib.func.deleteOldApprovalRecords(organizationDbConnection, responseObject[0].Process_Instance_Id, trx);
            }
          }
      // Log the deletion action
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection: trx, // Pass the transaction object
        message: `Record with ID ${deleteId} from ${messageKeyWord} deleted successfully`,
      });

      return { errorCode: '', message: `New position request: ${messageKeyWord} deleted successfully.` };
    });
  } catch (error) {
    console.log('Error in deleteOpenPositionSubTable function main catch block.', error);
    if (error === 'IVE0000') {
      console.log('Validation error in deleteOpenPositionSubTable function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      const errResult = commonLib.func.getError(error, 'EI00199');
      throw new ApolloError(errResult.message, errResult.code);
    }
  } finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};
