//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require knex to make DB connection
const knex = require('knex')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
let moment = require('moment')
const axios = require('axios')
const { formId } = require('../../../common/appConstants')
//function to get Auth tokens

module.exports.getAuthTokenJobStreet = async (event, args, context, info) => {
  console.log('Inside getAuthToken function')
  try {
    let loginEmployeeId = context.Employee_Id;
    let organizationDbConnection = knex(context.connection.OrganizationDb);
    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false, args.form_Id);
    if (Object.keys(checkRights).length > 0 && (checkRights.Role_Add === 1 || checkRights.Role_Update === 1)&& (checkRights.Employee_Role.toLowerCase() === 'admin'|| args.isPublish)) {
      let { SeekClientID, SeekClientSecret } = await getUserData();
      if (SeekClientID && SeekClientSecret) {
        const url = process.env.jobStreetAuthTokenAPI;
        const requestBody = {
          audience: 'https://graphql.seek.com',
          client_id: SeekClientID,
          client_secret: SeekClientSecret,
          grant_type: 'client_credentials'
        }

        try {
          const response = await axios
            .post(url, requestBody, {
              headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'YourPartnerService/1.2.3'
              }
            })
            .catch((err) => {
              console.log('Error in getAuthTokenJobStreet catch block', err)
              if (err.response.status === 401) {
                console.log(
                  'unathorized error please check client id and credential are correct',
                  err
                )
                throw 'EI00141'
              } else {
                throw err
              }
            })
          if (!response || !response.data || !response.data.access_token) {
            throw 'EI00142'
          }
          let browserTokenResponse
          if (args.hirer_Id && args.hirer_Id.length) {
            browserTokenResponse = await getBrowserToken(
              args.hirer_Id,
              response.data.access_token,
              context.Org_Code
            ) // Assuming hirerId is defined somewhere

            if (!browserTokenResponse || !browserTokenResponse.data || !browserTokenResponse.data.access_token) {
              throw 'EI00145'
            }
          }

          return {
            errorCode: '',
            message: 'auth tokens retrieved successfully.',
            getData: {
              browserToken: browserTokenResponse?.data? JSON.stringify(browserTokenResponse?.data):null,
              accessToken: JSON.stringify(response.data)
            }
          }
        } catch (error) {
          console.error('Error in getAuthToken function catch block', error)
          return null
        }
      } else {
        console.log('there is no client id or secret id configured')
      }

      return {
        errorCode: '',
        message: 'jobSreet getAuthToken created successfully.',
        getData: tokens
      }
    }else{
      if(Object.keys(checkRights).length < 0 || (Object.keys(checkRights).length > 0 && checkRights.Role_Update !== 1)){
        console.log('No rights to update the recruitment integration Status');
        throw '_DB0111';
      }else{
        throw '_DB0109'
      }
    }
   
    
  } catch (mainCatchError) {
    console.log('Error in getAuthToken function main block', mainCatchError)
    let errResult = commonLib.func.getError(mainCatchError, 'EI00121')
    // return response
    throw new ApolloError(errResult.message, errResult.code)
  }
}

async function getUserData() {
  try {
    const AWS = require('aws-sdk')

    // Create client for secrets manager
    let client = new AWS.SecretsManager({
      region: process.env.region
    })
    // Get secrets from aws secrets manager
    let secretKeys = await client
      .getSecretValue({ SecretId: process.env.dbSecretName })
      .promise()
    secretKeys = JSON.parse(secretKeys.SecretString)
    let {SeekClientID,SeekClientSecret}=secretKeys;
    if(!SeekClientID || !SeekClientSecret){
      throw 'EI00163';
    }
    return {
      SeekClientID,
      SeekClientSecret
    }

  } catch (err) {
    console.log('Error in getUserData function main catch block')
    throw err
  }
}

async function getBrowserToken(hirerId, partnerToken, orgCode) {
const url =process.env.jobStreetAuthBrowserTokenAPI;
const requestBody = {
  hirerId: hirerId,
  scope:
    'query:ad-products mutate:application-questionnaires query:ontologies query:organizations query:position-profiles query:advertisement-brandings query:posted-position-profile-previews',
  userId: orgCode
}
const requestJson =requestBody;
const apiHeaders={
  Authorization: `Bearer ${partnerToken}`,
  'Content-Type': 'application/json',
  'User-Agent': 'YourPartnerService/1.2.3'
}
const config = {
  method: 'post', 
  url: url,
  maxBodyLength: Infinity,
  data : requestJson,
  headers: apiHeaders
};
  try {
    const response = await Promise.resolve(axios.request(config))
      .catch((err) => {
        console.log('Error in getBrowserToken function catch block', err)
        if (err.response.status === 400) {
          console.log('invalide seek hirerId', err)
          throw 'EI00143'
        } else {
          throw err
        }
      })

    if (!response || !Object.keys(response) || (response && response.error)) {
      throw 'EI00144'
    }

    const data = response;
    return data // Assuming the access token is what you want to retrieve
  } catch (error) {
    console.error('Error in getBrowserToken function main catch block', error)
    return error
  }
}
