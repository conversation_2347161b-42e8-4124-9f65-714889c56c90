const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require jsonwebtoken for encryption and decryption
let jwt = require('jsonwebtoken');
//Resolver function to encrypt and decrypt camu data according to the arguments.
let appmanagerDbConnection;
let organizationDbConnection;
module.exports.encryptDecryptCamuData = async (parent, args, context, info) => {
    console.log('Inside encryptDecryptcryptCamuData() function.');
    let validationError = {};
    let errResult;
    let employeeId=context.Employee_Id;
    let password;
    let camuToken = args.camuToken?args.camuToken:'-';
    let orgCode=context.Org_Code?context.Org_Code:null;
    try{
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the appmanager database connection
        appmanagerDbConnection = knex(context.connection.AppManagerDb);
        if(!employeeId && camuToken.length<=1){
            validationError['SIB0131'] = commonLib.func.getError('', 'SIB0131').message;
        }
        if(Object.keys(validationError).length === 0){
            if(orgCode)
            {
                password=await commonLib.employees.getCamuClientToken(appmanagerDbConnection,orgCode,organizationDbConnection,employeeId);
                if(camuIntegrationDetails.fieldForceEnabled){
                    password=camuIntegrationDetails['empCamuDetails'][employeeId][0]['Client_Access_Token'];
                }else{
                    password=camuIntegrationDetails['empCamuDetails']['Client_Access_Token'];
                }
                appmanagerDbConnection?appmanagerDbConnection.destroy():null;
            }  
            let partnerId=context.partnerId &&context.partnerId.length>1?context.partnerId:'-';
            if(camuToken && camuToken.length>1)
            {
                let camuDecryptedData= await decryptData(camuToken,password);
                if( camuDecryptedData && Object.keys(camuDecryptedData).length > 0)
                {
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return {errorCode:'',message:'camuToken decrypted successfully.',decryptedData: camuDecryptedData}; 
                }
                else{

                    throw 'SIB0138';
                }
            }
            else if(partnerId && partnerId!=='-')
            {
                let camuData=await getCamuDataToBeEncrypted(organizationDbConnection,employeeId);
                if(camuData && Object.keys(camuData).length > 0)
                {
                    camuData=JSON.parse(JSON.stringify(camuData));
                    let camuEncryptedData= await encryptData(camuData,password);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    if(camuEncryptedData)
                    {
                        return {errorCode:'',message:'Camu Data encrypted successfully.',encryptedToken: camuEncryptedData};
                    }
                    else{
                        throw 'SIB0137';
                    }
                   
                }
                else{
                    throw 'SIB0137';
                }
            }
            else{
                console.log('Empty partner id.',partnerId);
                throw 'SIB0128';
            }
        }else{
            throw 'IVE0000';
        }
    }catch(mainCatchError){
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in the encryptDecryptCamuData() main catch block.',mainCatchError);
        if(mainCatchError==='IVE0000'){
            console.log('Validation error in the encryptDecryptCamuData() function.',validationError);
            errResult = commonLib.func.getError('',mainCatchError);
            throw new UserInputError(errResult.message,{validationError: validationError});
        }
        else if(mainCatchError==='SIB0138' || mainCatchError==='SIB0137' || mainCatchError==='SIB0128'){
            errResult = commonLib.func.getError(mainCatchError,mainCatchError);
            throw new ApolloError(errResult.message, errResult.code);
        }
        else{
            errResult = commonLib.func.getError('SIB0139','SIB0139');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}

async function encryptData(data,password)
{
    try{
        let token = jwt.sign(data, password, { expiresIn: '30s' });
        return token;
    }
    catch(e){
        console.log("Error Occurred while encrypting the camu data",e);
        return "";
    }
}

async function decryptData(encryptedData,password)
{
    try{
        let decodedData = jwt.verify(encryptedData,password);
        return decodedData;
    }
    catch(e){
        console.log("Error Occurred while deCrypting the camu data",e);
        return "";
    }
}

async function getCamuDataToBeEncrypted(organizationDbConnection,employeeId)
{
    try{
        return(
            organizationDbConnection(ehrTables.empJob)
            .select('EJ.Camu_Id as uniqueID','EJ.User_Defined_EmpId as staffId',organizationDbConnection.raw('CASE WHEN EPI.Work_Email IS NOT NULL AND EPI.Work_Email !="" THEN EPI.Work_Email ELSE EPI.Personal_Email END as email'))
            .from(ehrTables.empJob +' as EJ')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI','EJ.Employee_Id','EPI.Employee_Id')
            .where('EJ.	Employee_Id',employeeId)
            .then(data=>{
                if(data[0] && data[0]['email'] && data[0]['staffId'] && data[0]['uniqueID'])
                {
                    return data[0];
                }
                else
                {
                    return "";
                }
            })
            .catch(e=>{
                console.log("Error Occurred while getting camu data for encryption",e);
                return "";
            })
        )
    }
    catch(e){
        console.log("Error in the getCamuDataToBeEncrypted function main catch block.",e);
        return "";
    }
}
