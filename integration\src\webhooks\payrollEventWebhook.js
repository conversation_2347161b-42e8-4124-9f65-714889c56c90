const { syntrumValues } = require('../../common/appConstants');
const { ehrTables } = require('../../common/tablealias');
const { getConnection } = require("../stepFunction/commonFunctions");
const { deleteFileFromS3Bucket } = require('../common/commonFunction');
const { generateSyntrumPayslip } = require('../woresolvers/syntrum/generateSyntrumPayslip');
const { generateSyntrumSalary } = require('../woresolvers/syntrum/generateSyntrumSalary');
const knex = require('knex');
const crypto = require("crypto");
const moment = require('moment');

// Create a constant for the secret key to avoid repeated hashing
const SECRET_KEY = crypto.createHash("sha256").update(process.env.decryptionSecretKeyName).digest();
let requestBody = null

/**
 * Creates an error response object with a given status code and message.
 *
 * @param {number} statusCode - The HTTP status code for the error response.
 * @param {string} message - A message describing the error.
 * @param {Error|null} [error=null] - An optional error object to include additional error details.
 * @returns {Object} An object containing the status code and a JSON stringified body with success, message, and optional error details.
 */

async function createErrorResponse(statusCode, message, error = null, organizationDbConnection = null) {
    //Log the error if organizationDbConnection exists
    if (statusCode !== 401 && statusCode !== 503 && organizationDbConnection) {
        const errorJSON = {
            "statusCode": statusCode,
            "message": message + ' - ' + (error?.message || error),
        }
        await organizationDbConnection(ehrTables.externalAPISyncStatus)
            .insert({
                'Entity_Type': 'Syntrum Payroll Webhook',
                'Entity_Id': requestBody?.employeeCode,
                'Integration_Type': syntrumValues.integrationType,
                'Failure_Reason': errorJSON,
                'Form_Data': requestBody,
                'Status': 'Failed',
                'Added_On': moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                'Added_By': 1
            })
    }

    return {
        statusCode,
        body: JSON.stringify({
            success: false,
            message,
            ...(error && { error: error.message || error })
        })
    };
};


/**
 * Decrypts an API key that is encrypted with a specified encryption algorithm.
 *
 * This function decodes the provided base64-encoded encrypted key, extracts the 
 * initialization vector (IV), authentication tag, and the encrypted data. It then 
 * uses the SECRET_KEY and cipher algorithm specified in the environment variables 
 * to decrypt the data.
 *
 * @param {string} encryptedKey - The base64-encoded string containing the encrypted 
 *                                API key, IV, and authentication tag.
 * @returns {string} The decrypted API key in UTF-8 format.
 * @throws Will throw an error if the decryption process fails.
 */

const decryptApiKey = (encryptedKey) => {
    try {
        const data = Buffer.from(encryptedKey, "base64");
        const iv = data.slice(0, 12);
        const authTag = data.slice(12, 28);
        const encrypted = data.slice(28);

        const decipher = crypto.createDecipheriv(
            process.env.decryptionAlgorithm,
            SECRET_KEY,
            iv
        );
        decipher.setAuthTag(authTag);

        const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
        return decrypted.toString("utf8");
    } catch (error) {
        console.error("API Key Decryption Error:", error);
        throw new Error("Invalid API Key");
    }
};

/**
 * Validates the request body of the payroll event webhook.
 *
 * This function checks for the presence of all required fields, and
 * validates the values of the event and status fields. If any of the
 * checks fail, an error is thrown.
 *
 * @param {Object} requestBody - The request body of the webhook
 * @returns {{eventValue: string, statusValue: string}} - The validated event and status values
 * @throws {Error} If any of the required fields are missing, or if the event or status values are invalid
 */
const validateInputs = (requestBody) => {
    try {
        const requiredFields = ['employeeCode', 'event', 'status', 'transactionMonthYear'];

        // Check for missing fields
        const missingFields = requiredFields.filter(field => !requestBody[field]);
        if (missingFields.length) {
            const error = new Error(`Missing required fields: ${missingFields.join(', ')}`);
            error.statusCode = 400;
            throw error;
        }

        // Validate event and status
        const eventValue = requestBody.event.toLowerCase();
        const statusValue = requestBody.status.toLowerCase();

        if (!syntrumValues.payrollEvents.includes(eventValue)) {
            const error = new Error(`Invalid Event: ${eventValue}`);
            error.statusCode = 400;
            throw error;
        }

        if (!syntrumValues.payrollStatusMapping[eventValue].includes(statusValue)) {
            const error = new Error(`Invalid Status: ${statusValue}`);
            error.statusCode = 400;
            throw error;
        }

        // Validate transaction month year
        if (!moment(requestBody.transactionMonthYear, "YYYY-MM-DD", true).isValid()) {
            const error = new Error("Invalid Transaction Month Year");
            error.statusCode = 400;
            throw error;
        }

        return { eventValue, statusValue };
    } catch (error) {
        console.error('Error in validateInputs', error);
        throw error;
    }
};


/**
 * Processes payroll events by invoking the appropriate handler based on the event type.
 *
 * This function maps the eventValue to a corresponding handler function that updates
 * the payslip status, generates salary, or updates air ticketing status. It throws an
 * error if the event type is unsupported.
 *
 * @param {Object} organizationDbConnection - The database connection object for the organization.
 * @param {string} eventValue - The type of payroll event to process (e.g., 'payslip', 'salary', 'air ticketing').
 * @param {Object} args - An object containing necessary arguments for handling the event.
 * @param {Object} context - An object containing additional context information.
 * @param {string} statusValue - The status value associated with the event.
 * @throws {Error} Will throw an error if the event type is unsupported.
 */

const processPayrollEvent = async (organizationDbConnection, eventValue, args, context, statusValue) => {
    try {
        const eventHandlers = {
            'payslip': async () => {
                await updatePayslipStatus(organizationDbConnection, args, statusValue, context);
            },
            'salary': async () => {
                await generateSyntrumSalary(organizationDbConnection, context);
            },
            'air ticketing': async () => {
                await updateAirTicketingStatus(organizationDbConnection, args, statusValue);
            }
        };

        const handler = eventHandlers[eventValue];
        if (!handler) {
            throw new Error(`Unsupported event: ${eventValue}`);
        }

        await handler();
    } catch (error) {
        console.error('Error in processPayrollEvent', error);
        throw error;
    }
};

/**
 * Handles payroll events from Syntrum and updates corresponding records in the database.
 *
 * This function validates the API key, database connection, request body, and employee
 * before processing the event. It returns a 200 response for successful events or a 500
 * response with an error message for failed events.
 *
 * @param {Object} event - The API Gateway event object containing the request headers, body, and other information.
 * @throws {Error} Will throw an error if the API key is invalid, the database connection fails, the request body is invalid, or the employee is not found.
 * @returns {Object} A response object with a status code and a JSON stringified body.
 */
async function payrollEventWebhook(event) {
    let organizationDbConnection;
    try {
        // API Key Validation
        const apiKey = event.headers?.['X-API-KEY'];
        if (!apiKey) {
            return createErrorResponse(401, "Unauthorized: Missing API Key");
        }

        const decryptedKey = decryptApiKey(apiKey);
        console.log("Decrypted API Key:", decryptedKey);
        const [orgCode, ...rest] = decryptedKey.split('_');
        if (rest.length !== 2) {
            return createErrorResponse(401, "Unauthorized: Invalid API Key");
        }

        // Database Connection
        const connection = await getConnection(
            process.env.stageName,
            process.env.dbPrefix,
            process.env.dbSecretName,
            process.env.region,
            orgCode
        );
        if (!connection || Object.keys(connection).length === 0) {
            return createErrorResponse(503, "Database Connection Failed");
        }

        // Get Organization DB Connection
        organizationDbConnection = knex(connection.OrganizationDb);

        // Parse and Validate Request Body
        requestBody = JSON.parse(event.body);
        const { eventValue, statusValue } = validateInputs(requestBody);

        // Validate Employee
        const employeeId = await getEmployeeId(organizationDbConnection, requestBody.employeeCode);
        if (!employeeId) {
            return await createErrorResponse(400, "Invalid Employee Code / Employee Not Found", null, organizationDbConnection);
        }

        // Prepare Context and Arguments
        const context = {
            Employee_Id: employeeId,
            Org_Code: orgCode
        };

        const args = {
            employeeId,
            month: moment(requestBody.transactionMonthYear, "YYYY-MM-DD").month() + 1,
            year: moment(requestBody.transactionMonthYear, "YYYY-MM-DD").year()
        };

        // Process Event
        await processPayrollEvent(organizationDbConnection, eventValue, args, context, statusValue);

        return {
            statusCode: 200,
            body: JSON.stringify({ success: true, message: "Status updated successfully" })
        };
    } catch (error) {
        console.error('Payroll Event Webhook Error:', error);
        return await createErrorResponse(error?.statusCode ? error.statusCode : 500, "Internal Server Error", error, organizationDbConnection);
    }
    finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}

/**
 * Retrieves the Employee Id for a given Employee Code from the ehrTables.empJob table.
 * @param {knex} organizationDbConnection - A Knex instance connected to the organization database.
 * @param {string} employeeCode - The User Defined Employee Id to lookup.
 * @returns {Promise<number | null | undefined>} The Employee Id if found, or null/undefined if not.
 */
async function getEmployeeId(organizationDbConnection, employeeCode) {
    try {
        const employee = await organizationDbConnection(ehrTables.empJob)
            .where('User_Defined_EmpId', employeeCode)
            .select('Employee_Id')
            .first();

        return employee?.Employee_Id;
    }
    catch (error) {
        console.error('Error in getEmployeeId', error);
        throw error;
    }
}

/**
 * Updates the Air Ticket Settlement Summary table to reflect the new status of
 * the Air Ticket for the given Employee Id and Payroll Month.
 * @param {knex} organizationDbConnection - A Knex instance connected to the organization database.
 * @param {Object} args - An object containing the Employee Id and Payroll Month/Year details.
 * @param {string} statusValue - The new status of the Air Ticket.
 * @returns {Promise<void>} - Resolves if the update is successful, rejects with an error if not.
 */
async function updateAirTicketingStatus(organizationDbConnection, args, statusValue) {
    try {
        const settlementStatus = statusValue === 'settled' ? 'Settled' : 'Yet to be Settled';
        await organizationDbConnection(ehrTables.airTicketSettlementSummary)
            .update('Settlement_Status', settlementStatus)
            .where('Employee_Id', args.employeeId)
            .where('Payroll_Month', moment(`${args.year}-${args.month}`, "YYYY-M").format("YYYY-MM"));
    } catch (error) {
        console.error('Error in updateAirTicketingStatus', error);
        throw error;
    }
}

/**
 * Updates the Syntrum Payslip record in the salaryPayslipExtended table for the given Employee Id and Payroll Month.
 * If the statusValue is 'create', it generates a new Syntrum Payslip record.
 * If the statusValue is 'delete', it deletes the existing Syntrum Payslip record and its associated PDF file from the S3 bucket.
 * @param {knex} organizationDbConnection - A Knex instance connected to the organization database.
 * @param {Object} args - An object containing the Employee Id and Payroll Month/Year details.
 * @param {string} statusValue - The status of the Syntrum Payslip to update (create or delete).
 * @param {Object} context - An object containing additional context information.
 * @returns {Promise<void>} - Resolves if the update is successful, rejects with an error if not.
 */
async function updatePayslipStatus(organizationDbConnection, args, statusValue, context) {
    try {
        if (statusValue === 'create') {
            await generateSyntrumPayslip(organizationDbConnection, args, context, 'WebHook');
        } else if (statusValue === 'delete') {
            await deleteSyntrumPayslip(organizationDbConnection, args, context.Org_Code);
        }
    } catch (error) {
        console.error('Error in updatePayslipStatus', error);
        throw error;
    }
}

/**
 * Deletes the Syntrum Payslip record from the salaryPayslipExtended table for the given Employee Id and Payroll Month.
 * If the record exists and has a File_Path, it also deletes the corresponding PDF file from the S3 bucket.
 * @param {knex} organizationDbConnection - A Knex instance connected to the organization database.
 * @param {Object} args - An object containing the Employee Id, Payroll Month, and Payroll Year details.
 * @returns {Promise<void>} - Resolves if the delete is successful, rejects with an error if not.
 */
async function deleteSyntrumPayslip(organizationDbConnection, args, orgCode) {
    try {
        const deletedRecord = await organizationDbConnection(ehrTables.salaryPayslip)
            .select('*')
            .where('Employee_Id', args.employeeId)
            .where('Salary_Month', args.month+","+args.year)
            .first();

        if (deletedRecord?.S3_FileName) {
            await organizationDbConnection(ehrTables.salaryPayslip)
                .where('Employee_Id', args.employeeId)
                .where('Salary_Month', args.month+","+args.year)
                .del();

            const filePath = `${process.env.domainName}/${orgCode}/Salary Payslip/${args.year}/Monthly/${deletedRecord.S3_FileName}.pdf`;
            await deleteFileFromS3Bucket(filePath, process.env.documentsBucket);
        }
    }
    catch (error) {
        console.error('Error in deleteSyntrumPayslip', error);
        throw error;
    }
}

module.exports = { payrollEventWebhook };