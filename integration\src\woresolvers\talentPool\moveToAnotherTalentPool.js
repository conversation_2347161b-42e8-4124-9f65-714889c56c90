// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');

module.exports.moveToAnotherTalentPool = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside moveToAnotherTalentPool function.");
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        if(!args.candidateId || !args.talentPoolId){
            throw 'IVE0000'
        }
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
        
            let [candidateData, talentPool] = await Promise.all([ 
                 organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
                .select('CPI.Talent_Pool_Id', 'CRI.Archived', 'TP.Talent_Pool', organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Last_Name) as candidateName"))
                .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                .leftJoin(ehrTables.talentPool + ' as TP', 'TP.Talent_Pool_Id', 'CPI.Talent_Pool_Id')
                .where('CPI.Candidate_Id', args.candidateId).first(),

                organizationDbConnection(ehrTables.talentPool).select('Talent_Pool')
                .where('Talent_Pool_Id', args.talentPoolId).first() 
            ]);

            if(!talentPool){
                throw 'TAP0112'; //Unable to transfer details to the specified talent pool as it has been deleted.
            }

            if (!candidateData || candidateData?.Archived.toLowerCase() === 'yes' || !candidateData?.Talent_Pool_Id || candidateData?.Talent_Pool_Id === args.talentPoolId) {
                throw candidateData ? 
                    candidateData?.Archived.toLowerCase() === 'yes' ? 'TAP0114' : //Apologies! The candidate details have already been archived in a different user session.
                    !candidateData?.Talent_Pool_Id ? 'EI00211' : //Oops! An error occurred while transfering the talent pool. Please try again later.
                    'EI00210' : ////The candidate is already part of the selected talent pool. Please choose a different talent pool.
                    'EI00224'; //Sorry! An error occurred while processing the candidate details. Please try again. 
            }
    
            await organizationDbConnection(ehrTables.candidatePersonalInfo)
            .update({ Talent_Pool_Id: args.talentPoolId }).where('Candidate_Id', args.candidateId);

            await commonLib.func.createSystemLogActivities({
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                changedData: args,
                organizationDbConnection: organizationDbConnection,
                formId: args.formId,
                action: 'Moved Pool',
                isEmployeeTimeZone: 0,
                uniqueId: args.candidateId,
                message: `The candidate has been moved from ${candidateData.Talent_Pool} to ${talentPool.Talent_Pool} talent pool.`
            });
    
            return { errorCode: '', message: 'The candidate has been successfully transferred from the one talent pool to another talent pool.' };

        }else{
            throw '_DB0102'; //This employee do not have edit access rights
        }

    } catch (e) {
        console.error('Error while moveToAnotherTalentPool main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EI00211'); //Oops! An error occurred while transfering the talent pool. Please try again later.
        throw new ApolloError(errResult.message, errResult.code);
    }finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null; 
    }
}
