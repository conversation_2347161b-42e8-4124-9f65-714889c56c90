const camuCreateStaff = require('./roresolvers/camuIntegration/camuCreateStaff');
const encryptDecryptCamuData = require('./roresolvers/camuIntegration/encryptDecryptCamuData')
const getStaffSchedulesByDateRange = require('./roresolvers/camuIntegration/getStaffSchedulesByDateRange')
const getCamuStatus = require('./roresolvers/camuIntegration/getCamuStatus')
const jobBoardIntegrationStatus = require('./roresolvers/irukkaIntegration/jobBoardIntegrationStatus.js');
const getLocationDetails = require('./roresolvers/irukkaIntegration/getLocationDetails')
const getCompanySignUpDetails = require('./roresolvers/irukkaIntegration/getCompanySignUpDetails');
const getAWSCognitoIdentitiesResponse = require('./roresolvers/irukkaIntegration/getAWSCognitoIdentities.js')
const getIndeedAuthCredentials = require('./roresolvers/indeedIntegration/getIndeedAuthCredentials.js');
const getIndeedAuthToken = require('./roresolvers/indeedIntegration/getIndeedAuthToken.js');
const retrieveIndeedJobPostDetails = require('./roresolvers/indeedIntegration/retrieveIndeedJobPostDetails.js');
const retrieveJobStreetJobPostDetails = require('./roresolvers/jobStreetIntegration/retrieveJobStreetJobPostDetails.js');
const getAuthTokenJobStreet = require('./roresolvers/jobStreetIntegration/getAuthTokenJobStreet.js');
const verifyJobStreetRelation = require('./roresolvers/jobStreetIntegration/verifyJobStreetRelation.js');
const getJobStreetHirerList = require('./roresolvers/jobStreetIntegration/getJobStreetHirerList.js');
const getEncryptionKey = require('./roresolvers/indeedIntegration/getEncryptionKey.js');
const listForecastPosition = require('./roresolvers/mppIntegration/listForecastPosition.js');
const listHiringForecast = require('./roresolvers/mppIntegration/listHiringForecast.js');
const retrieveForeCastSettings = require('./roresolvers/mppIntegration/retrieveForeCastSettings.js');
const retrieveEmployeeRoleEmail = require('./roresolvers/mppIntegration/retrieveEmployeeRoleEmail.js');
const listDetailsBasedOnOrgPosId = require('./roresolvers/mppIntegration/listDetailsBasedOnOrgPosId.js');
const listReqruitmentRequest = require('./roresolvers/mppIntegration/listReqruitmentRequest.js');
const listDetailsBasedOnGroupCode = require('./roresolvers/mppIntegration/listDetailsBasedOnGroupCode.js');
const listNewPositionRequest = require('./roresolvers/mppIntegration/listNewPositionRequest.js');
const retrieveNewPositionDetails = require('./roresolvers/mppIntegration/retrieveNewPositionDetails.js');
const retrievePositionJobSummary = require('./roresolvers/sfapiIntegration/retrievePositionJobSummary.js');
const retrieveEducationLabelList = require('./roresolvers/mppIntegration/retrieveEducationLabelList.js');
const getTalentPoolList = require('./roresolvers/talentPool/getTalentPoolList.js');
const retrievePositionLevel = require('./roresolvers/mppIntegration/retrievePositionLevel.js');
const retrieveRecruitmentSettings = require('./roresolvers/mppIntegration/retrieveRecruitmentSettings.js');
const getArchiveReasonsList = require('./roresolvers/talentPool/getArchiveReasonsList.js');
const retrieveMicrosoftIntegration = require('./roresolvers/microsoft/retrieveMicrosoftIntegration.js');
const listSalaryPayslip = require('./roresolvers/syntrum/listSalaryPayslip.js');
const retrieveWorkflowApprovalSetting = require('./roresolvers/mppIntegration/retrieveWorkflowApprovalSetting.js');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        camuCreateStaff,
        encryptDecryptCamuData,
        getStaffSchedulesByDateRange,
        getCamuStatus,
        jobBoardIntegrationStatus,
        getLocationDetails,
        getCompanySignUpDetails,
        getAWSCognitoIdentitiesResponse,
        getIndeedAuthCredentials,
        getIndeedAuthToken,
        retrieveIndeedJobPostDetails,
        retrieveJobStreetJobPostDetails,
        getAuthTokenJobStreet,
        verifyJobStreetRelation,
        getJobStreetHirerList,
        getEncryptionKey,
        listForecastPosition,
        listHiringForecast,
        retrieveForeCastSettings,
        retrieveEmployeeRoleEmail,
        listDetailsBasedOnOrgPosId,
        listReqruitmentRequest,
        listDetailsBasedOnGroupCode,
        listNewPositionRequest,
        retrieveNewPositionDetails,
        retrievePositionJobSummary,
        retrieveEducationLabelList,
        getTalentPoolList,
        retrievePositionLevel,
        retrieveRecruitmentSettings,
        getArchiveReasonsList,
        retrieveMicrosoftIntegration,
        listSalaryPayslip,
        retrieveWorkflowApprovalSetting
    )
}
exports.resolvers = resolvers;
