// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appConstants');


let organizationDbConnection;
module.exports.jobBoardIntegrationStatus = async (parent, args, context, info) => {
    try {
        console.log("Inside jobBoardIntegrationStatus function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, null, '', 'UI', false, args.form_Id);
        let adminAccessRights = (args.form_Id == 241) ? (checkRights.Employee_Role.toLowerCase() === 'admin'): 1;
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            if(Object.keys(checkRights).length > 0 && adminAccessRights){
                return (
                    await organizationDbConnection(ehrTables.recruitmentIntegration)
                    .select('*')
                    .then((data) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Job board integration status retrieved successfully.", getStatus: data };
                    })
                    .catch((err) => {
                        console.log('Error in jobBoardIntegrationStatus .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'SET0101');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
                )
            }
            else{
                throw '_DB0109'
            }
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        console.log('Error in jobBoardIntegrationStatus function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'SET0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
