//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
const { getConnection } = require('../../stepFunction/commonFunctions')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
const axios = require('axios')
//Require knex to make DB connection
const knex = require('knex')
//Require table alias
const { ehrTables } = require('../../../common/tablealias')
const moment = require('moment')
const { candidateProfile } = require('../../queries/jobStreetQueries')
const {
  getUserData,
  getJobStreetAccessToken,
  fetchAndUploadFile
} = require('../../common/commonFunction')

//List the work schedule details in the work schedule form
module.exports.asyncJobStreetWebHookFunction = async (args) => {
  console.log('Inside asyncJobStreetWebHookFunction function started ', args)
  let organizationDbConnection
  try {
    console.log(
      ' asyncJobStreetWebHookFunction args value => ',
      args.status.response.events
    )
    const query = candidateProfile
    if (
      args &&
      args.status.response
    ) {
      let eventsjson = JSON.parse(args.status.response)
      if (eventsjson.events && eventsjson.events.length) {
        // Correct way to access the events array
        let events = eventsjson.events

        console.log(events, 'events')
        let insertArray = []
       const GRAPHQL_ENDPOINT = process.env.jobStreetTokenAPI
        let deletionArray = []
        for (element of events) {
          const candidateApplicationProfileId =
            element.candidateApplicationProfileId
          const variables = {
            id: candidateApplicationProfileId
          }
          let { SeekClientID, SeekClientSecret } = await getUserData();
          const ACCESS_TOKEN = await getJobStreetAccessToken(
            SeekClientID,
            SeekClientSecret
          )
          const apiHeaders = {
            Authorization: `Bearer ${ACCESS_TOKEN.access_token}`,
            'Content-Type': 'application/json',
            'User-Agent': 'YourPartnerService/1.2.3'
          }
          const config = {
            method: 'post',
            url: GRAPHQL_ENDPOINT,
            maxBodyLength: Infinity,
            data: JSON.stringify({
              query: query,
              variables: variables
            }),
            headers: apiHeaders
          }
          let response = await axios.request(config).catch(err=>{
            console.log(err.response.data.data,"errpprppr")
          })
          if (
            response.data.data &&
              response.data.data.errors &&
              response.data.data.errors.length
          ) {
            console.log(
              'Error in addUpdateJobPostJobStreet function catch block.',
              response.data.data.errors
            )
            let errorCode
            if (response.data.data && response.data.data.errors) {
              errorCode = await mapErrorToCustomCodeSeek(response.data.data)
            }
            if (errorCode === 'CH0001') {
              console.log('unathentcated request check the tokens')
            } else if (errorCode === 'CH0003') {
              console.log('check the validations bad_input error')
            }

            throw 'EI00152'
          }
          let orgCode;
          let jobPostId;
          if (
            response &&
            response.data.data.candidateProfile.candidate &&
            response.data.data.candidateProfile.associatedPositionProfile &&
            response.data.data.candidateProfile.associatedPositionProfile
              .seekHirerJobReference &&
            response.data.data.candidateProfile.associatedPositionProfile
              .seekHirerJobReference.length > 0
          ) {
            const seekHirerJobReferenceArray =
              response.data.data.candidateProfile.associatedPositionProfile
                .seekHirerJobReference
            orgCode = seekHirerJobReferenceArray.split('-')[0];
            jobPostId=seekHirerJobReferenceArray.split('-')[1]
          } else {
            throw 'EI00153'
          }
          let candidate = response.data.data.candidateProfile.candidate
          let resumeFile = null
          let databaseConnection = await getConnection(
            process.env.stageName,
            process.env.dbPrefix,
            process.env.dbSecretName,
            process.env.region,
            orgCode
          )
          let organizationDbConnection
          // check whether data exist or not
          if (databaseConnection && Object.keys(databaseConnection).length) {
            organizationDbConnection = knex(databaseConnection.OrganizationDb)
            return (
              organizationDbConnection
                .transaction(function (trx) {
                  let candidatePersonalInfoObject = {
                    Emp_First_Name: candidate?.person?.name?.given,
                    Emp_Last_Name: candidate?.person?.name?.family,
                    Nationality: args.nationality,
                    Personal_Email:
                      response.data.data.candidateProfile?.candidate?.person
                        ?.communication?.email[0]?.address,
                    Source_Type: 'ATS',
                    Added_On: moment.utc().format('YYYY-MM-DD'),
                    Added_By: 1
                  }
                  return (
                    organizationDbConnection('candidate_personal_info')
                      .insert(candidatePersonalInfoObject)
                      .transacting(trx)
                      .then(async (data) => {
                        var lastInsertedId = data[0]
                        let candidateContactDetailsObject = {
                          Candidate_Id: lastInsertedId,
                          Mobile_No:
                            response.data.data.candidateProfile?.candidate?.person
                              ?.communication?.phone[0]?.formattedNumber,
                          pCity:
                            response.data.data.candidateProfile?.candidate?.person?.communication?.address[0]?.formattedAddress?.split(
                              ' '
                            )[0],
                          pState:
                            response.data.data.candidateProfile?.candidate?.person?.communication?.address[0]?.formattedAddress?.split(
                              ' '
                            )[1],
                          pCountry:
                            response.data.data.candidateProfile?.candidate?.person
                              ?.communication?.address[0]?.countryCode,
                          pPincode:
                            response.data.data.candidateProfile?.candidate?.person?.communication?.address[0]?.formattedAddress?.split(
                              ' '
                            )[2]
                        }
                        if (
                          response.data.data &&
                          response.data.data.candidateProfile.attachments &&
                          response.data.data.candidateProfile.attachments.length
                        ) {
                          let domainName = process.env.domainName
                          const resumeAttachment =
                            response.data.data.candidateProfile.attachments.find(
                              (attachment) =>
                                attachment.seekRoleCode === 'Resume'
                            )
                          const resumeUrl = resumeAttachment
                            ? resumeAttachment.url
                            : null
                          const bucketName = process.env.documentsBucket
                          const formattedDate = moment().format(
                            'YYYY-MM-DD.HH:mm:ss'
                          )
                          let name = candidate?.person?.name?.given || lastInsertedId;
                          let finalFileName = `resume?${formattedDate}??${name}`;
                          const fileKey =
                            domainName +
                            '_' +
                            '/' +
                            orgCode +
                            '/resume/' +
                            finalFileName;
                          let uploadedresult = await fetchAndUploadFile(
                            resumeUrl,
                            fileKey,
                            bucketName,
                            ACCESS_TOKEN.access_token
                          )
                          if (!uploadedresult) {
                            resumeFile = null
                          }
                          resumeFile = finalFileName+uploadedresult;
                          const otherAttachment = response.data.data.candidateProfile.attachments.filter(
                            (attachment) => attachment.seekRoleCode !== 'Resume'
                          );
                          if (otherAttachment && otherAttachment.length) {
                            let otherAttachmentArray = [];
                            for (const element of otherAttachment) {
                              try {
                                const finalFileName = `${element.seekRoleCode}?${formattedDate}??${name}`;
                                const url = element?.url;
                                  const fileKey =
                                  domainName +
                                  '_' +
                                  '/' +
                                  orgCode +
                                  '/otherAttachment/' +
                                  finalFileName;
                                const uploadedResult = await fetchAndUploadFile(
                                  url,
                                  fileKey,
                                  bucketName,
                                  ACCESS_TOKEN.access_token
                                );
                                  const otherAttachmentFile =
                                    uploadedResult !== null &&
                                    uploadedResult !== undefined
                                      ? `${finalFileName}${uploadedResult}`
                                      : null
                                otherAttachmentArray.push({
                                  Candidate_Id: lastInsertedId,
                                  Candidate_Other_Attachments_Id: 0,
                                  Document_Name:element.seekRoleCode,
                                  Source: 'Seek',
                                  Added_On: moment.utc().format('YYYY-MM-DD'),
                                  Added_By: 1,
                                  File_Name: otherAttachmentFile,
                                });
                              } catch (err) {
                                console.error('Error processing attachment:', err);
                              }
                            }
                          
                            if (otherAttachmentArray.length > 0) {
                              try {
                                await addCandidateAttachmentInfo(
                                  organizationDbConnection,
                                  trx,
                                  otherAttachmentArray
                                );
                              } catch (err) {
                                console.error('Error adding candidate attachment info:', err);
                              }
                            }
                          }
                        } else {
                          throw 'EI00153'
                        }
                        /**insert candidate's contact details */
                        return organizationDbConnection(
                          'candidate_contact_details'
                        )
                          .insert(candidateContactDetailsObject)
                          .transacting(trx)
                          .then(async () => {
                          await Promise.all([
                              insertEducationDetails(
                                organizationDbConnection,
                                response,
                                lastInsertedId,
                                trx
                              ),
                              addCandidateCertificationDetails(
                                organizationDbConnection,
                                response,
                                lastInsertedId,
                                trx
                              ),
                              addCandidateSkillDetails(
                                organizationDbConnection,
                                response,
                                lastInsertedId,
                                trx
                              ),
                              insertCandidateExperienceDetails(
                                organizationDbConnection,
                                response,
                                lastInsertedId,
                                trx
                              ),
                              addCandidateCareerInfo(
                                organizationDbConnection,
                                args,
                                lastInsertedId,
                                trx,
                                resumeFile,
                                jobPostId,
                                candidateApplicationProfileId,
                                response
                              )
                            ])
                            return {
                              errorCode: '',
                              message: 'Candidate details added successfully.',
                              validationError: null,
                            }
                          })
                      }).catch(async err=>{
                        console.log(candidateApplicationProfileId,'Error in the asyncJobStreetWebHookFunction() function main catch block.',err)
                        await organizationDbConnection('jobstreet_candidate')
                        .insert({
                          Jobstreet_Candidate_Profile_Id:candidateApplicationProfileId,
                          Added_By:1,
                          Added_On: moment.utc().format('YYYY-MM-DD')
                        })
                        .transacting(trx);
                      })
                      /**if the organizationDbConnection queries are executed successfully */
                      .then(trx.commit)
                      .catch(trx.rollback)
                  ) /**rollback if any error occurs */
                })

                /**return the success result to the user */
                .then(function (result) {
                  return result
                })

                /**check and return if any error occured */
                .catch(function (err) {
                  console.log(
                    'Error in insertJobCandidates .catch function',
                    err
                  )
                  let errResult = commonLib.func.getError(err, 'ATS0114')
                  throw new ApolloError(errResult.message, errResult.code)
                })
                /**close the database connection */
                .finally(() => {
                  organizationDbConnection.destroy()
                })
            )
          } else {
            console.log('Error while creating database connection')
          }
        }
        
      }
    }
  } catch (err) {
    console.error(
      'Error in the asyncJobStreetWebHookFunction() function main catch block. ',
      err
    )
    // destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null
    let errResult = commonLib.func.getError(err, '_UH0001')
    throw new ApolloError(errResult.message, errResult.code)
  }
}

async function addCandidateSkillDetails(
  organizationDbConnection,
  args,
  lastInsertedId,
  trx
) {
  try {
    let candidateSkills = args?.data?.data?.candidateProfile?.qualifications || []
    if (candidateSkills && candidateSkills.length > 0) {
      candidateSkills = candidateSkills.map((field) => ({
        Candidate_Id: lastInsertedId,
        Skills: field.competencyName
      }))
      return organizationDbConnection('candidate_skills')
        .insert(candidateSkills)
        .transacting(trx)
        .then(async (data) => {
          return true
        })
        .catch(function (err) {
          return err
        })
    } else {
      return true
    }
  } catch (err) {
    console.log('Error in addCandidateSkillDetails main catch() block', err)
    throw err
  }
}

async function insertCandidateExperienceDetails(
  organizationDbConnection,
  args,
  lastInsertedId,
  trx
) {
  try {
    let candidateExperience = args?.data?.data?.candidateProfile?.employment || []
    if (candidateExperience && candidateExperience.length > 0) {
      const candidateExperienceDetails =
        args?.data?.data?.candidateProfile?.employment?.flatMap((employment) =>
          employment.positionHistories.map((history) => {
            const startDate = moment(history.start, 'YYYY-MM')
            const endDate = history.end
              ? moment(history.end, 'YYYY-MM')
              : moment() // If no end date, use the current date
            const duration = moment.duration(endDate.diff(startDate))
            const years = duration.years()
            const months = duration.months()
            let durationString = ''
            if (years > 0) {
              durationString += `${years} years`
            }
            if (months > 0) {
              if (durationString) durationString += ', '
              durationString += `${months} months`
            }
            return {
              Candidate_Id: lastInsertedId,
              Prev_Company_Name: employment.organization.name,
              Designation: history.title,
              Start_Date: startDate.format('YYYY-MM-DD'),
              End_Date:
                endDate.format('YYYY-MM') === moment().format('YYYY-MM-DD')
                  ? 'Present'
                  : endDate.format('YYYY-MM-DD'),
              Duration: durationString,
              Years: years,
              Months: months
            }
          })
        )
      return organizationDbConnection('candidate_experience')
        .insert(candidateExperienceDetails)
        .transacting(trx)
        .then((data) => {
          return true
        })
        .catch(function (err) {
          return err
        })
    } else {
      return true
    }
  } catch (err) {
    console.log(
      'Error in insertCandidateExperienceDetails main catch() block',
      err
    )
    throw err
  }
}

async function addCandidateCertificationDetails(
  organizationDbConnection,
  args,
  lastInsertedId,
  trx
) {
  try {
    const certifications = args?.data?.data?.candidateProfile?.certifications || []

    if (certifications.length > 0) {
      const candidateCertification = certifications.map((field) => ({
        Candidate_Id: lastInsertedId,
        Certification_Name: field.name,
        Received_Date: field.issued
          ? moment(field.issued).format('YYYY-MM-DD')
          : null,
        Certificate_Received_From: field.issuingAuthority
          ? field.issuingAuthority.name
          : null
      }))
      return organizationDbConnection('candidate_certifications')
        .insert(candidateCertification)
        .transacting(trx)
        .then(async (data) => {
          return true
        })
    } else {
      return true
    }
  } catch (err) {
    console.log(
      'Error in addCandidateCertificationDetails main catch() block',
      err
    )
    throw err
  }
}
async function insertEducationDetails(
  organizationDbConnection,
  candidate,
  lastInsertedId,
  trx
) {
  try {
    const education = candidate?.data?.data?.candidateProfile?.education || []
    let candidateEducation = []
    if (education.length > 0) {
      for (const field of education) {
        for (const degree of field.educationDegrees) {
          // Loop through each degree
          const entry = {
            Candidate_Id: lastInsertedId,
            Specialisation: degree.name,
            Institute_Name: field.institution.name,
            Year_Of_Passing: degree.date
              ? moment(degree.date, 'YYYY-MM').year()
              : null,
            University: degree.name,
            Start_Date: null,
            End_Date: degree.date
              ? moment(degree.date, 'YYYY-MM').format('YYYY-MM-DD')
              : null
          }
          if (field.descriptions.length > 0) {
            entry.Grade = field.descriptions[0]
          }

          candidateEducation.push(entry)
        }
      }
      return organizationDbConnection('candidate_education')
        .insert(candidateEducation)
        .transacting(trx)
        .then(async (data) => {
          return true
        })
        .catch(function (err) {
          console.log('Error inserting education details:', err)
          throw err
        })
    } else {
      return true
    }
  } catch (err) {
    console.log('Error in insertEducationDetails main catch() block', err)
    throw err
  }
}

async function addCandidateCareerInfo(
  organizationDbConnection,
  args,
  lastInsertedId,
  trx,
  resumeFileName,
  jobPostId,
  candidateId,
  response
) {
  try {
  
    let payrollCountry = 'PHP'

    const payrollData = await organizationDbConnection(
      'payroll_general_settings'
    )
      .select('Payroll_Currency')
      .first()

    if (payrollData && payrollData.Payroll_Currency) {
      payrollCountry = payrollData.Payroll_Currency
    }

    let targetQuestion = `What is your expected monthly basic salary (in ${payrollCountry})?`

    const responses =
      response?.data?.data?.candidateProfile?.seekQuestionnaireSubmission
        ?.responses || []

    let expectedSalary = null
    for (const resp of responses) {
      const questionHtml = resp?.component?.questionHtml
      if (questionHtml && questionHtml.toLowerCase() === targetQuestion.toLowerCase()) {
        if (resp.answers && resp.answers.length > 0) {
          expectedSalary = resp.answers[0].answer
        }
        break
      }
    }

    /**insert candidate's career details */
    const convertedSalary = convertToDecimal(expectedSalary)
    const isValidDecimal = typeof convertedSalary === 'number'

    let currencyId = null

    const result = await organizationDbConnection('payroll_general_settings')
      .select('currency.Currency_Id')
      .innerJoin(
        'currency',
        'payroll_general_settings.Payroll_Currency',
        'currency.Currency_Code'
      )

    if (result && result.length > 0 && result[0].Currency_Id) {
      currencyId = result[0].Currency_Id
    }

    return organizationDbConnection('candidate_recruitment_info')
      .insert({
        Candidate_Id: lastInsertedId,
        Resume: resumeFileName,
        Source: 'Seek',
        Job_Post_Id: jobPostId,
        Candidate_Status: 10,
        Added_On: moment.utc().format('YYYY-MM-DD'),
        Added_By: 1,
        Seek_Profile_Id: candidateId,
        Expected_CTC: isValidDecimal ? convertedSalary : null,
        Expected_CTC_Sourced_From_Seek: expectedSalary,
        Currency: currencyId
      })
      .transacting(trx)
      .then((data) => {
        return true
      })
      .catch(function (err) {
        console.log('Error in addCandidateCareerInfo  catch() block', err)
        return err
      })
  } catch (err) {
    console.log('Error in addCandidateCareerInfo main catch() block', err)
    throw err
  }
}
function convertToDecimal(value) {
  // Ensure the value is treated as a string
  if (typeof value !== 'string') {
    value = String(value);
  }
  // Remove comma separators
  const cleanedValue = value.replace(/,/g, '').trim();
  // Attempt to parse the cleaned value as a float
  const decimalValue = parseFloat(cleanedValue);
  // If the conversion fails, return the original value
  return isNaN(decimalValue) ? value : decimalValue;
}

async function addCandidateAttachmentInfo(
  organizationDbConnection,
  trx,
  attachmentDetails
) {
  try {
    await organizationDbConnection('candidate_other_attachments')
      .insert(attachmentDetails)
      .transacting(trx);
    return true;
  } catch (err) {
    console.error('Error in addCandidateAttachmentInfo:', err);
    throw err;
  }
}