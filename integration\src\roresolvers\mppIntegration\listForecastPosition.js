
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment');
const { formId,s3FileUpload } = require('../../../common/appConstants');
const { generateAndUploadReport } = require('../../../src/common/commonFunction');

module.exports.listForecastPosition = async (parent, args, context, info) => {

    console.log("Inside listForecastPosition function.")
    let organizationDbConnection;

    try {
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, args.formId)
       
        if(Object.entries(checkRights).length > 0  && checkRights.Role_View === 1){

            // Handle position parent setup only for non-export cases
            let existingOriginalPositionIds = [];
            let getOrgLevel = null;

            if (!args.alexport) {
                // Only get parent ID from designation if not provided at all
                if(!args.postionParentId){
                    let orgStructureResult = await organizationDbConnection(ehrTables.empJob + ' as EJ')
                        .select(organizationDbConnection.raw(`CASE WHEN OS.Parent_Path IS NOT NULL  AND OS.Parent_Path != '0' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(OS.Parent_Path, ',', 2), ',', -1) ELSE OS.Originalpos_Id END AS firstParentPathId`))
                        .join( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
                        .join( ehrTables.SFWPOrganizationStructure + ' as OS', 'OS.Pos_Code', 'DES.Designation_Code')
                        .where('EJ.Employee_Id', employeeId).first();

                    args.postionParentId = orgStructureResult?.firstParentPathId;
                }
                if(args.formId === formId.hiringForeCast){
                    let forecastStartYear = args.forecastingYear ? args.forecastingYear : moment().year();

                    let mppForecastSettings = await organizationDbConnection(ehrTables.mppForecastSettings).select('End_Month').first()
                    let forecastEndMonth = mppForecastSettings ? mppForecastSettings.End_Month : 12;

                    let {forecastStartMonth, forecastEndYear} = calculateStartMonth(forecastEndMonth, forecastStartYear)

                    //Hiring Forecast position dropdown exclude value
                    let hiringForecastData = await organizationDbConnection(ehrTables.mppHiringForecast)
                    .select('Organization_Structure_Id')
                    .where(function() {
                        this.where('Forecast_Year', forecastStartYear)
                        .andWhereBetween('Forecast_Month', [forecastStartMonth, 12])
                        .orWhere('Forecast_Year', forecastEndYear)
                        .andWhereBetween('Forecast_Month', [1, forecastEndMonth]);

                    }).where(function() {
                        if(args.action && args.action.toLowerCase()=='update' && args.originalPositionId){
                            this.whereNotIn('Organization_Structure_Id', [args.originalPositionId])
                        }
                    })
                    .groupBy('Organization_Structure_Id');
                    existingOriginalPositionIds = hiringForecastData && hiringForecastData.length
                    ? [...new Set(hiringForecastData.flat().map(row => row.Organization_Structure_Id).filter(id => id !== null))]
                    : [];
                }

                if(!args.postionParentId || args.postionParentId.length===0){
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Hiring forecast position list retrieved successfully.", parentGroup: args.postionParentId, positionList: [], totalCountResult:0, orgLevel: ''};
                }

                // Get org level if postionParentId is provided and not "0"
                if(args.postionParentId && args.postionParentId !== "0"){
                    getOrgLevel = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
                        .select('OS.Org_Level')
                        .where('OS.Originalpos_Id', args.postionParentId)
                        .first();
                }
            }
        
            const [countResult, positionListResult] = await Promise.all([
                // Query to get the total count of records
                organizationDbConnection(ehrTables.SFWPOrganizationStructure)
                  .count('Organization_Structure_Id as totalRecords')
                  .whereNotIn('Status', ['InActive'])
                  .where(function() {
                    if (existingOriginalPositionIds && existingOriginalPositionIds.length && !args.alexport && args.action!=='update') {
                      this.whereNotIn('Organization_Structure_Id', existingOriginalPositionIds);
                    }
                    if(args.formId===291){
                      this.whereNotNull('Originalpos_Id')
                    }
                    if(args.formId!==288){
                      this.where('Type','JOBPOS')
                    }
                  })
                  .modify((queryBuilder) => {
                    // Apply position parent filtering only for non-export cases
                    if ((!args.alexport && args.postionParentId) || args.parentPath){
                      queryBuilder.andWhere(function() {
                        if (args.parentPath) {
                          const normalizedPath = args.parentPath.replace(/,$/, '');
                          this.orWhere('Parent_Path', 'like', `${normalizedPath}%`)
                              .andWhere(function() {
                                this.where('Parent_Path', 'like', `${normalizedPath},%`)
                                    .orWhere('Parent_Path', '=', normalizedPath);
                              })
                              .orWhere('Originalpos_Id', args.postionParentId);
                        }
                        else{
                          if (args.postionParentId && !args.alexport) {
                            this.where('Parent_Path', '=', `0,${args.postionParentId}`)
                              .orWhere('Parent_Path', 'like', `0,${args.postionParentId},%`)
                              .orWhere('Originalpos_Id', args.postionParentId);
                          }
                        }
                      });
                    }
                  }),
              
                // Query to fetch the records with offset and limit
                organizationDbConnection(ehrTables.SFWPOrganizationStructure)
                  .select('Originalpos_Id','Organization_Structure_Id','Pos_Code', 'Pos_Name', 'Job_Title_Code', 'Cost_Code', 'Global_Grade', 'Approved_Position', 'Status','Warm_Bodies', 'Parent_Id', 'Parent_Path',
                    organizationDbConnection.raw('Approved_Position - Warm_Bodies AS To_Be_Hired'),
                    organizationDbConnection.raw(
                      "CASE WHEN Pos_Code IS NOT NULL THEN CONCAT(Pos_Name,' - ',Pos_Code) ELSE Pos_Name END AS Pos_full_Name"
                    ),)
                  .whereNotIn('Status', ['InActive'])
                  .where(function() {
                    if (existingOriginalPositionIds && existingOriginalPositionIds.length && args.action!=='update' && !args.alexport) {
                      this.whereNotIn('Organization_Structure_Id', existingOriginalPositionIds);
                    }
                    if(args.formId===291){
                      this.whereNotNull('Originalpos_Id')
                    }
                    if(args.formId!==288){
                      this.where('Type','JOBPOS')
                    }
                  })
                  .modify(function (queryBuilder) {
                    // Apply position parent filtering only for non-export cases
                    if ((!args.alexport && args.postionParentId) || args.parentPath){
                      queryBuilder.andWhere(function() {
                        if (args.parentPath) {
                          const normalizedPath = args.parentPath.replace(/,$/, '');
                          this.orWhere('Parent_Path', 'like', `${normalizedPath}%`)
                              .andWhere(function() {
                                this.where('Parent_Path', 'like', `${normalizedPath},%`)
                                    .orWhere('Parent_Path', '=', normalizedPath);
                              })
                              .orWhere('Originalpos_Id', args.postionParentId);
                        }
                        else{
                          if (args.postionParentId && !args.alexport) {
                            this.where('Parent_Path', '=', `0,${args.postionParentId}`)
                              .orWhere('Parent_Path', 'like', `0,${args.postionParentId},%`)
                              .orWhere('Originalpos_Id', args.postionParentId);
                          }
                        }
                      });
                    }

                    // Apply pagination only for non-export cases
                    if (!args.alexport) {
                      if (args.offset) {
                        queryBuilder.offset(args.offset);
                      }
                      if (args.limit) {
                        queryBuilder.limit(args.limit);
                      }
                    }
                  })
              ]);
              
              // Extract the total count
              let totalCountResult = countResult[0].totalRecords;

              // Handle alexport functionality
              if (args.alexport) {
                // Transform data for export
                const formattedData = transformAlexportDataForecastPosition(positionListResult);

                // Get organization code
                const orgCode = context.Org_Code || 'default';

                // Generate and upload file to S3
                const uploadResult = await generateAndUploadReport({
                    orgCode: orgCode,
                    reportData: formattedData,
                    reportType: 'ORG_STRUCTURE',
                    fileName: null
                });

                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return {
                    errorCode: "",
                    message: "Table of Organization export data retrieved successfully.",
                    parentGroup: null,
                    positionList: null,
                    totalCountResult: formattedData.length,
                    orgLevel: '',
                    s3Url: uploadResult.success ? uploadResult.s3Url : null,
                    s3Path: uploadResult.success ? uploadResult.s3Path : null
                };
              }

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Hiring forecast position list retrieved successfully.", parentGroup: args.postionParentId, positionList: positionListResult,totalCountResult:totalCountResult,orgLevel:getOrgLevel?.Org_Level };
        }else{
            throw '_DB0100';
        }
    } catch(err){
        //Destroy DB connection
        console.error('Error in listForecastPosition function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'EI00174'); //Sorry!, an error occured while retrieve the organization of hiring forecast. Please try after some time.
        throw new ApolloError(errResult.message, errResult.code);
    }
    
}


function calculateStartMonth(endMonth, startYear) {
    let forecastStartMonth;
    let forecastEndYear = startYear;

    if (endMonth === 12) {
        // Scenario 1: End month is December, start month is January of the same year
        forecastStartMonth = 1;
    } else {
        // Scenario 2: End month is any month other than December
        forecastStartMonth = endMonth + 1;
        forecastEndYear = startYear + 1;
    }

    return { forecastStartMonth, forecastEndYear };
}



function transformAlexportDataForecastPosition(rawData) {
    return rawData.map(row => ({
        'Position Title': row.Pos_Name || '',
        'Position Code': row.Pos_Code || '',
        'Job Title Code': row.Job_Title_Code || '',
        'Cost Code': row.Cost_Code || '',
        'Approved Position': row.Approved_Position || 0,
        'Warm Bodies': row.Warm_Bodies || 0,
        'Approved Vacant Positions': row.To_Be_Hired || 0
    }));
}

