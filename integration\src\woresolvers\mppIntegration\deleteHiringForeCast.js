// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require moment
const moment = require('moment'); // Missing moment import
// Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formId } = require('../../../common/appConstants');
const { ehrTables } = require('../../../common/tablealias');
const { calculateStartMonth } = require('../../common/commonFunction');

module.exports.deleteHiringForeCast = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside deleteHiringForeCast function.");
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        
        // Check access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loginEmployeeId,
            null,
            '',
            'UI',
            false,
            formId.hiringForeCast
        );

        // If rights are available, proceed with deletion
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Delete === 1) {
            let forecastStartYear = args.forecastingYear;

            // Retrieve forecast settings
            let mppForecastSettings = await organizationDbConnection(ehrTables.mppForecastSettings).select('End_Month').first();
            let forecastEndMonth = mppForecastSettings ? mppForecastSettings.End_Month : 12;

            // Calculate forecast start month and end year
            let { forecastStartMonth, forecastEndYear } = calculateStartMonth(+forecastEndMonth, +forecastStartYear);
            // Perform the delete operation
            await organizationDbConnection(ehrTables.mppHiringForecast)
                .del()
                .where('Original_Position_Id', args.originalPositionId)
                .where(function() {
                    this.where('Forecast_Year', forecastStartYear)
                        .andWhereBetween('Forecast_Month', [forecastStartMonth, 12])
                        .orWhere('Forecast_Year', forecastEndYear)
                        .andWhereBetween('Forecast_Month', [1, forecastEndMonth]);
                });

            // Log the system activity after deletion
            let systemLogParam = {
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                organizationDbConnection: organizationDbConnection,
                message: `Hiring ForeCast details deleted for Hiring ForeCast id: ${args.originalPositionId}`
            };
            await commonLib.func.createSystemLogActivities(systemLogParam);

            // Return successful message
            return { errorCode: '', message: 'Hiring ForeCast details deleted successfully.' };
        } else {
            console.log('No rights to delete the Hiring ForeCast details');
            throw '_DB0103';
        }
    } catch (error) {
        console.log('Error while deleteHiringForeCast main catch block.', error);
        let errResult = commonLib.func.getError(error, 'EI00204');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Ensure DB connection is destroyed
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
};
