# defining custom data type
scalar Date
type Query {
   listDummyQuery: listDummyQueryResponse!
}
type Mutation {
  updateIrukkaJobPostStatus(
    jobPostId: Int!
    irukkaJobPostId: String!
    jobPostStatus: String!
  ) : updateIrukkaJobPostStatusResponse

  addIrukkaCandidateDetails(
    jobId: String!
    partnerJobReferenceId: Int!
    jobAssignedId: String!
    userId: Int!
    name: String!
    genderId: Int
    gender: String!
    countryCode: String!
    phoneNumber: String!
    email: String
    city: String
    state: String
    country: String
    pinCode: String
  ) : addIrukkaCandidateDetailsResponse
}
type listDummyQueryResponse {
  errorCode: String
  message: String
}
type updateIrukkaJobPostStatusResponse{
  errorCode :String
  message :String
}
type addIrukkaCandidateDetailsResponse{
  errorCode :String
  message :String
}
type commonResponse{
  errorCode :String
  message :String
}
schema {
  query: Query
  mutation: Mutation
}