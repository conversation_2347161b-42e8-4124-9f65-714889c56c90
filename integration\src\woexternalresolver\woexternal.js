const { ApolloServer, gql } = require('apollo-server-lambda');
const { resolvers } = require('./woexternalresolver');
const path = require("path");
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(path.resolve()+'/src/woexternalresolver/'+'woexternalschema.graphql', 'utf8'));
// require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
module.exports.graphql = (event, context, callback) => {
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes
    // get customAuthorizerData from firebase authorize and  return it to resolver if exists.
    let idToken = event.requestContext.authorizer.idToken ? event.requestContext.authorizer.idToken : '';
    let refreshToken = event.requestContext.authorizer.refreshToken ? event.requestContext.authorizer.refreshToken : '';
    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            let authDetails ={
                idToken: idToken,
                refreshToken: refreshToken
            };
            let contextData = await commonLib.func.getContextDataWithoutEmployeeId(event, 1, 'wo');
            contextData.irukkaIdToken = event.headers.irukkaIdToken; 
            //return header to resolver function
            return {...contextData};
        }
    });
    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    
    function callbackFilter(error, output) {
        // We are appending the idToken and refreshToken in the response. While running this in local this is not returning the response
        // so here checked the stagename as local or not. If it is local then we will no append the token response. 
        // Otherwise token response will be append and response will be returned
        // If any doubts check this task #3794
        if (process.env.stageName !== 'local') {
            // parse the response data
            let responseData = JSON.parse(output.body);
            // push idToken and refresh token into an json objects
            let identityToken = {
                idToken: idToken,
                refreshToken: refreshToken
            }
            // return the idToken and refreshTOken to UI
            responseData.identityToken = identityToken;
            output.body = JSON.stringify(responseData);
        }
        
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};
