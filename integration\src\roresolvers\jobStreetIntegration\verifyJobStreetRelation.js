// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
const {
  hiringOrgLegacy
} = require('../../queries/jobStreetQueries')
//Require knex to make DB connection
const moment = require('moment')
const knex = require('knex')
//Require axios
const axios = require('axios')
const { mapErrorToCustomCodeSeek } = require('../../common/commonFunction')
const { ehrTables } = require('../../../common/tablealias')

module.exports.verifyJobStreetRelation = async (
  parent,
  args,
  context,
  info
) => {
  try {
    console.log('Inside verifyJobStreetRelat,ion function.')
    let loginEmployeeId = context.Employee_Id;
    let  organizationDbConnection = knex(context.connection.OrganizationDb);
    let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false, args.form_Id);
    if(Object.keys(checkRightsForForm).length > 0  && ((checkRightsForForm.Role_Add === 1) || (checkRightsForForm.Role_Update === 1))  && checkRightsForForm.Employee_Role.toLowerCase() === 'admin'){
        if(!context.jobstreet_access_token){
        throw 'EI00146';
    }
    const ACCESS_TOKEN = context.jobstreet_access_token
    const GRAPHQL_ENDPOINT = process.env.jobStreetTokenAPI;
    try {
      const hiringOrgJson = { "legacyId": args.companyId }
      const positionOpeningResponse = await axios
        .post(
          GRAPHQL_ENDPOINT,
          { query: hiringOrgLegacy, variables: hiringOrgJson },
          {
            headers: {
              Authorization: `Bearer ${ACCESS_TOKEN}`,
              'Content-Type': 'application/json',
              'User-Agent': 'YourPartnerService/1.2.3'
            }
          }
        )
        .catch((err) => {
          console.log('Error in verifyJobStreetRelation function catch block.',err)
          throw err
        });
      if (
        positionOpeningResponse.data &&
        positionOpeningResponse.data.errors &&
        positionOpeningResponse.data.errors.length
      ) {
        console.log(
          'Error in verifyJobStreetRelation function catch block.',
          positionOpeningResponse.data.errors
        )
        let errorCode = await mapErrorToCustomCodeSeek(
          positionOpeningResponse.data
        )
        if (errorCode === 'CH0001') {
          throw 'EI00147' //need to change
        }
        else if(errorCode === 'CH0003'){
            throw 'EI00149'
        }
        else {
          throw 'EI00148'
        }
      }
      let seekId=null
      let companyName=null
      if(positionOpeningResponse.data && positionOpeningResponse.data.data&& positionOpeningResponse.data.data.seekAnzAdvertiser){
        seekId=positionOpeningResponse.data.data.seekAnzAdvertiser.id.value
        companyName=positionOpeningResponse.data.data.seekAnzAdvertiser.name;
      }
      else{
        throw 'EI00161'
      }
      await organizationDbConnection(ehrTables.seekHirerList)
        .insert({
        Company_Id: args.companyId,
        Hirer_ID:seekId,
        Integration_Type:'seek',
        Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
        Added_By: loginEmployeeId,
        Company_Name:companyName
        })
        .catch((error) => {
          console.log(
            'Error in verifyJobStreetRelation function catch block ',
            error
          )
          throw error
        })

      return { errorCode: '', message: `verified successfully` }
    } catch (error) {
      throw error
    }
}
else{
  if(Object.keys(checkRightsForForm).length < 0 || (Object.keys(checkRightsForForm).length > 0 && checkRightsForForm.Role_Update !== 1)){
    console.log('No rights to update the recruitment integration Status');
    throw '_DB0111';
  }else{
    throw '_DB0109'
  }
}
  } catch (e) {
    //Destroy DB connection
    console.log(
      'Error in verifyJobStreetRelation function main catch block.',
      e
    )
    let errResult;
    let errorCode = e.code === 'ER_DUP_ENTRY' ? 'EI00167' : e;
   if(errorCode==='EI00167'){
    errResult = commonLib.func.getError('EI00167')
   }
   else{
    errResult = commonLib.func.getError(errorCode, 'EI00150')
   }
    throw new ApolloError(errResult.message, errResult.code)
  }
}
