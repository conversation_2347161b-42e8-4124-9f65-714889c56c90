// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');

module.exports.retrieveWorkflowApprovalSetting = async (parent, args, context, info) => {
    console.log("Inside retrieveWorkflowApprovalSetting function.");
    let organizationDbConnection;

    try {
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check access rights - Update formId if needed
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, 
            employeeId, 
            '', 
            '', 
            'UI', 
            false,
            args.formId // Update this formId if required for proper access control
        );

        if (Object.entries(checkRights).length > 0 && checkRights.Role_View === 1) {

            const workflowSettings = await organizationDbConnection(ehrTables.mppForecastSettings)
                .first()
                .select('Enable_Workflow_Approval');

            organizationDbConnection?.destroy();

            return { 
                errorCode: "", 
                message: "Workflow approval setting retrieved successfully.", 
                enableWorkflowApproval: workflowSettings?.Enable_Workflow_Approval || 'Yes' 
            };

        } else {
            throw '_DB0100';
        }

    } catch (err) {
        console.error('Error in retrieveWorkflowApprovalSetting:', err);
        organizationDbConnection?.destroy();

        let errResult = commonLib.func.getError(err, 'EI00330');
        throw new ApolloError(errResult.message, errResult.code);
    }
}