// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const {formId} = require('../../../common/appConstants');
let moment = require('moment');
const axios = require('axios');


module.exports.closeJobpostToIndeed = async (parent, args, context, info) => {
    console.log('Inside closeJobpostToIndeed function');
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false,  formId.jobpost);
        if(Object.keys(checkRightsForForm).length > 0  && checkRightsForForm.Role_Update === 1){
            const indeedUrl = process.env.indeedPublishJobAPI;
            const sourcedPostingIds = [args.sourcedPostingId];
            const input = {
                jobs: sourcedPostingIds.map(id => ({ sourcedPostingId: id }))
              };
            const query = `mutation expireJobs($input: ExpireSourcedJobsBySourcedPostingIdInput!) {
                            jobsIngest {
                            expireSourcedJobsBySourcedPostingId(input: $input) {
                                results {
                                trackingKey
                                }
                            }
                            }
                        }`;
            // Make the request using Axios
            const config = {
                method: 'post',
                url: indeedUrl,
                headers: {
                    'Authorization': context.indeed_access_token, 
                    'org_code': context.Org_Code,
                    'Content-Type': 'application/json',
                },
                data: JSON.stringify({
                    query: query,
                    variables: {
                    input: input
                    }
                  })
                };
                try {
                    // Make the POST request using Axios
                    let response = await Promise.resolve(axios.request(config))
                    const responseMessage = JSON.stringify(response.data);
                    if(response && response.data && !response.data.errors){
                        await closeJobPostForHrapp(organizationDbConnection, args.integrationId, loginEmployeeId, context);
                    }
                    return { errorCode: "", results: responseMessage };
                  } catch (error) {
                    if (error.response) {
                        // The request was made and the server responded with a status code
                        console.error('Response Error:', error.response.data);
                        console.error('Status Code:', error.response.status);
                        const responseMessage = JSON.stringify(error.response.data);
                        return { errorCode: "", results: responseMessage };
                    } else if (error.request) {
                        // The request was made but no response was received
                        console.error('Request Error:', error.request);
                    } else {
                        // Something else happened in setting up the request
                        console.error('Other Error:', error.message);
                    }
                    throw 'SET0120';
                  }
        }
        else {
            console.log('This employee do not have add or edit access rights');
            throw '_DB0111';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in closeJobpostToIndeed function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0020');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function closeJobPostForHrapp(organizationDbConnection, integrationId, loginEmployeeId, context){
    try{ 
          return (
              organizationDbConnection(ehrTables.jobPostIndeedIntegration)
                  .update({
                      Status: "Deleted",
                      Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                      Updated_By: loginEmployeeId
                  }).where('Integration_Id', integrationId)
                  .then(async(data) => {
                      if (data) {
                        let systemLogParam = {
                            action: 'Update',
                            userIp: context.User_Ip,
                            employeeId: loginEmployeeId,
                            trackingColumn: '',
                            organizationDbConnection: organizationDbConnection,
                            message: `The jobpost has been closed by ${loginEmployeeId}`
                        };

                        // Call the function to add the system log
                        await commonLib.func.createSystemLogActivities(systemLogParam);
                          organizationDbConnection ? organizationDbConnection.destroy() : null;
                          return true;
                      } else {
                          throw 'SET0121'
                      }
                  })
                  .catch((catchError) => {
                      console.log('Error in closeJobPostForHrapp .catch() block', catchError);
                      let errResult = commonLib.func.getError(catchError, 'SET0117');
                      organizationDbConnection ? organizationDbConnection.destroy() : null;
                      throw new ApolloError(errResult.message, errResult.code);
                  })
          )
    } catch(err){
        console.log('Error in closeJobPostForHrapp main catch() block', err);
        throw err;
    }
  }