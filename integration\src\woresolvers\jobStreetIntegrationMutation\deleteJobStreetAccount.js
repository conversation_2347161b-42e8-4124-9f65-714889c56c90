const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { checkDeletionEligibility } = require('../../common/commonFunction');

module.exports.deleteJobStreetAccount = async (parent, args, context) => {
  let validationError = {};
  const { Employee_Id: loginEmployeeId, User_Ip: userIp } = context;
  let organizationDbConnection;

  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check user rights
    let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection, 
      loginEmployeeId, 
      null, 
      '', 
      "UI", 
      false, 
      args.formId
    );

    if (Object.keys(checkRightsForForm).length > 0 && 
        checkRightsForForm.Role_Delete === 1 && 
        checkRightsForForm.Employee_Role.toLowerCase() === 'admin') {

      await checkDeletionEligibility(organizationDbConnection, args.seekHirerId);
      // Proceed with deletion if no blocking status is found
      await organizationDbConnection(ehrTables.seekHirerList)
        .where('Hirer_List_Id', args.seekHirerId)
        .del();
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Hirer list details for hirer-id ${args.seekHirerId} deleted`
      });

      return {
        errorCode: '',
        message: 'The hirer list details have been successfully deleted.'
      };

    } else {
        if (Object.keys(checkRightsForForm).length === 0 || checkRightsForForm.Role_Delete !== 1) {
          console.log('No rights to delete the recruitment integration Status');
          throw '_DB0103';
        } else {
          throw '_DB0109';
        }
    }

  } catch (error) {
    console.error('Error in deleteJobStreetAccount function main catch block.', error);
    const errResult = commonLib.func.getError(error, 'EI00216');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};

