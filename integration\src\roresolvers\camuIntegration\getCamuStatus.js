// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appConstants');


let organizationDbConnection;
module.exports.getCamuStatus = async (parent, args, context, info) => {
    try {
        console.log("Inside getCamuStatus function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.userAccounts, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 && checkRights.Employee_Role.toLowerCase() === 'admin') {
            return (
                await organizationDbConnection(ehrTables.employeeInfoTimestampLog)
                    .select("*")
                    // .where('Log_Timestamp', '=', function () {
                    //     this.select('Log_Timestamp')
                    //         .from('employee_info_timestamp_log as e2')
                    //         .whereRaw('e1.Employee_Id = e2.Employee_Id')
                    //         .orderBy('Log_Timestamp', 'desc')
                    //         .where('Action', 'Add')
                    //         .limit(1)
                    // })
                    .orderBy('Employee_Id')
                    .where('Action', 'Add')
                    .groupBy('Employee_Id')
                    .then((data) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Camu status retrieved successfully.", camuStatus: data };
                    })
                    .catch((err) => {
                        console.log('Error in getCamuStatus .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'EI00119');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getCamuStatus function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EI00120');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
