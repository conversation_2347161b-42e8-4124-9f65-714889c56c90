// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');

module.exports.retrieveEducationLabelList = async (parent, args, context, info) => {

    console.log("Inside retrieveEducationLabelList function.");
    let organizationDbConnection;

    try {
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, 
            employeeId, 
            '', 
            '', 
            'UI', 
            false,
            formId.newPosition
        );

        if (Object.entries(checkRights).length > 0 && checkRights.Role_View === 1) {

            const educationLabelList = await organizationDbConnection(ehrTables.mppEducationRequirements)
                .select('*');
                console.log("educationLabelList", educationLabelList);

            organizationDbConnection ? organizationDbConnection.destroy() : null;

            return { 
                errorCode: "", 
                message: "Education Label List retrieved successfully.", 
                educationLabelList: educationLabelList 
            };

        } else {
            throw '_DB0100';
        }

    } catch (err) {
        //Destroy DB connection
        console.error('Error in retrieveEducationLabelList function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        let errResult = commonLib.func.getError(err, 'EI00183');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
