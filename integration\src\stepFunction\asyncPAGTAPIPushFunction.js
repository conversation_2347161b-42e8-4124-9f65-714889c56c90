//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const{getConnection}=require("./commonFunctions");
const axios = require('axios');
//Require knex to make DB connection
const knex = require('knex');
//Require table alias
const { ehrTables } = require('../../common/tablealias');
const moment = require('moment');


module.exports.asyncPAGTAPIPushFunction = async (args) => {

    console.log('Inside asyncPAGTNexusHrmsAPIPushFunction function started ');
    let organizationDbConnection;
    try{ 

        console.log(' asyncPAGTNexusHrmsAPIPushFunction args value => ', args);
        let connection = await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region, args.orgCode);
        organizationDbConnection = knex(connection.OrganizationDb);
    
        
        await organizationDbConnection(ehrTables.externalApiIntegrationLog)
        .update({ Status: 'InProgress'}).whereIn('Status', ['Open', 'Failed'])
        .where(qb => { 
            if (args.candidateId) {
                qb.where('Candidate_Id', args.candidateId);
            }
        });

        let secretKeys = await commonLib.func.getCredentials(process.env.region, process.env.dbSecretName);

        const pagtNexusHrmsDNSURL = process.env.pagtAPIURL

        const authentication = await getPagtAPIRequestToken(pagtNexusHrmsDNSURL, secretKeys);

        console.log('Inside asyncPAGTNexusHrmsAPIPushFunction authentication response => ', authentication);

        if(authentication && authentication.status){
            let orgCode = args.orgCode;
            await getEmployeeIntegrationLog(args.candidateId, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode})
            
        } else {
        
            const employeeAPIIntegrationLogList = await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .select('Employee_Id', 'Data_Sync_Result')
            .where('Candidate_Id', args.candidateId);


            const requiredKeys = ['Personal', 'Language', 'Accreditations', 'Education', 'Additional', 'Certificate', 'PreviousEmployment', 'Attachment'];

            await Promise.all(employeeAPIIntegrationLogList.map(async ({ Employee_Id, Data_Sync_Result }) => {
                const syncResults = Data_Sync_Result ? JSON.parse(Data_Sync_Result) : {};

                let updatedSyncResults = await Promise.all(requiredKeys.map(key => {
                    if (!syncResults[key] || (syncResults[key].Sync_Status && syncResults[key].Sync_Status.toLowerCase() !== 'success')) {
                        return { Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'), Sync_Status: 'Failed', Failure_Reason: `Authentication Failed` };
                    }
                    return syncResults[key];
                }));

                const allSyncStatusSuccess = Object.values(updatedSyncResults).every(
                    (section) => section.Sync_Status === "Success"
                );
    
                let updateData = { Status: 'Failed', Data_Sync_Result: JSON.stringify(Object.fromEntries(requiredKeys.map((k, i) => [k, updatedSyncResults[i]]))) };
                if(allSyncStatusSuccess){
                    updateData.Status = 'Success';
                }
                
                await organizationDbConnection(ehrTables.externalApiIntegrationLog)
                .update(updateData).where('Employee_Id', Employee_Id);
            }));
        }
        
        
    } catch (error) {
        console.error('Error asyncPAGTNexusHrmsAPIPushFunction() function main catch block. ', error);
        organizationDbConnection && await organizationDbConnection(ehrTables.externalApiIntegrationLog)
        .update({Status: 'Failed'}).where('Candidate_Id', args.candidateId);

    } finally {
        // destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }            
}

async function getPagtAPIRequestToken(pagtNexusHrmsDNSURL, paramsValue) {
    const url = `${pagtNexusHrmsDNSURL}/request-token?secretKey=${paramsValue.pagt_apikey}`;
    try {
        const config = {
            method: 'post',
            url,
            maxBodyLength: Infinity,
            data: {},
        };
        console.log(`Inside authentication request => `, config);
        const { data } = await axios.request(config);
        console.log('Inside authentication success response => ', data);
        return { status: true, ACCESS_TOKEN: data, TOKEN_TYPE: 'Bearer', message: 'Success' };
    } catch (error) {
        console.error('Error Occured While getting from getPagtAPIRequestToken() main catch block ', error.response);
        if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
            return { status: false, message: `408 - Request Timeout. The authentication server took too long to respond. Please try again in a moment`};
        }
        const { response } = error;
        const message = response && response.data ? response.data.MESSAGE || response.data : "Authentication error for this request Please verify the API key or token is valid and try again or contact your administrator.";
        return { status: false, message: message };
    }
}

async function getEmployeeIntegrationLog(candidateId, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode}) {
    try {
        const employeeAPIIntegrationLogList = await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .select('Employee_Id', 'Data_Sync_Result')
            .where('Candidate_Id', candidateId);

        const requiredKeys = ['Personal', 'Language', 'Accreditations', 'Education', 'Additional', 'Certificate', 'PreviousEmployment', 'Attachment'];

        const updatePromises = employeeAPIIntegrationLogList.map(async ({ Employee_Id, Data_Sync_Result }) => {
            const syncResults = Data_Sync_Result ? JSON.parse(Data_Sync_Result) : {};

            const updatedSyncResults = await Promise.all(requiredKeys.map(async key => {
                if (!syncResults[key] || (syncResults[key].Sync_Status && syncResults[key].Sync_Status.toLowerCase() !== 'success')) {
                    console.log(`Retrying ${key} for Employee ${Employee_Id}...`);

                    try {
                        let result;
                        switch (key) {
                            case 'Personal':
                                result = await pushToEmployeePersonalData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL });
                                break;
                            case 'Accreditations':
                                result = await pushToEmployeeAccreditationsData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode });
                                break;
                            case 'Education':
                                result = await pushToEmployeeEducationData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL });
                                break;
                            case 'Additional':
                                result = await pushToEmployeeAdditionalData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL, candidateId });
                                break;
                            case 'PreviousEmployment':
                                result = await pushToPreviousEmploymentData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL });
                                break;
                            case 'Attachment':
                                result = await pushToAttachmentFileData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode });
                                break;
                            case 'Language':
                                result = await pushToEmployeeLanguageData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL });
                                break;
                            case 'Certificate':
                                result = await pushToEmployeeCertificateData([Employee_Id], { organizationDbConnection, authentication, pagtNexusHrmsDNSURL });
                                break;
                            default:
                                break;

                        }
                        return result || syncResults[key] || { Sync_Status: 'Open', Failure_Reason: '', Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') };
                    } catch (error) {
                        console.error(`Error retrying ${key} for Employee ${Employee_Id}.`, error);
                        return { Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'), Sync_Status: 'Failed', Failure_Reason: `Error: ${error.message}` };
                    }
                }
                return syncResults[key];
            }));

            const allSyncStatusSuccess = Object.values(updatedSyncResults).every(
                (section) => section.Sync_Status === "Success"
            );

            let updateData = { Status: 'Failed', Data_Sync_Result: JSON.stringify(Object.fromEntries(requiredKeys.map((k, i) => [k, updatedSyncResults[i]]))) };
            if(allSyncStatusSuccess){
                updateData.Status = 'Success';
            }
            
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
                .update(updateData)
                .where('Employee_Id', Employee_Id);
        });

        await Promise.all(updatePromises);
    } catch (error) {
        console.error('Error in getEmployeeIntegrationLog function main catch block.', error);
        return false;
    }
}

async function callingPagtAPI(url, authentication, data, type) {

    const config = {
        method: 'post',
        url,
        maxBodyLength: Infinity,
        data: data,
        headers: {
            Authorization: `${authentication.TOKEN_TYPE} ${authentication.ACCESS_TOKEN}`,
            'Content-Type': 'application/json',
        },
    };

    try {

        console.log("Inside callingPagtAPI type= "+type+", request= ", data);
       
        const { data: response } = await axios.request(config);
        console.log(`Inside callingExternalAPI ${type} success response => `, response);
        return { status: true, message: typeof response === "string" ? response : response.Message || response.toString() };
    } catch (error) {
        console.error(`Error callingExternalAPI ${type} function main catch block.`, error?.response?.data || error?.response || error);
       // console.log(`Inside callingExternalAPI ${type} request => `, config);
        if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
            return { status: false, message: `408 - Request Timeout. The server took too long to respond. Please try again in a moment`};
        }
        const { status, statusText, data, data: { message: errorMessage, Message: errorMessage1} = {} } = error?.response || {};
        const errorDetails = `Error: ${status || ' Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${errorMessage || errorMessage1 || data || ' No error message provided'}`;
        return { status: false, message: errorDetails };
    }
}



async function pushToEmployeePersonalData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL}) {
    try {
        console.log('Inside pushToEmployeePersonalData() function');
        let employeePersonalList = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EMP')
            .select(
                'EJ.User_Defined_EmpId as EmployeeCode',
                'EMP.Salutation as Salutation', 'EMP.Emp_First_Name as FirstName', 'EMP.Emp_Middle_Name as MiddleName', 'EMP.Emp_Last_Name as LastName', 'EMP.Appellation as Suffix', 'EMP.Emp_Pref_First_Name as NickName',
                'EMP.Gender as Gender', 'EMP.DOB as DOB', 'MS.Marital_Status as MaritalStatus', 'EMP.Blood_Group as BloodGroup', 'EMP.Personal_Email as PersonalMailID', 'EMP.PAN as TaxIdentificationNumber',
                'ET.Employee_Type_Code as EmployeeTypeCode', 
                'OG.Organization_Group_Code as OrganizationGroup', 'BSU.Business_Unit_Code as BusinessUnit', 'SP.Service_Provider_Code as OrganizationUnit',
                'DEPT.Department_Code as DepartmentCode', 'DES.Designation_Code as DesignationCode', 'EJ.Date_Of_Join as DateOfJoin', 'L.Location_Code as WorkLocationCode',
                'EJ2.User_Defined_EmpId as SupervisorCode', 'EJ3.User_Defined_EmpId as SecondarySupervisorCode', 'OD.Org_Name as OrgCode', 'EG.Grade_Code as GradeCode',
                organizationDbConnection.raw("CASE WHEN ECD.Mobile_No_Country_Code IS NOT NULL AND ECD.Mobile_No IS NOT NULL THEN CONCAT(ECD.Mobile_No_Country_Code, ECD.Mobile_No) ELSE ECD.Mobile_No END as MobileNo"),
                organizationDbConnection.raw("CONCAT_WS(' ',ECD.pApartment_Name, ECD.pStreet_Name) as PermanentAddress"), 'ECD.pCity as City', 'ECD.pBarangay as Barangay', 'ECD.pRegion as Region', 'ECD.pState as State', 'ECD.pPincode as Zipcode',
                organizationDbConnection.raw("CONCAT_WS(' ',ECD.cApartment_Name, ECD.cStreet_Name) as CurrentAddress"), 'ECD.cCity as CA_City', 'ECD.cBarangay as CA_Barangay', 'ECD.cRegion as CA_Region', 'ECD.cState as CA_State', 'ECD.cPincode as CA_Zipcode',
                'EBD.Bank_Account_Number as BankAccountNumber', 'EBD.IFSC_Code as BranchCode', 'EBD.Bank_Account_Name as BankAccountName', 'BD.Bank_Name as BankName',
                'EP.Passport_No as PassportNumber', 'EP.Issue_Date as IssueDate', 'EP.Expiry_Date as ExpiryDate', 'EP.Issuing_Country as IssuingCountry', 'EP.Issuing_Authority as IssuingAuthority',
                organizationDbConnection.raw('\'\' AS CategoryCode'),
                organizationDbConnection.raw('\'\' AS ManagementCode'),
                organizationDbConnection.raw('\'\' AS ManagementName'),
                organizationDbConnection.raw('\'\' AS ReportingManageName'),
                organizationDbConnection.raw('\'\' AS VerifierCode'),
                organizationDbConnection.raw('\'\' AS VerifierUserName'),
                organizationDbConnection.raw('\'\' AS DivisionCode'),
                organizationDbConnection.raw('\'\' AS SectionCode'),
            )
            .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EMP.Employee_Id')
            .leftJoin(ehrTables.contactDetails + ' as ECD', 'ECD.Employee_Id', 'EMP.Employee_Id')
            .leftJoin(ehrTables.empBankdetails + ' as EBD', 'EBD.Employee_Id', 'EMP.Employee_Id')
            .leftJoin(ehrTables.empPassport + ' as EP', 'EP.Employee_Id', 'EMP.Employee_Id')
            .leftJoin(ehrTables.employeeType + ' as ET', 'ET.EmpType_Id', 'EJ.EmpType_Id')
            .leftJoin(ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
            .leftJoin(ehrTables.department + ' as DEPT', 'DEPT.Department_Id', 'EJ.Department_Id')
            .leftJoin(ehrTables.organizationGroup + ' as OG', 'OG.Organization_Group_Id', 'EJ.Organization_Group_Id')
            .leftJoin(ehrTables.businessUnit + ' as BSU', 'BSU.Business_Unit_Id', 'EJ.Business_Unit_Id')
            .leftJoin(ehrTables.serviceProvider + ' as SP', 'SP.Service_Provider_Id', 'EJ.Service_Provider_Id')
            .leftJoin(ehrTables.empJob + ' as EJ2', 'EJ2.Employee_Id', 'EJ.Manager_Id')
            .leftJoin(ehrTables.empJob + ' as EJ3', 'EJ3.Employee_Id', 'EJ2.Manager_Id')
            .leftJoin(ehrTables.empGrade + ' as EG', 'EG.Grade_Id', 'DES.Grade_Id')
            .leftJoin(ehrTables.maritalStatus + ' as MS', 'MS.Marital_Status_Id', 'EMP.Marital_Status')
            .leftJoin(ehrTables.location + ' as L', 'L.Location_Id', 'EJ.Location_Id')
            .leftJoin(ehrTables.bankDetails + ' as BD', 'EBD.Bank_Id', 'BD.Bank_Id')
            .join(ehrTables.orgDetails + ' as OD')
            .whereIn('EMP.Employee_Id', employeeIds);
           

        if (employeePersonalList && employeePersonalList.length) {

            employeePersonalList =replaceNullValues(employeePersonalList)
              
            const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-personal-data-to-staging`, authentication, employeePersonalList, "PerosnalInfo");
            return {
                Sync_Status: response.status ? 'Success' : 'Failed',
                Failure_Reason: response.status ? null : response.message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };

        } else {
            return { 
               Sync_Status: 'Failed', 
               Failure_Reason: 'No records found', 
               Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') 
            }
        }
    } catch (error) {
        console.error('Error pushToEmployeePersonalData() function main catch block.', error);
        throw error;
    }
}
function replaceNullValues(objectValue) {
    if (typeof objectValue === undefined || objectValue === null) {
        return '';
    }
    if (Array.isArray(objectValue)) {
        return objectValue.map(item => replaceNullValues(item));
    }
    if (typeof objectValue === 'object') {
        const newObj = {};
        for (const key in objectValue) {
            if (objectValue.hasOwnProperty(key)) {
                newObj[key] = replaceNullValues(objectValue[key]);
            }
        }
        return newObj;
    }
    return objectValue;
}

async function pushToEmployeeEducationData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL }) {
    try {
       
        const employeeEducationList = await organizationDbConnection(ehrTables.empEducation + ' as EE')
        .select([
            'EJ.User_Defined_EmpId as EmployeeCode','CD.Course_Id as EducationLevelID', 'EIT.Institution_Id as SchoolID', 
            'ESZ.Specialization_Id as CourseID', 'EE.Start_Date as StartDate', 'EE.End_Date as EndDate',
            organizationDbConnection.raw("CONCAT_WS('', EE.Education_Id,EE.Education_Type,EE.Employee_Id) as ApplicantEducationID"), 
        ])
        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EE.Employee_Id')
        .leftJoin(ehrTables.courseDetails + ' as CD', 'CD.Course_Id', 'EE.Education_Type')
        .leftJoin(ehrTables.eduInstitution + ' as EIT', 'EIT.Institution_Id', 'EE.Institution_Id')
        .leftJoin(ehrTables.eduSpecialization + ' as ESZ', 'ESZ.Specialization_Id', 'EE.Specialization_Id')
        .whereIn('EE.Employee_Id', employeeIds);

        if(employeeEducationList && employeeEducationList.length > 0) {

            const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-employee-education-data-to-staging`, authentication, employeeEducationList, "EducationInfo");

            return {
                Sync_Status: response.status ? 'Success' : 'Failed',
                Failure_Reason: response.status ? null : response.message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };

        } else {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }

    } catch (error) {
        console.error('Error pushToEmployeeEducationData() function main catch block.', error);
        throw error;
    }
}

async function pushToEmployeeAccreditationsData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode}) {
    try {

        let employeeAccreditationsList = await organizationDbConnection(ehrTables.employeeAccreditationDetails + ' as EAD')
        .select([
            'EJ.User_Defined_EmpId as EmployeeCode', 'ACAT.Accreditation_Type as LicenseType', 'EAD.Exam_Rating as ExamRating', 
            'EAD.Exam_Date_Year as ExamDateYear', 'EAD.Exam_Date_Month as ExamDateMonth',
            'EAD.Identifier as LicenseNumber', 'EAD.Received_Date as DateReleased', 'EAD.Expiry_Date as DateExpiry',
            'EAD.File_Name as DocumentName', organizationDbConnection.raw('\'\' AS DocumentURL'),
        ])
        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EAD.Employee_Id')
        .innerJoin(ehrTables.accreditationCategoryAndType + ' as ACAT', 'ACAT.Accreditation_Category_And_Type_Id', 'EAD.Accreditation_Category_And_Type_Id')
        .whereIn('EAD.Employee_Id', employeeIds);

        if(employeeAccreditationsList && employeeAccreditationsList.length) {

            for(let employeeAccreditations of employeeAccreditationsList){
                employeeAccreditations.DocumentURL = await getBase64Attachment(employeeAccreditations.DocumentName, orgCode)
                employeeAccreditations.DocumentName = employeeAccreditations.DocumentName ? employeeAccreditations.DocumentName?.split("?").pop() : ""; 
            }

            const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-employee-licenses-data-to-staging`, authentication, employeeAccreditationsList, "AccreditationInfo");
            return {
                Sync_Status: response.status ? 'Success' : 'Failed',
                Failure_Reason: response.status ? null : response.message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
            
        } else {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }

    } catch (error) {
        console.error('Error pushToEmployeeAccreditationsData() function main catch block.', error);
        throw error;
    }
}


async function pushToEmployeeAdditionalData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL, candidateId}) {
    try {
        const employeeJobQuery = organizationDbConnection(ehrTables.empJob + ' as EJ')
            .select(
                'EJ.User_Defined_EmpId as EmployeeCode', 'GE.Gender_Expression_Id as GenderExpressionID', 'GI.Gender_Identity_Id as GenderIdentityID',
                'GPR.Gender_Pronoun_Id as GenderPronounID', 'GO.Gender_Orientations_Id as SexualOrientationID', 'EJ.Probation_Date AS EndDate',
                organizationDbConnection.raw('\'\' AS IsIntern'),
                organizationDbConnection.raw('\'\' AS IsRehire'),
                organizationDbConnection.raw('\'\' AS IsPWD'),
                organizationDbConnection.raw('\'\' AS IsCPA'),
                organizationDbConnection.raw('\'\' AS PWDID'),
                organizationDbConnection.raw('\'\' AS ReferralEmployeeCode'),
                organizationDbConnection.raw('\'\' AS Currency'),
                organizationDbConnection.raw('\'\' AS ReferralBonus'),
                organizationDbConnection.raw('\'\' AS SignInBonus'),
               
                organizationDbConnection.raw('CONCAT_WS(EPI1.Emp_First_Name, EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) AS OnboardingSpecialist'),
                organizationDbConnection.raw('CASE WHEN ECD.Emergency_Contact_Name IS NOT NULL THEN ECD.Emergency_Contact_Name ELSE "" END AS EmergencyContactName'), 
                organizationDbConnection.raw('CASE WHEN ECD.Fax_No THEN ECD.Fax_No ELSE "" END AS EmergencyContactNumber'),
                'EPI.Statutory_Insurance_Number AS PhilHealthID', 'EPI.PRAN_No AS HDMF_ID', 'EPI.Aadhaar_Card_Number AS SSS_Number', 'EPI.PAN AS TIN', 
            )
            .innerJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'EJ.Employee_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EPI1', 'EPI1.Employee_Id', 'EJ.Added_By')
            .leftJoin(ehrTables.genderPronoun + ' as GPR', 'GPR.Gender_Pronoun_Name', 'EPI.Pronoun')
            .leftJoin(ehrTables.genderIdentity + ' as GI', 'GI.Gender_Identity_Id', 'EPI.Gender_Identity_Id')
            .leftJoin(ehrTables.genderExpression + ' as GE', 'GE.Gender_Expression_Id', 'EPI.Gender_Expression_Id')
            .leftJoin(ehrTables.genderOrientations + ' as GO', 'GO.Gender_Orientations_Name', 'EPI.Gender_Orientations')
            .leftJoin(ehrTables.contactDetails + ' as ECD', 'ECD.Employee_Id', 'EPI.Employee_Id')
            .whereIn('EJ.Employee_Id', employeeIds)
           

        let [additionalDetails, employeeSalary, recruitmentDetails, customFieldObject] = await Promise.all([
            employeeJobQuery,
            getEmployeeSalary(organizationDbConnection, candidateId),
            fetchCandidateRecruitmentDetails(organizationDbConnection, candidateId),
            getCustomFieldValues(organizationDbConnection, employeeIds, candidateId)
        ]);

        if (!additionalDetails || additionalDetails.length ===0) {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }

        additionalDetails = additionalDetails[0]

        Object.assign(additionalDetails, {
            RecruiterAssigned: recruitmentDetails?.RecruiterAssigned || "",
            HiringManager: recruitmentDetails?.HiringManager || "",
            MonthlyBasicSalaryOffered: employeeSalary?.basicPay || 0,
            SignInBonus: employeeSalary?.signInBonus || "",
            ReferralBonus:  customFieldObject?.ReferralBonus || "",
            Currency: recruitmentDetails?.Currency || "",
            IsIntern: customFieldObject?.IsIntern || false,
            IsRehire: customFieldObject?.IsRehire || false,
            IsPWD: customFieldObject?.IsPWD || false,
            IsCPA: customFieldObject?.IsCPA ||false,
            PWDID: customFieldObject?.PWDID || '',
            ReferralEmployeeCode: customFieldObject?.ReferralEmployeeCode || '',
        });

        let additionalDetailsList = [];
        additionalDetailsList.push(additionalDetails);

        const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-employee-additional-info-data-to-staging`, authentication, additionalDetailsList, "AdditionalInfo");

        return {
            Sync_Status: response.status ? 'Success' : 'Failed',
            Failure_Reason: response.status ? null : response.message.toString(),
            Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
        };

    } catch (error) {
        console.error('Error pushToEmployeeAdditionalData() function main catch block.', error);
        throw error;
    }
}


async function getEmployeeSalary(organizationDbConnection, candidateId) {
    try {
        console.log("Inside getEmployeeSalary() function ")
        const employeeSalaryDetails = await organizationDbConnection('emp_generated_documents')
        .select('Additional_Details').whereNotNull('Additional_Details')
        .where('Candidate_Id', candidateId)
        .andWhere(function() {
            this.where('Additional_Details', 'like', '%basicPay%')
            .orWhere('Additional_Details', 'like', '%signInBonus%');
        });
        if(employeeSalaryDetails && employeeSalaryDetails.length > 0){
            let additional = JSON.parse(employeeSalaryDetails[0].Additional_Details);
            
            if(typeof additional === 'string'){
                additional = JSON.parse(additional)
            }
            return additional;
        }
        return {};
    } catch(error){
        console.error("Error Occured While getting from getEmployeeSalary() main catch block ", error);
        throw error;
    }
}

async function fetchCandidateRecruitmentDetails(organizationDbConnection, candidateId) {
    try {
        console.log("Inside fetchCandidateRecruitmentDetails() function ");

        const data = await organizationDbConnection(ehrTables.candidateRecruitmentInfo + ' as CRI')
            .select( 'CU.Currency_Name as Currency',
                organizationDbConnection.raw('GROUP_CONCAT(CONCAT_WS(EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name)) as RecruiterAssigned, GROUP_CONCAT(CONCAT_WS(EMP2.Emp_First_Name, EMP2.Emp_Middle_Name, EMP2.Emp_Last_Name)) as HiringManager')
            )
            .leftJoin(ehrTables.jobPost + ' as JP', 'JP.Job_Post_Id', 'CRI.Job_Post_Id')
            .leftJoin(ehrTables.currency + ' as CU', 'CU.Currency_Id', 'JP.Currency')
            .leftJoin(ehrTables.jobPostRecruiters + ' as JPR', 'JPR.Job_Post_Id', 'CRI.Job_Post_Id')
            .leftJoin(ehrTables.jobPostHiringManagers + ' as JPM', 'JPM.Job_Post_Id', 'CRI.Job_Post_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'JPR.Recruiter_Id')
            .leftJoin(ehrTables.empPersonalInfo + ' as EMP2', 'EMP2.Employee_Id', 'JPM.Hiring_Manager_Id')
            .where('CRI.Candidate_Id', candidateId)
            .groupBy('CRI.Job_Post_Id');

        return data[0] || {};
    } catch (error) {
        console.error("Error Occured While getting from fetchCandidateRecruitmentDetails() main catch block ", error);
        throw error;
    }
}

async function getCustomFieldValues(organizationDbConnection, employeeIds, candidateId) {
    try {
        const [teamSummaryCustomValues, jobPostCustomValue, customFieldAssociated] = await Promise.all([
            organizationDbConnection(ehrTables.teamSummaryCustomFieldValues + ' as TSCV')
                .pluck('TSCV.Custom_Field_Value')
                .whereNotNull('TSCV.Custom_Field_Value')
                .whereIn('TSCV.Primary_Id', employeeIds),

            organizationDbConnection('job_post_custom_field_values as JPCFV').pluck('JPCFV.Custom_Field_Value')
            .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Job_Post_Id', 'JPCFV.Primary_Id')
            .where('CRI.Candidate_Id', candidateId),
    
            organizationDbConnection('custom_field_associated_forms as CFAF')
                .select('CFAF.Integration_Mapping_Key', 'CFAF.Custom_Field_Id', 'CF.Custom_Field_Type')
                .innerJoin('custom_fields as CF', 'CF.Custom_Field_Id', 'CFAF.Custom_Field_Id')
                .whereIn('CFAF.Form_Id', ['243', '15'])
                .whereIn('CFAF.Integration_Mapping_Key', ['IsIntern', 'IsRehire', 'IsPWD', 'IsCPA', 'PWDID', 'ReferralEmployeeCode', 'ReferralBonus'])
        ]);

        const teamSummary = teamSummaryCustomValues && teamSummaryCustomValues.length ? JSON.parse(teamSummaryCustomValues[0]) : {}
        const jobPost = jobPostCustomValue && jobPostCustomValue.length ? JSON.parse(jobPostCustomValue[0]) : {}
        
        const parsedValues = {
            ...teamSummary,
            ...jobPost
          };

        if(customFieldAssociated && customFieldAssociated.length > 0) {
            return customFieldAssociated.reduce((acc, { Integration_Mapping_Key, Custom_Field_Id, Custom_Field_Type }) => {

                if(Custom_Field_Type && Custom_Field_Type.toLowerCase() == "single choice"){
                    acc[Integration_Mapping_Key] = parsedValues[Custom_Field_Id]?.toLowerCase() === 'yes';
                } else {
                    acc[Integration_Mapping_Key] = parsedValues[Custom_Field_Id] || '';
                }
                return acc;
            }, {});
        }
        return  { IsIntern: false, IsRehire: false, IsPWD: false, IsCPA: false, PWDID: '', ReferralEmployeeCode: '', ReferralBonus: ''};

    } catch (error) {
        console.error("Error occurred in getCustomFieldValues() main catch block", error);
        throw error;
    }
}
  
  

async function pushToPreviousEmploymentData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL}) {

    try {

        const employeePreviousEmploymentList = await organizationDbConnection(ehrTables.empExperience + ' as EEP')
        .select([
            'EJ.User_Defined_EmpId as EmployeeCode', 'EEP.Prev_Company_Name as EmployerName', 'EEP.Designation as Position', 
            'EEP.Start_Date_Join as StartDate', 'EEP.End_Date as EndDate', 'EEP.Prev_Company_Location as Location',
            organizationDbConnection.raw(`
                JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'Reference_Email', EER.Reference_Email,
                        'Reference_Name', EER.Reference_Name,
                        'Reference_Number', EER.Reference_Number
                    )
                ) AS characterReferencesList
            `)
        ])
        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EEP.Employee_Id')
        .leftJoin(ehrTables.empExperienceReference + ' as EER', 'EER.Experience_Id', 'EEP.Experience_Id')
        .whereIn('EEP.Employee_Id', employeeIds).groupBy('EEP.Experience_Id');

        if(employeePreviousEmploymentList && employeePreviousEmploymentList.length) {

            const transformReferences = (result, references) => {
                if (references) {
                    references = JSON.parse(references);
                    references.forEach((ref, index) => {
                        const idx = index + 1;
                       if(idx < 3){
                            result[`CharacterReferenceName${idx}`] = ref.Reference_Email;
                            result[`CharacterReferenceNo${idx}`] = ref.Reference_Name;
                            result[`CharacterReferenceEmail${idx}`] = ref.Reference_Number;
                       }
                        
                    });
                }else {
                    result[`CharacterReferenceName1`] = "";
                    result[`CharacterReferenceNo1`] = "";
                    result[`CharacterReferenceEmail1`] = "";
                    result[`CharacterReferenceName2`] = "";
                    result[`CharacterReferenceNo2`] = "";
                    result[`CharacterReferenceEmail2`] = "";

                }
                return result;
            };

            employeePreviousEmploymentList.forEach(item => {
                item = transformReferences(item, item.characterReferencesList);
                delete item.characterReferencesList;
            });

            const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-employee-previous-employment-data-to-staging`, authentication, employeePreviousEmploymentList, "PreviousEmploymentInfo");
            return {
                Sync_Status: response.status ? 'Success' : 'Failed',
                Failure_Reason: response.status ? null : response.message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };

        } else {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }
    } catch (error) {
        console.error('Error pushToPreviousEmploymentData() function main catch block.', error);
        throw error;
    }
}


async function pushToEmployeeLanguageData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL}) {

    try {

        const employeeLanguageList = await organizationDbConnection(ehrTables.empLanguage + ' as EL')
        .select([
            'EJ.User_Defined_EmpId as EmployeeCode','EL.Lang_Known as LanguageKnownID', 
            'EL.Lang_Spoken as LanguageSpoken', 'EL.Lang_Read_Write as LanguageWritten', 'EL.Lang_Proficiency as LanguageProficiency'
        ])
        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EL.Employee_Id')
        .whereIn('EL.Employee_Id', employeeIds).whereNotNull('EL.Lang_Known');

        if(employeeLanguageList && employeeLanguageList.length > 0) {

            const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-languageknown-to-staging`, authentication, employeeLanguageList, "LanguageInfo");

            return {
                Sync_Status: response.status ? 'Success' : 'Failed',
                Failure_Reason: response.status ? null : response.message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };

        } else {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }


    } catch (error) {
        console.error('Error pushToEmployeeLanguageData() function main catch block.', error);
        throw error;
    }
}

async function pushToEmployeeCertificateData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL}) {

    try {

        const employeeCertificateList = await organizationDbConnection(ehrTables.empCertifications + ' as EC')
        .select([
            'EJ.User_Defined_EmpId as EmployeeCode', 'EC.Certification_Name as CertificationName', 'EC.Received_Date as ReceivedOn', 
            'EC.Certificate_Received_From as ReceivedFrom', 'EC.Ranking as Ranking'
        ])
        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EC.Employee_Id')
        .whereIn('EC.Employee_Id', employeeIds);

        if(employeeCertificateList && employeeCertificateList.length > 0) {

            const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-certification-details-to-staging`, authentication, employeeCertificateList, "CertificationInfo");

            return {
                Sync_Status: response.status ? 'Success' : 'Failed',
                Failure_Reason: response.status ? null : response.message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };

        } else {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }

    } catch (error) {
        
    }
}

async function pushToAttachmentFileData(employeeIds, {organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode}) {

    try {

        const attachment = [
            organizationDbConnection(ehrTables.empDocumentCategory + " as EDC")
            .select( 
                'EJ.User_Defined_EmpId as EmployeeNo', 'ED.File_Name as FileName', 
                'DST.Document_Sub_Type as DocumentName','DC.Category_Fields as DocumentCategory', 
                'DT.Document_Type as DocumentType', 'EDC.Document_Id as DocumentId', 'EDC.Document_Sub_Type_Id as DocumentSubTypeId',
                'EDC.Employee_Id as EmployeeId'
            )
            .innerJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'EDC.Document_Sub_Type_Id')
            .leftJoin(ehrTables.documentType + ' as DT', 'DT.Document_Type_Id', 'DST.Document_Type_Id')
            .leftJoin(ehrTables.documentCategory + ' as DC', 'DC.Category_Id', 'DT.Category_Id')
            .innerJoin(ehrTables.empDocuments + " as ED", "ED.Document_Id", "EDC.Document_Id")
            .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EDC.Employee_Id')
            .where('EDC.Employee_Id', 'in', employeeIds),

            organizationDbConnection('sunfish_attachment_status_log').whereIn('Employee_Id', employeeIds)
        ]

        let [ attachmentInfoList, attachmentStatusLog ]  =  await Promise.all(attachment)
        
        
        if(attachmentInfoList && attachmentInfoList.length > 0 ) {

            let successFlag = true;
            let message = '';

            attachmentInfoList = await filterByAttachmentStatusLog(attachmentInfoList, attachmentStatusLog)

            for(let employeeDocument of attachmentInfoList){

                let documentId = employeeDocument.DocumentId;
                let documentSubTypeId = employeeDocument.DocumentSubTypeId;
                let employeeId = employeeDocument.EmployeeId;

                delete employeeDocument.DocumentId;
                delete employeeDocument.DocumentSubTypeId;
                delete employeeDocument.EmployeeId;

                employeeDocument.ContentBase64 = await getBase64Attachment(employeeDocument.FileName, orgCode)
                employeeDocument.FileName = employeeDocument.FileName ? employeeDocument.FileName?.split("?").pop() : ""; 
                const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/upload-file`, authentication, [employeeDocument], "Attachment");

                if(response.status){
                    const documentLog = {
                        LOGID: documentSubTypeId,
                        EMPNO: employeeId,
                        COMPANYID: 'PAGT',
                        DOCUMENTNO: documentId
                    }
                    await addSuccessAttachmentLog(organizationDbConnection, documentLog);
                }else{
                    let errorMessage = `${employeeDocument.DocumentName} Document is failed to upload- ${response.message.toString()}`;
                    message = message ? message + ', '+ errorMessage : errorMessage;
                    successFlag = false;
                }
            }

            return {
                Sync_Status: successFlag ? 'Success' : 'Failed',
                Failure_Reason: successFlag ? null : message.toString(),
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        } else {
            return {
                Sync_Status: 'Success',
                Failure_Reason: '',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            };
        }

    } catch (error) {
        console.error('Error pushToAttachmentFileData() function main catch block.', error);
        throw error;
    }
}

async function addSuccessAttachmentLog(organizationDbConnection, employeeDocument){

    try{
        await organizationDbConnection('sunfish_attachment_status_log')
        .insert({
            Log_Id: employeeDocument.LOGID,
            Employee_Id: employeeDocument.EMPNO,
            Company_Id: employeeDocument.COMPANYID,
            Document_No:employeeDocument.DOCUMENTNO
        })
    } catch(err){
        console.error("Error: addSuccessAttachmentLog catch block ", err)
    }
}

function filterByAttachmentStatusLog(dataList, statusLog) {
    return dataList.filter(data => 
      !statusLog.some(item2 => 
        data.DocumentSubTypeId == item2.Log_Id && 
        "PAGT" == item2.Company_Id && 
        data.DocumentId == item2.Document_No && 
        data.EmployeeId == item2.Employee_Id
      )
    );
  }

async function getBase64Attachment(fileKey, orgCode) {

    const AWS = require('aws-sdk');
    const s3 = new AWS.S3({ region: process.env.region });

    try {
        fileKey = `${process.env.domainName}/${orgCode}/Employees Document Upload/${fileKey}`;
        console.log(fileKey)
        const data = await s3.getObject({Bucket: process.env.documentsBucket, Key: fileKey}).promise();
        const base64String = data.Body.toString('base64');
        return base64String;
      } catch (error) {
        return  '';
      }
}