// Require necessary libraries and modules
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib; // Common library for accessing shared functions
const knex = require('knex'); // Database query builder
const { ApolloError } = require('apollo-server-lambda'); // To handle errors in Apollo Server
const { ehrTables } = require('../../../common/tablealias'); // Alias for database table names
const { getDuplicateRecords } = require('../../common/commonFunction');
const {formId} = require('../../../common/appConstants');

module.exports.moveBlackedListToCandidate = async (parent, args, context, info) => {
    console.log("Inside moveBlackedListToCandidate function.");
    let organizationDbConnection;
    try {

        organizationDbConnection = knex(context.connection.OrganizationDb);
        const loginEmployeeId = context.Employee_Id; // Extract employee ID from context

        // Validate input arguments
        if (!args || !args.candidateId) {
            throw 'IVE0000'; // Throw an error if candidateId is missing
        }

        // Check if the employee has the rights to update
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formId.jobCandidate
        );

        // If the employee has update rights
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            // Fetch the candidate data from the database
            const candidateData = await organizationDbConnection(ehrTables.candidateRecruitmentInfo + ' as CRI')
                .select('CRI.Blacklisted', 'CPI.Emp_First_Name', 'CPI.Emp_Middle_Name', 'CPI.Emp_Last_Name', 'CPI.Personal_Email as emailId', 
                    'CPI.DOB as dob', 'CCD.Mobile_No as mobileNo')
                .innerJoin(ehrTables.candidatePersonalInfo + ' as CPI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                .leftJoin(ehrTables.candidateContactDetails + ' as CCD', 'CCD.Candidate_Id', 'CPI.Candidate_Id')
                .where('CPI.Candidate_Id', args.candidateId)
                .first();

            if (!candidateData) {
                throw 'EI00224'; //Sorry! An error occurred while processing the candidate details. Please try again.
            }

            if (candidateData && candidateData.Blacklisted.toLowerCase() === 'no') {
                throw 'EI00227'; 
            }

            const candidateIds = await organizationDbConnection(ehrTables.candidatePersonalInfo).pluck('Candidate_Id')
            .where('Personal_Email', candidateData.emailId)

            await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
            .whereIn('Candidate_Id', candidateIds)
            .update({
                Blacklisted: 'No',
                Blacklisted_On: null,
                Blacklisted_By: null,
                Blacklisted_Reason_Id: null,
                Blacklisted_Comments: null,
                Blacklisted_Attachment_File_Name: null
            });


             const systemLogParam = candidateIds.map(candidateId=> {
                return {
                    userIp: context.User_Ip,
                    employeeId: loginEmployeeId,
                    changedData: args,
                    organizationDbConnection: organizationDbConnection,
                    formId: formId.jobCandidate,
                    action: `Rollback`,
                    isEmployeeTimeZone: 0,
                    uniqueId: candidateId,
                    message: `The candidate has been removed to the blacklist.`
                }
            });

            await commonLib.func.createMultiSystemLogActivities(systemLogParam);        

            // Return a success message
            return { errorCode: '', message: 'The candidate has been removed from blacklisted.' };
        } else {
            throw '_DB0102'; // Error for lack of edit access rights
        }
    } catch (e) {
        console.error('Error while moveBlackedListToCandidate main catch block.', e);
        // Get a user-friendly error message
        const errResult = commonLib.func.getError(e, 'EI00213');  //Oops! An error occurred while updating the candidate details. Please try again later.
        // Throw an ApolloError with the error message
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Destroy the database connection to free up resources
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}


