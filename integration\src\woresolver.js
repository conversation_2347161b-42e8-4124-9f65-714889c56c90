//const getIrukkaStatus = require('./roresolvers/camuIntegration/getIrukkaStatus');
const addUpdateRecruitmentStatus = require('./roresolvers/irukkaIntegration/addUpdateRecruitmentStatus');
const closeJobPost = require('./roresolvers/irukkaIntegration/closeJobPost');
const sfaIntegrationEmployeeDetails = require('./woresolvers/sfapiIntegration/sfaIntegrationEmployeeDetails');
const closeJobpostToIndeed = require('./woresolvers/indeedIntegration/closeJobpostToIndeed');
const publishJobPostToIndeed = require('./woresolvers/indeedIntegration/publishJobPostToIndeed');
const addUpdateJobPostJobStreet = require('./woresolvers/jobStreetIntegrationMutation/addUpdateJobPost.js');
const updateJobOpeningDetails = require('./woresolvers/jobStreetIntegrationMutation/updateJobOpeningDetails.js');
const closeJobStreetJob = require('./woresolvers/jobStreetIntegrationMutation/closeJobStreetJob.js');
const addUpdateHiringForecast = require('./woresolvers/mppIntegration/addUpdateHiringForecast.js');
const addUpdateForeCastSettings = require('./woresolvers/mppIntegration/addUpdateForeCastSettings.js');
const addUpdateReqruitmentPosition = require('./woresolvers/mppIntegration/addUpdateReqruitmentPosition.js');
const addUpdateNewPosition = require('./woresolvers/mppIntegration/addUpdateNewPosition.js');
const updatePositionJobSummary = require('./woresolvers/sfapiIntegration/updatePositionJobSummary.js');
const addUpdateExperience = require('./woresolvers/mppIntegration/addUpdateNewPositionExperience.js');
const addUpdateWorkingConditions = require('./woresolvers/mppIntegration/addUpdateWorkingCondition.js');
const updateExtIntNewPosition = require('./woresolvers/mppIntegration/updateExtIntNewPosition.js');
const addUpdateEducationRequirementsDescriptions = require('./woresolvers/mppIntegration/addUpdateEducationRequirementsDescriptions.js');
const addUpdateDutiesResponsibilities = require('./woresolvers/mppIntegration/addUpdateDutiesResponsibilities.js');
const updateNewPositionChangeStatus = require('./woresolvers/mppIntegration/updateNewPositionChangeStatus.js');
const deleteOpenPositionSubTable = require('./woresolvers/mppIntegration/deleteOpenPositionSubTable.js');
const jobStreetCompanyIdStatusUpdate = require('./woresolvers/jobStreetIntegrationMutation/jobStreetCompanyIdStatusUpdate.js');
const addUpdateTalentPool = require('./woresolvers/talentPool/addUpdateTalentPool.js');
const deleteTalentPoolDetails = require('./woresolvers/talentPool/deleteTalentPoolDetails.js');
const addCandidateToTalentPool = require('./woresolvers/talentPool/addCandidateToTalentPool.js');
const archiveCandidateDetails = require('./woresolvers/talentPool/archiveCandidateDetails.js');
const deleteHiringForeCast = require('./woresolvers/mppIntegration/deleteHiringForeCast.js')
const moveToJobPost = require('./woresolvers/talentPool/moveToJobPost.js');
const uploadBulkCandidateDetails = require('./woresolvers/uploadBulkCandidateDetails.js');
const moveToAnotherTalentPool = require('./woresolvers/talentPool/moveToAnotherTalentPool.js');
const moveArchiveToCandidate = require('./woresolvers/talentPool/moveArchiveToCandidate.js');
const deleteJobStreetAccount = require('./woresolvers/jobStreetIntegrationMutation/deleteJobStreetAccount.js');
const addUpdateMicrosoftIntegration = require('./woresolvers/microsoft/addUpdateMicrosoftIntegration.js');
const moveCandidateToBlackedList = require('./woresolvers/talentPool/moveCandidateToBlackedList.js');
const moveBlackedListToCandidate = require('./woresolvers/talentPool/moveBlackedListToCandidate.js');
const generateSalaryInformation = require('./woresolvers/syntrum/generateSalaryInformation.js');
const generateSyntrumPayslipDetails = require('./woresolvers/syntrum/generateSyntrumPayslipDetails.js');
const updateWorkflowApprovalSetting = require('./woresolvers/mppIntegration/updateWorkflowApprovalSetting.js');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        //getIrukkaStatus,
    ),
    Mutation: Object.assign({},
        addUpdateRecruitmentStatus,
        closeJobPost,
        publishJobPostToIndeed,
        sfaIntegrationEmployeeDetails,
        addUpdateJobPostJobStreet,
        updateJobOpeningDetails,
        closeJobStreetJob,
        closeJobpostToIndeed,
        publishJobPostToIndeed,
        addUpdateHiringForecast,
        addUpdateForeCastSettings,
        addUpdateReqruitmentPosition,
        addUpdateNewPosition,
        updatePositionJobSummary,
        addUpdateExperience,
        addUpdateWorkingConditions,
        updateExtIntNewPosition,
        addUpdateEducationRequirementsDescriptions,
        addUpdateDutiesResponsibilities,
        updateNewPositionChangeStatus,
        addUpdateDutiesResponsibilities,
        deleteOpenPositionSubTable,
        jobStreetCompanyIdStatusUpdate,
        addUpdateTalentPool,
        deleteTalentPoolDetails,
        addCandidateToTalentPool,
        archiveCandidateDetails,
        deleteHiringForeCast,
        moveToJobPost,
        uploadBulkCandidateDetails,
        moveToAnotherTalentPool,
        moveArchiveToCandidate,
        deleteJobStreetAccount,
        addUpdateMicrosoftIntegration,
        moveCandidateToBlackedList,
        moveBlackedListToCandidate,
        generateSalaryInformation,
        generateSyntrumPayslipDetails,
        updateWorkflowApprovalSetting
    )
}
exports.resolvers = resolvers;