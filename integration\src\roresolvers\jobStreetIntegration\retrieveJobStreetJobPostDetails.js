// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formId } = require('../../../common/appConstants');
const retrieveJobPostData = async (organizationDbConnection, jobPostId) => {
    return await organizationDbConnection(ehrTables.jobStreetJobProfile + " as JSP")
    .select('JSP.Job_Street_Id AS jobStreetId',
        'JSO.Job_Street_Id AS jobStreetIdOpening',
    'JSP.Job_Post_Id AS JobPostId',
    'JSP.Video_Position_Code AS videoPositionCode',
    'JSP.Video_Url AS videoUrl',
    'JSP.Position_Location_Id AS positionLocationId',
    'JSP.Seek_Advertisement_Product_Id AS seekAdvertisementProductId',
    'JSP.Sub_Category_Id AS subCategoryId',
    'JSP.Category_Id AS categoryId',
    'JSP.Seek_Work_Type_Code AS seekWorkTypeCode',
    'JSP.Seek_Work_Arrangement_Codes AS seekWorkArrangementCodes',
    'JSP.Advertisement_Branding AS advertisementBranding',
    'JSP.Seek_Billing_Reference AS seekBillingReference',
    'JSP.Profile_Id AS profileId',
    'JSP.Applied_Status AS appliedStatus',
    'JSP.Added_By AS addedBy',
    'JSP.Added_On AS addedOn',
    'JSP.Updated_On AS updatedOn',
    'JSP.Search_Bullet_Points_Array AS searchBulletPointsArray',
    'JSP.Pay_Description as payDescription',
   'JSO.Recruiter_Phone_No AS phoneNo',
    'JSP.Hirer_Id AS hirerId',
    'JSP.Updated_By AS updatedBy',
    'JSO.Role_Code as roleCode',
    'Currency_Id as currencyId','Currency_Code as currencyCode','Currency_Name as currencyName',
    'JSO.Recruiter_Name as recruiterName',
    'JSO.Recruiter_Email_Id as recruiterEmailId',
'JSP.Job_Title AS jobTitle','JP.Min_Payment_Frequency AS minimumAmount',
'JSO.Recruiter_No_Country_Code as recruiterNoCountryCode',
'JP.Max_Payment_Frequency AS maximumAmount','JSP.Search_Summary_Description AS jobSummary')
    .where('JSP.Job_Post_Id', jobPostId)
    .where('JSP.Applied_Status','Applied')
    .innerJoin(ehrTables.jobStreetJobOpenings + " as JSO",'JSP.Job_Post_Id','JSO.Job_Post_Id')
    .leftJoin(ehrTables.jobPost + " as JP",'JP.Job_Post_Id','JSP.Job_Post_Id')
    .leftJoin(ehrTables.currency + " as C",'C.Currency_Id','JP.Currency');
};

module.exports.retrieveJobStreetJobPostDetails = async (parent, args, context, info) => {
    console.log('Inside retrieveJobStreetJobPostDetails function');
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formId.jobpost);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const jobPostDetails = await retrieveJobPostData(organizationDbConnection, args.jobPostId);
            // Destroy DB connection
            organizationDbConnection && organizationDbConnection.destroy();
            return {
                errorCode: '',
                message: 'job street jobpost details have been retrieved successfully.',
                data: jobPostDetails && jobPostDetails.length? jobPostDetails: []
            };
        } else {
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }
    } catch (e) {
        // Destroy DB connection
        organizationDbConnection && organizationDbConnection.destroy();
        console.log('Error in retrieveJobStreetJobPostDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'EI00126');
        throw new ApolloError(errResult.message, errResult.code);
    }
};