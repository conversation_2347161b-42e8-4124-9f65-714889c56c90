
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants')
const moment = require('moment');

module.exports.addUpdateForeCastSettings = async (parent, args, context, info) => {

    console.log("Inside addUpdateForeCastSettings function.")
    let organizationDbConnection, validationError= {};
    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId.hiringForeCastSettings);
    
        if(Object.entries(checkRights).length > 0 && (checkRights.Role_Add === 1  || checkRights.Role_Update===1) &&( checkRights.Employee_Role.toLowerCase() === 'admin' ||  (checkRights.Is_Recruiter && checkRights.Is_Recruiter.toLowerCase() === 'yes'))){
            if(!args.releaseDate){
                validationError['IVE0480'] = commonLib.func.getError('IVE0480', '').message;
                throw 'IVE0000';
            }else if((moment.utc().startOf('day').isAfter(moment.utc(args.releaseDate).startOf('day')))){
                validationError['IVE0480'] = commonLib.func.getError('IVE0480', '').message1;
                throw 'IVE0000';
            }else if(!args.endMonth){
                validationError['IVE0480'] = commonLib.func.getError('IVE0480', '').message2;
                throw 'IVE0000';
            }
            return (
                await organizationDbConnection.transaction(async function(trx){

                if(args.forecastSettingsId){

                    await organizationDbConnection(ehrTables.mppForecastSettings).transacting(trx)
                    .update({
                        Release_Date: args.releaseDate,
                        End_Month: args.endMonth,
                        Updated_By: employeeId,
                        Updated_On: moment().utc().format('YYYY-MM-DD')
                    }).where('Forecast_Settings_Id', args.forecastSettingsId)

                }else{
                   
                   let Ids= await organizationDbConnection(ehrTables.mppForecastSettings).transacting(trx)
                    .insert({
                        Release_Date: args.releaseDate,
                        End_Month: args.endMonth,
                        Added_By: employeeId,
                        Added_On: moment().utc().format('YYYY-MM-DD')
                    })
                    args.forecastSettingsId = Ids
                }

                await organizationDbConnection(ehrTables.mppForecastSettingsRole).transacting(trx)
                .del().where('Forecast_Settings_Id', args.forecastSettingsId);

                if(args.roleIds && args.roleIds.length > 0){
                    let rolesWithSettings = args.roleIds.map(roleId => ({
                        Role_Id: roleId,
                        Forecast_Settings_Id: args.forecastSettingsId
                    }));
                    await organizationDbConnection(ehrTables.mppForecastSettingsRole).transacting(trx)
                    .insert(rolesWithSettings)  
                }

                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode: "", message: "Hiring forecast settings added/updated successfully."};
            }))
            
        }else{
            throw '_DB0111'
        }
    }catch(err){
        //Destroy DB connection
        console.error('Error in addUpdateForeCastSettings function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(err === 'IVE0000'){
            let errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            err = err && err.code === 'ER_DUP_ENTRY' ? 'SET0118': err; //Sorry, already exists for this combination of role and hiring forecast settings. please contact the platform administrator.
            let errResult = commonLib.func.getError(err, 'SET0119'); //Sorry, an error occurred while adding/updating the hiring forecast settings details. Please contact the platform administrator.
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}