// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
const axios = require('axios');
//Require moment
const moment = require('moment-timezone');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const { validatCommonRuleInput, approvedPositionValidationSkip, sendApprovedPositionEmail, approvedAndForecastpositiondetails, getEmployeeEmail } = require('../../common/commonFunction');

module.exports.addUpdateReqruitmentPosition = async (parent, args, context) => {
  let validationError = {};
  try {
    const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
  let workflowEnabled,instanceData;
  let organizationDbConnection = knex(context.connection.OrganizationDb);
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId.reqruitmentRequest
    );
    if (Object.keys(checkRights).length <= 0 || (args.recruitmentId && checkRights.Role_Update === 0) || (!args.recruitmentId && checkRights.Role_Add === 0)) {
     throw !args.recruitmentId && checkRights.Role_Add === 0 ? '_DB0101' : '_DB0102';
    }

    const {
      recruitmentId,
      originalPositionId,
      positionCode,
      positionTitle,
      groupName,
      groupCode,
      divisionCode,
      divisionName,
      departmentCode,
      departmentName,
      sectionCode,
      sectionName,
      costCenter,
      employeeType,
      employeeTypeName,
      noOfPosition,
      workflowId,
      reasonForRequest,
      reasonForReplacement,
      PositionLevel
    } = args;
    const fieldValidations = {
    }
    if (+args.noOfPosition > 500) {
      validationError['IVE0488'] = commonLib.func.getError('IVE0488', '').message;
      throw 'IVE0000';
    }

    let [approvedPositionValidation, noOfPositionValidate] = await Promise.all([
      approvedPositionValidationSkip(organizationDbConnection),
      noOfPositionValidation(organizationDbConnection, args)
    ])

    approvedPositionValidation = approvedPositionValidation && 
    approvedPositionValidation.Warm_Bodies_Include_Notice_Period.toLowerCase() === 'yes' 
    && approvedPositionValidation.Allow_Hire_Without_Approved_Vacancy.toLowerCase() === 'yes'
    
    if (!(approvedPositionValidation) && !(noOfPositionValidate)) {
      validationError['IVE0489'] = commonLib.func.getError('IVE0489', '').message;
      throw 'IVE0000';
    }
    if(reasonForRequest){
      fieldValidations.reasonForRequest='IVE0506'
    }
    if(reasonForReplacement){
      fieldValidations.reasonForReplacement='IVE0507'
    }
    validationError = await validatCommonRuleInput(
      args,
      fieldValidations
    )
    if (Object.keys(validationError).length > 0) {
      throw 'IVE0000'
    }

    const mppRecruitmentRequestData = {
      Recruitment_Id: recruitmentId,
      Original_Position_Id: originalPositionId,
      Group_Code: groupCode || null,
      Division_Code: divisionCode || null,
      Department_Code: departmentCode || null,
      Section_Code: sectionCode || null,
      Cost_Center: costCenter || null,
      Employee_Type: employeeType || null,
      No_Of_Position: noOfPosition,
      Workflow_Id: workflowId || null,
      Custom_Group_Id: args.customGroupId || null,
      Replacement_For:reasonForRequest || null,
      Reason_For_Replacement:reasonForReplacement || null,
      Position_Level:PositionLevel || null,
    };

    instanceData = {
      originalPositionId: originalPositionId || '',
      positionCode: positionCode || '',
      positionTitle: positionTitle || '',
      groupName: groupName || '',
      groupCode: groupCode || '',
      divisionCode: divisionCode || '',
      divisionName: divisionName || '',
      departmentCode: departmentCode || '',
      departmentName: departmentName || '',
      sectionCode: sectionCode || '',
      sectionName: sectionName || '',
      costCenter: costCenter || '',
      employeeType: employeeType || '',
      employeeTypeName: employeeTypeName || '',
      customGroupId: args.customGroupId || '',
      noOfPosition: noOfPosition || 0
    };
    const workflowSettings = await organizationDbConnection(
      ehrTables.mppForecastSettings
    )
      .first()
      .select('Enable_Workflow_Approval')

    workflowEnabled = workflowSettings?.Enable_Workflow_Approval === 'Yes'

    // Modify status handling
    mppRecruitmentRequestData.Status = workflowEnabled
      ? 'Waiting For Approval'
      : 'Approved';
      instanceData.Approval_Status = workflowEnabled
      ? 'Waiting For Approval'
      : 'Approved';
    let responseObject={}
    if (recruitmentId) {
      let getExistingStatus= await organizationDbConnection(ehrTables.mppRecruitmentRequest)
      .select('Status')
      .where('Recruitment_Id', recruitmentId)
      .first();
      console.log(getExistingStatus.Status,"getExistingStatus")
      if(getExistingStatus.Status.toLowerCase() === 'approved'){
        const jobPosts = await organizationDbConnection(ehrTables.jobPost + ' as JP')
          .select('JP.Job_Post_Id', 'ATS.Status')
          .leftJoin(
            ehrTables.atsStatusTable + ' as ATS',
            'JP.Status',
            'ATS.Id'
          )
          .where('Position_Request_Id', recruitmentId)
          .where('MPP_Position_Type', 'Recruitment Request');
        if (jobPosts && jobPosts.length > 0) {
          const openJobPost = jobPosts.find(jobPost => jobPost.Status.toLowerCase() === 'open');
          if (openJobPost) {
            throw 'EI00228';
          }
      
          for (const jobPost of jobPosts) {
            if (jobPost.Status === 'Closed') {
              const candidateRecord = await organizationDbConnection('candidate_recruitment_info')
                .where('Job_Post_Id', jobPost.Job_Post_Id)
                .first();
                
              if (candidateRecord) {
                throw 'EI00229';
              }
            }
          }
        }
      }

      responseObject = await getWorkflowProcessInstanceId(
        organizationDbConnection,
        recruitmentId
      )
      mppRecruitmentRequestData.Updated_On = moment()
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')
      mppRecruitmentRequestData.Updated_By = loginEmployeeId
      instanceData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
      instanceData.Updated_By = loginEmployeeId
    } else {
      mppRecruitmentRequestData.Added_On = moment()
        .utc()
        .format('YYYY-MM-DD HH:mm:ss')
      mppRecruitmentRequestData.Added_By = loginEmployeeId;
      instanceData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss')
      instanceData.Added_By = loginEmployeeId;
    }
    return await organizationDbConnection.transaction(async (trx) => {
      const reqruitmentPosition =
        recruitmentId !== 0
          ? await organizationDbConnection(ehrTables.mppRecruitmentRequest).transacting(trx).where('Recruitment_Id', recruitmentId).update(mppRecruitmentRequestData)
          : await organizationDbConnection(ehrTables.mppRecruitmentRequest).transacting(trx).insert(mppRecruitmentRequestData);

      const updateResult = await reqruitmentPosition;
      if (!updateResult) throw 'EI00179';
      let lastInsertedId = recruitmentId || reqruitmentPosition[0];
      instanceData.recruitmentId =lastInsertedId;

      if (args.customGroupId) {
        if (recruitmentId) {
            await organizationDbConnection("custom_group_associated_forms")
                .transacting(trx)
                .del()
                .where("Form_Id", formId.jobpost)
                .where("Parent_Id", recruitmentId);
        }
        await organizationDbConnection("custom_group_associated_forms")
            .transacting(trx)
            .insert({
                Parent_Id: lastInsertedId,
                Form_Id: formId.jobpost,
                Custom_Group_Id: args.customGroupId,
            });
    }
    
      if (workflowEnabled && workflowId) {
        await initaiteWorkflow(
          args.eventId,
          instanceData,
          orgCode,
          formId.reqruitmentRequest,
          organizationDbConnection,
          loginEmployeeId,
          trx
        )

        if (recruitmentId) {
          if (responseObject && responseObject[0]?.Process_Instance_Id) {
            await commonLib.func.deleteOldApprovalRecords(
              organizationDbConnection,
              responseObject[0].Process_Instance_Id,
              trx
            )
          }
        }
      } else {
        // Handle workflow disabled scenario
        if (
          recruitmentId &&
          responseObject &&
          responseObject[0]?.Process_Instance_Id
        ) {
          await organizationDbConnection(ehrTables.mppRecruitmentRequest).transacting(trx).where('Recruitment_Id', recruitmentId).update({ Process_Instance_Id: null });
          await commonLib.func.deleteOldApprovalRecords(
            organizationDbConnection,
            responseObject[0].Process_Instance_Id,
            trx
          )
        }
      }

    }).then(async (result) => {
      if (!workflowEnabled && instanceData.customGroupId) {
        // Retrieve organization details
        const orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection, 1);
        const companyName = instanceData.companyName || orgDetails.orgName || '';
        const details = [
          instanceData.departmentName,
          instanceData.divisionName,
          companyName
        ].filter(Boolean).join(' / ') || 'N/A';

        const emailSubject = `Sourcing Request Raised – [${instanceData.positionTitle || 'N/A'}] | [${details}]`;

        const employeeDetails = await getEmployeeEmail(loginEmployeeId, organizationDbConnection);
        if (employeeDetails && employeeDetails.length > 0) {
          const { Emp_Email, Added_By_Name } = employeeDetails[0];
          instanceData.Added_By_Email = Emp_Email;
          instanceData.Added_By_Name = Added_By_Name;
        }
      
        // Retrieve email body and replyTo details
        const { replyTo, finalEmailBody } = await approvedAndForecastpositiondetails(
          instanceData,
          orgCode,
          organizationDbConnection,
          context,
          orgDetails
        );
      
        // Send the approved position email
        await sendApprovedPositionEmail(
          organizationDbConnection,
          finalEmailBody,
          emailSubject,
          replyTo,
          instanceData
        );
        console.log('Approved & Forecasted Position email sent');
      }
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Approved & Forecasted Positions request ${ positionCode
          +positionTitle} ${args.recruitmentId ? 'updated' : 'added'} successfully`,
      });
      if (organizationDbConnection) organizationDbConnection.destroy();
      return { errorCode: '', message: `Approved & Forecasted Positions request ${args.recruitmentId ? 'updated' : 'added'} successfully` };
    }).catch((error) => {
      throw error;
    });
  } catch (error) {
    if (error === 'IVE0000') {
      console.log('Validation error in addUpdateReqruitmentPosition function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      const errResult = commonLib.func.getError(error, 'EI00180');
      throw new ApolloError(errResult.message, errResult.code);
    }
  }
};

async function noOfPositionValidation(organizationDbConnection, args) {
  try{
  if (args.recruitmentId) {
    // Approved or rejected records cannot be updated
    const RejectedRecord = await organizationDbConnection(ehrTables.mppRecruitmentRequest + ' as MRR')
  .select('MRR.Original_Position_Id', 'MRR.Recruitment_Id')
  .where('MRR.Recruitment_Id', args.recruitmentId)
  .andWhere(function () {
    this.where('MRR.Status', 'Rejected');
  });

    if (RejectedRecord && RejectedRecord.length > 0) {
      throw 'EI00181';
    }
  }
  const countResult = await organizationDbConnection(ehrTables.mppRecruitmentRequest + ' as MRR')
  .sum({ totalRecords: 'MRR.No_Of_Position' })
  .where('MRR.Original_Position_Id', args.originalPositionId)
  .whereNot('MRR.Status', 'Rejected')
  .modify(function (queryBuilder) {
    if (args.recruitmentId) {
      queryBuilder.whereNot('MRR.Recruitment_Id', args.recruitmentId);
    }
  });

// Query to select Approved_Position and Warm_Bodies from SFWPOrganizationStructure
const selectResult = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as SFWP')
  .select(
    'SFWP.Approved_Position as totalApprovedPosition',
    'SFWP.Warm_Bodies as totalWarmBodies'
  )
  .where('SFWP.Originalpos_Id', args.originalPositionId);

// Extract results, ensuring safe defaults for missing records
const totalRecords = countResult[0] ? +countResult[0].totalRecords : 0;
const totalApprovedPosition = selectResult[0] ? +selectResult[0].totalApprovedPosition : 0;
const totalWarmBodies = selectResult[0] ? +selectResult[0].totalWarmBodies : 0;
  return +args.noOfPosition <= 500 && 
  args.noOfPosition <= 
  ((totalApprovedPosition || 0) - (totalWarmBodies || 0) - (totalRecords || 0));
}catch(err){
  console.log('Error in noOfPositionValidation', err);
  throw err;
}

}
async function getWorkflowProcessInstanceId(organizationDbConnection, uniqueId) {
  try {
    return await organizationDbConnection(ehrTables.mppRecruitmentRequest)
      .select('Process_Instance_Id')
      .where('Recruitment_Id', uniqueId);
  } catch (err) {
    console.log('Error in getWorkflowProcessInstanceId', err);
    throw err;
  }
}

async function initaiteWorkflow(eventId, instanceData, orgCode, formId, organizationDbConnection,loginEmployeeId,trx) {
  try {
    console.log('Inside initaiteWorkflow');
    instanceData.formId = formId
    instanceData.Employee_Id=loginEmployeeId
    instanceData.Added_By=loginEmployeeId
    instanceData.Added_On=moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const config = {
      method: 'post',
      url: `https://${process.env.customDomainName}/workflowEngine/workflow/initiate`,
      data: { event_id: eventId, instance_data: instanceData },
      headers: {
        'Content-Type': 'application/json',
        org_code: orgCode,
        employee_id: loginEmployeeId,
        db_prefix: process.env.dbPrefix,
      },
    };
    const response = await axios.request(config);
    console.log(response,"response")
    if (response.status === 200 && response.data?.workflowProcessInstanceId) {
      await updateWorkflowProcessInstanceId(organizationDbConnection, instanceData.recruitmentId, response.data.workflowProcessInstanceId,trx);
    } else {
      console.log('Error in the initaiteWorkflow endpoint.', response);
      throw 'ATS0009';
    }
  } catch (mainError) {
    console.log('Error in initaiteWorkflow', mainError);
    throw 'ATS0009';
  }
}

async function updateWorkflowProcessInstanceId(organizationDbConnection, uniqueId, workflowProcessInstanceId,trx) {
  try {
    return await organizationDbConnection(ehrTables.mppRecruitmentRequest)
      .where('Recruitment_Id', uniqueId)
      .transacting(trx)
      .update({ Process_Instance_Id: workflowProcessInstanceId });
  } catch (err) {
    console.log('Error in updateWorkflowProcessInstanceId', err);
    throw err;
  }
}
