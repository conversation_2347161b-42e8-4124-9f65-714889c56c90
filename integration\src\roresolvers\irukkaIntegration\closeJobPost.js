//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment');
const axios = require('axios');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const {formName} = require('../../../common/appConstants');

//function to update Integration settings
module.exports.closeJobPost = async (event, args, context, info) => {
    console.log('Inside closeJobPost function');
    let organizationDbConnection;
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.jobpost, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1 && checkRights.Is_Recruiter && checkRights.Is_Recruiter.toLowerCase() === 'yes') {
            const irukkaStatusData = await getIrukkaStatus(organizationDbConnection);
            const irukkaStatus = getStatusByType("irukka", irukkaStatusData);
            if(irukkaStatus && irukkaStatus == "Active"){
                if(context.irukkaidtoken){
                    await updateJobPostStatus(args, organizationDbConnection, context);
                    await closeJobpostForIrukka(context.irukkaidtoken);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Jobpost closed successfully." };
                }
                else{
                    // return the error and handle it in UI
                    console.log('Irukka idtoken is not available. Kindly do the sign-in again.');
                    throw 'SET0109';
                }
            }
           else{
            await updateJobPostStatus(args, organizationDbConnection, context);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Jobpost closed successfully." };
           }      
                   
            
        } else {
            console.log('No rights to close the jobpost');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in closeJobPost function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(mainCatchError, 'SET0008');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function getIrukkaStatus(organizationDbConnection){
    try{ 
            return (
                await organizationDbConnection(ehrTables.recruitmentIntegration)
                .select(
                    '*',
                  )
                .then((data) => {
                    return data ;
                })
                .catch((err) => {
                    console.log('Error in getIrukkaStatus .catch() block', err);
                    throw err;                   
                })
            )
        

    } catch(err){
        console.log('Error in getIrukkaStatus main catch() block', err);
        throw err;
    }

}

async function closeJobpostForIrukka(irukkaToken) {
    try {
        const url = process.env.irukkaCloseJobUrl
        const irukkaPartnerId = process.env.irukkaPartnerId;
        const headers = {
          "login-type": "Cognito-Authentication",
          "Partner-Id": irukkaPartnerId,
          "Content-Type": "application/json",
          Authorization: "Bearer "+ irukkaToken,
        };
       await axios.get(url, { headers });
      } catch (error) {
        console.log('Error in closeJobpostForIrukka main catch() block',error);
        throw error;
      }
  }


function updateJobPostStatus(args, organizationDbConnection, context){
    try {
        return (
            organizationDbConnection(ehrTables.jobPost)
                .where('Job_Post_Id', args.Job_Post_Id)
                .update({
                    Status: args.Status,
                    Closing_Date: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                })
                .then(async(data) => {
                 
                    if (data) {
                        await mailSendingCloseJob(organizationDbConnection, args, context)
                        return true;
                    } else {
                        throw 'SET0110'
                    }
                })
                .catch((catchError) => {
                    console.log('Error in closeJobPost .catch() block', catchError);
                    throw 'SET0111'
                })
        )
    } catch (error) {

        console.log('Error in closeJobPost main catch() block', error);
        throw 'SET0111'
    }
}

function getStatusByType(integrationType, irukkaStatusData) {
    // Find the object with the specified Integration_Type
    const integration = irukkaStatusData.find(
      (item) =>
        item.Integration_Type.toLowerCase() === integrationType.toLowerCase()
    );

    // If the integration is found, return its Integration_Status
    if (integration) {
      return integration.Integration_Status;
    } else {
      return null;
    }
}

async function mailSendingCloseJob(organizationDbConnection, args, context) {
    
    try {

        return (organizationDbConnection('job_post')
        .select('job_post.Job_Post_Id', 'job_post.Job_Post_Name', 'job_post.Added_By', 'emp_personal_info.Employee_Id', 'emp_job.Emp_Email', 'emp_personal_info.Emp_First_Name',
            organizationDbConnection.raw('CONCAT(emp_personal_info.Emp_First_Name, \' \', emp_personal_info.Emp_Last_Name) as "Employee_Name"'))
        .leftJoin('emp_personal_info', 'emp_personal_info.Employee_Id', 'job_post.Added_By')
        .leftJoin('emp_job', 'emp_personal_info.Employee_Id', 'emp_job.Employee_Id')
        .where('Job_Post_Id', args.Job_Post_Id)
        .then(async (result)=>{

            if(result && result.length > 0){
                                 
                var orgDetails = await getOrgDetails(context.Org_Code,organizationDbConnection);

                var organizationAddress =  [], companyName =""
                if(orgDetails && orgDetails.Field_Force && orgDetails.Field_Force ===1){
                    let serviceProvider = await organizationDbConnection('emp_job').select('service_provider.Service_Provider_Name', 'service_provider.Location_Id')
                    .leftJoin('service_provider', 'service_provider.Service_Provider_Id', 'emp_job.Service_Provider_Id')
                    .where('emp_job.Employee_Id', context.Employee_Id)
                    if(serviceProvider && serviceProvider.length > 0){
                        companyName = serviceProvider[0].Service_Provider_Name;
                        organizationAddress = await getOrgAddress(organizationDbConnection, serviceProvider[0].Location_Id);
                    }else{
                        organizationAddress = await getOrgAddress(organizationDbConnection, 0);
                    }
                }else{
                    companyName = orgDetails[0].Org_Name;
                    organizationAddress = await getOrgAddress(organizationDbConnection, null);
                }
                var street1 = organizationAddress.length > 0 ? organizationAddress[0].Street1 : '';
                var street2 = organizationAddress.length > 0 ? organizationAddress[0].Street2 : '';
                var pinCode = organizationAddress.length > 0 ? organizationAddress[0].Pincode : '';
                var cityName = organizationAddress.length > 0 ? organizationAddress[0].City_Name : '';
                var stateName = organizationAddress.length > 0 ? organizationAddress[0].State_Name : '';
                var countryName = organizationAddress.length > 0 ? organizationAddress[0].Country_Name : '';
                var mailContent = '';  
                
                const AWS = require('aws-sdk')

                var reportLogoS3Path ="";
                if(context && (context.partnerid=="entomo" || context.Partnerid=="entomo")){

                }else{

                     reportLogoS3Path =  orgDetails.Report_LogoPath ? process.env.domainName + '_upload/' + context.Org_Code+ '_tmp/logos/' + orgDetails.Report_LogoPath : ''; 
                    try {
                        const s3 = new AWS.S3({ region: process.env.region });
                        await s3.headObject({ Bucket: process.env.logoBucket, Key: reportLogoS3Path }).promise();
                        reportLogoS3Path = s3.getSignedUrl('getObject', { Bucket: process.env.logoBucket, Key: reportLogoS3Path, Expires: 60 * 60 });
                    }catch(err){
                        reportLogoS3Path = "";
                    }
                    reportLogoS3Path = reportLogoS3Path.split('?');
                }
                               
                var mailParams = () => {
                    return {
                        "Source": process.env.emailFrom,
                        "Template": "JobPostsTaskClosed",
                        "Destination": {
                            "ToAddresses": [result[0].Emp_Email] // for testing enviroment is has been hardcoded
                        },
                        "TemplateData": JSON.stringify({
                            orgLogo: reportLogoS3Path ? reportLogoS3Path[0] : "",
                            name: result[0].Emp_First_Name,
                            companyName: companyName,
                            jobPostTitle: result[0].Job_Post_Name,
                            jobCode: 'JOB'+ result[0].Job_Post_Id,
                            street1: street1,
                            street2: street2,
                            city: cityName,
                            pinCode: pinCode,
                            state: stateName,
                            country: countryName,
                            redirectUrl: "",
                            mailContent: mailContent,
                            hrappSupportEmail: result[0].Emp_Email ? result[0].Emp_Email : "",
                            initiatedBy: result[0].Employee_Name,
                            typeOfAction: "",
                            actionDoneBy:""
                        })
                    }
                }

                let params = mailParams();

                console.log("mailSendingCloseJob", process.env.sesRegion)
                // create object for aws SES
                const ses = new AWS.SES({ region: process.env.sesRegion });      
                //  call function sendTemplatedEmailto send email
                let response = await ses.sendTemplatedEmail(params).promise();
                
            }
        }));
    } catch(error){
        console.error('error while sending email notification', error);
    }
}

 // Get organization address
 const getOrgAddress =(organizationDbConnection, locationId) =>{
    return(
        organizationDbConnection('location')
        .select('location.Street1', 'location.Street2', 'location.Pincode', 'state.State_Name', 'country.Country_Name', 'city.City_Name')
        .from('location')
        .leftJoin('country', 'location.Country_Code','country.Country_Code')
        .leftJoin('state', 'location.State_Id','state.State_Id')
        .leftJoin('city', 'location.City_Id', 'city.City_Id')
        .where(qb => {                        
            if (locationId){
                qb.where('location.Location_Id', locationId);
            }else{
                qb.where('location.Location_Type','MainBranch');
            }
        })
        .then(orgDetails =>{                
            // return response
            return orgDetails;  
        })
    )
};

const getOrgDetails = (orgCode,organizationDbConnection) =>{
    return(
        organizationDbConnection('org_details')
        .select('Org_Name','Report_LogoPath','HR_Admin_Email_Address', 'Field_Force')
        .where('Org_Code',orgCode)
        .then(orgDetails =>{                
            // return response
            return orgDetails.length > 0 ? orgDetails[0] : {};
        })
    )
};
