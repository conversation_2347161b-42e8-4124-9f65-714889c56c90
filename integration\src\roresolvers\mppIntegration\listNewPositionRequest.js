// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../../../common/tablealias')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
const { formId,s3FileUpload } = require('../../../common/appConstants')
const { generateAndUploadReport } = require('../../../src/common/commonFunction');

module.exports.listNewPositionRequest = async (parent, args, context, info) => {
  console.log('Inside listNewPositionRequest function.')
  let organizationDbConnection

  try {
    let employeeId = context.Employee_Id
    organizationDbConnection = knex(context.connection.OrganizationDb)
    let formIds = args.formId || formId.newPosition
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      '',
      '',
      'UI',
      false,
      formIds
    )

    if (Object.entries(checkRights).length > 0 && checkRights.Role_View === 1) {

      // Handle backward compatibility for orgLevel in response
      let getOrgLevel = null;

      if (!args.alexport) {
        // When postionParentCode is '0', treat it as valid and set orgLevel to null
        if (args.postionParentCode === '0') {
          getOrgLevel = { Org_Level: null };
        } else if (!args.postionParentCode) {
          // Only fetch from employee designation when postionParentCode is not provided (null/undefined)
          let orgStructureResult = await organizationDbConnection(ehrTables.empJob + ' as EJ')
            .select(
              organizationDbConnection.raw(`CASE
                WHEN OS.Parent_Path IS NOT NULL AND OS.Parent_Path != '0'
                THEN SUBSTRING_INDEX(SUBSTRING_INDEX(OS.Parent_Path, ',', 2), ',', -1)
                ELSE OS.Originalpos_Id
              END AS firstParentPathId`)
            )
            .join(ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
            .join(ehrTables.SFWPOrganizationStructure + ' as OS', 'OS.Pos_Code', 'DES.Designation_Code')
            .where('EJ.Employee_Id', employeeId)
            .first();

          args.positionParentId = orgStructureResult?.firstParentPathId || '';
          let orgStructurePosition = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
            .select('OS.Pos_Code')
            .where('OS.Originalpos_Id', args.positionParentId)
            .first();
            args.postionParentCode = orgStructurePosition?.Pos_Code || '';
        } else {
          // When postionParentCode is provided (not null/undefined and not '0')
          let orgStructurePosition = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
            .select('OS.Originalpos_Id')
            .where('OS.Pos_Code', args.postionParentCode)
            .first();
          args.positionParentId = orgStructurePosition?.Originalpos_Id || '';
        }
        if(!args.postionParentCode || args.postionParentCode.length===0){
          organizationDbConnection ? organizationDbConnection.destroy() : null;
          return { errorCode: "", message: "New Position & Additional Headcount details retrieved successfully.", groupCode: args.postionParentCode, positionParentId:args.positionParentId, openPositiontRequestDetails: [], orgLevel:''};
        }
        // Get org level only if postionParentCode is not '0' and is provided
        if (args.postionParentCode && args.postionParentCode !== '0') {
          getOrgLevel = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
            .select('OS.Org_Level')
            .where('OS.Pos_Code', args.postionParentCode)
            .first();
        }
      }
      const openPositiontRequestDetails = await organizationDbConnection(
        ehrTables.mppPositionRequest + ' as MPR'
      ).distinct('MPR.Position_Request_Id')
        .select(
          // Main position request fields
          'MPR.Position_Request_Id', 'MPR.Original_Position_Id','MPR.Organization_Structure_Id','MPR.Request_Type', 'MPR.Position_Title',
          'MPR.Group_Code', 'MPR.Division_Code', 'MPR.Department_Code', 'MPR.Section_Code', 'MPR.Reason_For_Request',
          'MPR.No_Of_Position', 'MPR.Comments', 'MPR.Status', 'MPR.Cost_Center',
          'MPR.License_Certificate', 'MPR.License_Certificate_Details',
          'MPR.Employee_Type',
          organizationDbConnection.raw("CASE WHEN MPR.Internal_Operating_Network IS NOT NULL THEN REPLACE(REPLACE(REPLACE(MPR.Internal_Operating_Network, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as Internal_Operating_Network"),
          organizationDbConnection.raw("CASE WHEN MPR.External_Operating_Network IS NOT NULL THEN REPLACE(REPLACE(REPLACE(MPR.External_Operating_Network, '[\"', ''), '\"]', ''), '\", \"', ',') ELSE '' END as External_Operating_Network"),

          // Organization structure fields
          'SFWP.Pos_Code',
          'GRP.Pos_Name as Group_Name',
          'DIV.Pos_Name as Division_Name',
          'DEPT.Pos_Name as Department_Name',
          'SEC.Pos_Name as Section_Name',

          // Employee type and position level
          'ET.Employee_Type AS Employee_Type_Name',
          'PL.Position_Level',
          'PL.Position_Level_Id',

          // Audit fields
          'MPR.Added_On', 'EJ.User_Defined_EmpId as Added_By_Id',
          organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) AS Added_By"),
          'MPR.Updated_On', 'EJ2.User_Defined_EmpId as Updated_By_Id',
          organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) AS Updated_By"),
          'MPR.Approved_On', 'EJ3.User_Defined_EmpId as Approved_By_Id','MPR.Approver_Id',
          organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) AS Approved_By"),

          // Additional fields for backward compatibility
          'MPR.Reason_For_Replacement',
          'EJ.Emp_Email as Added_By_Email'
        )
        .leftJoin( ehrTables.positionLevel + ' as PL', 'MPR.Position_Level', 'PL.Position_Level_Id')
        .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id','MPR.Added_By')
        .leftJoin(ehrTables.empJob + ' as EJ', 'EPI.Employee_Id','EJ.Employee_Id')
        .leftJoin(
          ehrTables.empPersonalInfo + ' as EPI2',
          'EPI2.Employee_Id',
          'MPR.Updated_By'
        )
        .leftJoin(ehrTables.empJob + ' as EJ2', 'EPI2.Employee_Id','EJ2.Employee_Id')
        .leftJoin(
          ehrTables.empPersonalInfo + ' as EPI3',
          'EPI3.Employee_Id',
          'MPR.Approver_Id'
        )
        .leftJoin(ehrTables.empJob + ' as EJ3', 'EPI3.Employee_Id','EJ3.Employee_Id')
        .leftJoin(
          ehrTables.employeeType + ' as ET',
          'MPR.Employee_Type',
          'ET.EmpType_Id'
        )
        .leftJoin(
          'SFWP_Organization_Structure as SFWP',
          'MPR.Organization_Structure_Id',
          'SFWP.Organization_Structure_Id'
        )
        .leftJoin(
          'SFWP_Organization_Structure as GRP',
          'MPR.Group_Code',
          'GRP.Pos_Code'
        )
        .leftJoin(
          'SFWP_Organization_Structure as DIV',
          'MPR.Division_Code',
          'DIV.Pos_Code'
        )
        .leftJoin(
          'SFWP_Organization_Structure as DEPT',
          'MPR.Department_Code',
          'DEPT.Pos_Code'
        )
        .leftJoin(
          'SFWP_Organization_Structure as SEC',
          'MPR.Section_Code',
          'SEC.Pos_Code'
        )
        .modify((queryBuilder) => {
          // New organizational filtering logic - apply to both export and non-export
          const orgFilters = [];

          // Check for new organizational filters
          if (args.groupFilter && args.groupFilter.code) {
            if (args.groupFilter.code === '0') {
              orgFilters.push(['MPR.Group_Code', 'NULL_OR_EMPTY']);
            } else {
              orgFilters.push(['MPR.Group_Code', args.groupFilter.code]);
            }
          }
          if (args.divisionFilter && args.divisionFilter.code) {
            if (args.divisionFilter.code === '0') {
              orgFilters.push(['MPR.Division_Code', 'NULL_OR_EMPTY']);
            } else {
              orgFilters.push(['MPR.Division_Code', args.divisionFilter.code]);
            }
          }
          if (args.departmentFilter && args.departmentFilter.code) {
            if (args.departmentFilter.code === '0') {
              orgFilters.push(['MPR.Department_Code', 'NULL_OR_EMPTY']);
            } else {
              orgFilters.push(['MPR.Department_Code', args.departmentFilter.code]);
            }
          }
          if (args.sectionFilter && args.sectionFilter.code) {
            if (args.sectionFilter.code === '0') {
              orgFilters.push(['MPR.Section_Code', 'NULL_OR_EMPTY']);
            } else {
              orgFilters.push(['MPR.Section_Code', args.sectionFilter.code]);
            }
          }

          // Apply organizational filters
          if (orgFilters.length > 0 && !args.alexport) {
            orgFilters.forEach(([field, value]) => {
              if (value === 'NULL_OR_EMPTY') {
                queryBuilder.andWhere(function() {
                  this.whereNull(field).orWhere(field, '');
                });
              } else {
                queryBuilder.andWhere(field, value);
              }
            });
          }

          if(args.alexport){
            queryBuilder.orderBy('MPR.Position_Request_Id', 'ASC');
          }

          // Apply form-specific filtering
          if (args.formId && args.formId === 15) {
            queryBuilder.andWhere(function () {
              this.whereIn('MPR.Status', ['Approved','TO Changes Approved','To In Review'])
            })
          }
        })

      // Handle alexport functionality
      if (args.alexport) {
        // Fetch additional data for export
        const [
          dutiesResponsibilities,
          workingConditions,
          experienceRequirements,
          educationRequirements
        ] = await Promise.all([
          getMppDutiesResponsibilities(organizationDbConnection),
          getMppWorkingConditions(organizationDbConnection),
          getMppExperienceRequirements(organizationDbConnection),
          getMppEducationRequirementsDescriptions(organizationDbConnection)
        ]);
        // Transform data for export with additional sheets
        const formattedData = transformAlexportDataNewPositionWithDetails(
          openPositiontRequestDetails,
          dutiesResponsibilities,
          workingConditions,
          experienceRequirements,
          educationRequirements
        );
        // Get organization code
        const orgCode = context.Org_Code || 'default';

        // Generate and upload file to S3
        let uploadResult;
        try {
          uploadResult = await generateAndUploadReport({
              orgCode: orgCode,
              reportData: formattedData,
              reportType: 'NEW_POSITION',
              fileName: null
          });
        } catch (uploadError) {
          console.error('Error generating/uploading report:', uploadError);
          uploadResult = { success: false, error: uploadError.message };
        }

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return {
          errorCode: '',
          message: 'New Position & Additional Headcount export data retrieved successfully.',
          groupCode: null,
          positionParentId: null,
          openPositiontRequestDetails: null,
          orgLevel: '',
          s3Url: uploadResult.success ? uploadResult.s3Url : null,
          s3Path: uploadResult.success ? uploadResult.s3Path : null,
        };
      }

      //Destroy DB connection
      organizationDbConnection ? organizationDbConnection.destroy() : null
      return {
        errorCode: '',
        message: 'New Position & Additional Headcount details retrieved successfully.',
        groupCode: args.postionParentCode,
        positionParentId:args.positionParentId,
        openPositiontRequestDetails: openPositiontRequestDetails,
        orgLevel:getOrgLevel?.Org_Level
      }
    } else {
      throw '_DB0100'
    }
  } catch (err) {
    //Destroy DB connection
    console.error(
      'Error in listNewPositionRequest function main catch block.',
      err
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    let errResult = commonLib.func.getError(err, 'EI00183')
    throw new ApolloError(errResult.message, errResult.code)
  }
}

// Helper functions to fetch additional data for export
async function getMppDutiesResponsibilities(organizationDbConnection){
    try {
        return organizationDbConnection( ehrTables.mppDutiesResponsibilities)
        .select('Position_Request_Id', 'Regular_Duties', 'No_Of_Hours_Period as No_Of_Hours_Performed_per_Period',
            'Period', 'Competencies_Required', 'Competency', 'Rating_Of_Competency')
            .orderBy('Position_Request_Id', 'asc');
    }catch(err){
        console.error('Error in getMppDutiesResponsibilities function main catch block', err)
        throw err;
    }
}

async function getMppWorkingConditions(organizationDbConnection){
    try {
        return organizationDbConnection( ehrTables.mppWorkingConditions)
        .select('Position_Request_Id', 'Working_Area', 'Time_Spent as Percent_Of_Time_Spent')
        .orderBy('Position_Request_Id', 'asc');
    }catch(err){
        console.error('Error in getMppWorkingConditions function main catch block', err)
        throw err;
    }
}

async function getMppExperienceRequirements(organizationDbConnection){
    try {
        return organizationDbConnection( ehrTables.mppExperience)
        .select('Position_Request_Id', 'Type_Of_Jobs', 'Months', 'Years')
        .orderBy('Position_Request_Id', 'asc');
    }catch(err){
        console.error('Error in getMppExperienceRequirements function main catch block', err)
        throw err;
    }
}

async function getMppEducationRequirementsDescriptions(organizationDbConnection){
    try {
        return organizationDbConnection( ehrTables.mppEducationRequirementsDescriptions + ' as MERD')
        .select('MERD.Position_Request_Id', 'MER.Education_Type', 'MERD.Description')
        .innerJoin(ehrTables.mppEducationRequirements + ' as MER', 'MER.Mpp_Education_Requirements_Id', 'MERD.Mpp_Education_Requirements_Id')
        .orderBy('Position_Request_Id', 'asc');
    }catch(err){
        console.error('Error in getMppEducationRequirementsDescriptions function main catch block', err)
        throw err;
    }
}

async function getPositionLevel(organizationDbConnection){
    try {
        return organizationDbConnection( ehrTables.positionLevel)
        .select('Position_Level_Id', 'Position_Level')
    }catch(err){
        console.error('Error in getPositionLevel function main catch block', err)
        throw err;
    }
}

function transformAlexportDataNewPositionWithDetails(
  mainData,
  dutiesResponsibilities,
  workingConditions,
  experienceRequirements,
  educationRequirements
) {
  // Transform main position request data
  let checkrow =mainData.filter(row => row.Position_Request_Id === 375);
  console.log('checkrow',checkrow);
  const mainSheet = mainData.map(row => ({
    'Position Request Id': row.Position_Request_Id || '',
    'Original Position Id': row.Original_Position_Id || '',
    'Request Type': row.Request_Type || '',
    'Position Title': row.Position_Title || '',
    'Group Code': row.Group_Code || '',
    'Group Name': row.Group_Name || '',
    'Division Code': row.Division_Code || '',
    'Division Name': row.Division_Name || '',
    'Department Code': row.Department_Code || '',
    'Department Name': row.Department_Name || '',
    'Section Code': row.Section_Code || '',
    'Section Name': row.Section_Name || '',
    'Reason For Request': row.Reason_For_Replacement || '',
    'Employee Type': row.Employee_Type_Name || '',
    'No Of Position': row.No_Of_Position || 0,
    'Additional Comments': row.Comments || '',
    'Status': row.Status || '',
    'Cost Center': row.Cost_Center || '',
    'License Certificate': row.License_Certificate || '',
    'License Certificate Details': row.License_Certificate_Details || '',
    'Position Level': row.Position_Level || '',
    'Internal Operating Network': row.Internal_Operating_Network || '',
    'External Operating Network': row.External_Operating_Network || '',
    'Added On': row.Added_On || '',
    'Added By': row.Added_By || '',
    'Updated On': row.Updated_On || '',
    'Updated By Id': row.Updated_By_Id || '',
    'Updated By': row.Updated_By || '',
    'Approved On': row.Approved_On || '',
    'Approved By': row.Approved_By || ''
  }));

  // Transform duties and responsibilities data
  const dutiesSheet = dutiesResponsibilities.map(row => ({
    'Position Request Id': row.Position_Request_Id || '',
    'Regular Duties': row.Regular_Duties || '',
    'No Of Hours Performed per Period': row.No_Of_Hours_Performed_per_Period || '',
    'Period': row.Period || '',
    'Competencies Required': row.Competencies_Required || '',
    'Competency': row.Competency || '',
    'Rating Of Competency': row.Rating_Of_Competency || ''
  }));

  // Transform working conditions data
  const workingConditionsSheet = workingConditions.map(row => ({
    'Position Request Id': row.Position_Request_Id || '',
    'Working Area': row.Working_Area || '',
    'Percent Of Time Spent': row.Percent_Of_Time_Spent || ''
  }));

  // Transform experience requirements data
  const experienceSheet = experienceRequirements.map(row => ({
    'Position Request Id': row.Position_Request_Id || '',
    'Type Of Jobs': row.Type_Of_Jobs || '',
    'Years': row.Years || '',
    'Months': row.Months || ''
  }));

  // Transform education requirements data
  const educationSheet = educationRequirements.map(row => ({
    'Position Request Id': row.Position_Request_Id || '',
    'Education Type': row.Education_Type || '',
    'Description': row.Description || ''
  }));

  // Return array of sheet objects for multi-sheet Excel
  return [
    { 'New Position Requests': mainSheet },
    { 'Duties Responsibilities': dutiesSheet },
    { 'Working Conditions': workingConditionsSheet },
    { 'Experience Requirements': experienceSheet },
    { 'Education Requirements': educationSheet }
  ];
}

function transformAlexportDataNewPosition(rawData) {
  return rawData.map(row => ({
    'Position Request Id': row.Position_Request_Id || '',
    'Original Position Id': row.Original_Position_Id || '',
    'Request Type': row.Request_Type || '',
    'Position Title': row.Position_Title || '',
    'Position Code': row.Pos_Code || '',
    'Group Code': row.Group_Code || '',
    'Group Name': row.Group_Name || '',
    'Division Code': row.Division_Code || '',
    'Division Name': row.Division_Name || '',
    'Department Code': row.Department_Code || '',
    'Department Name': row.Department_Name || '',
    'Section Code': row.Section_Code || '',
    'Section Name': row.Section_Name || '',
    'Reason For Request': row.Reason_For_Request || '',
    'Employee Type': row.Employee_Type_Name || '',
    'No Of Position': row.No_Of_Position || 0,
    'Comments': row.Comments || '',
    'Status': row.Status || '',
    'Cost Center': row.Cost_Center || '',
    'License Certificate': row.License_Certificate || '',
    'License Certificate Details': row.License_Certificate_Details || '',
    'Position Level': row.Position_Level || '',
    'Internal Operating Network': row.Internal_Operating_Network || '',
    'External Operating Network': row.External_Operating_Network || '',
    'Added On': row.Added_On || '',
    'Added By': row.Added_By || '',
    'Updated On': row.Updated_On || '',
    'Updated By Id': row.Updated_By_Id || '',
    'Updated By': row.Updated_By || '',
    'Approved On': row.Approved_On || '',
    'Approved By': row.Approved_By || ''
  }));
}


