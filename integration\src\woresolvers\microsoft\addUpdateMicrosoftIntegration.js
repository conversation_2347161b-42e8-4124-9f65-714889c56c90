
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants')
const moment = require('moment');

module.exports.addUpdateMicrosoftIntegration = async (parent, args, context, info) => {
    console.log("Inside addUpdateMicrosoftIntegration function.");
    // variable to hold the database connection
    let organizationDbConnection;
    // variable to hold the validation errors
    let validationError = {};
    try {
        // get the database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the employee id of the logged in user
        let employeeId = context.Employee_Id;

        // check the access rights of the logged in user
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, employeeId, '', '', 'UI', false, formId.myIntegrations
        );

        // if the user does not have the required access rights, throw an error
        if (!checkRights || 
            (checkRights.Role_Add === 0 && args.action.toLowerCase() === "add") || 
            (checkRights.Role_Update === 0 && args.action.toLowerCase() === "update")) {
            throw !checkRights ? '_DB0104' : (checkRights.Role_Add === 0 && args.action.toLowerCase() === "add")? '_DB0101' : '_DB0102';
        }

        // validate the input
        if (args.action.toLowerCase() === "add" && !args.microsoftEmail) {
            validationError['IVE0000'] = 'Microsoft email is required';
        }
        if((!args.calendarStatus && !args.teamsStatus)){
            validationError['IVE0001'] = 'Calendar status or Teams status is required';
        }

        if (args.calendarStatus && !["active", "inactive"].includes(args.calendarStatus.toLowerCase())) {
            validationError['IVE0002'] = 'Calendar status should be Active or Inactive';
        }

        if (args.teamsStatus && !["active", "inactive"].includes(args.teamsStatus.toLowerCase())) {
            validationError['IVE0003'] = 'Teams status should be Active or Inactive';
        }

        if(validationError && Object.keys(validationError).length > 0){
            throw 'IVE0000';
        }

        // check if the microsoft integration for the logged in user already exists
        const microsoftIntegration = await organizationDbConnection(ehrTables.employeeLevelMicrosoftIntegration)
            .where('Employee_Id', employeeId).first();
            
        // variable to hold the query, message and error code
        let query, message, errorCode;
        // current date
        const currentDate = moment().utc().format('YYYY-MM-DD');

        // if the microsoft integration already exists
        if (microsoftIntegration) {
          
            let updateData = {
                Updated_On: currentDate
            }
            if(args.calendarStatus){
                updateData.Calendar_Status = args.calendarStatus;
            }
            if(args.teamsStatus){
                updateData.Teams_Status = args.teamsStatus;
            }
            if(args.microsoftEmail){
                updateData.Microsoft_Email = args.microsoftEmail;
            }
           // update the microsoft integration
            query = organizationDbConnection(ehrTables.employeeLevelMicrosoftIntegration)
                .update(updateData)
                .where('Employee_Id', employeeId);
            // message and error code
            message = "Microsoft integration updated successfully.";
            errorCode = "EI00220"; //An error occurred while updating the microsoft integration. Please try again later.
           
        } else {
            // if the microsoft integration does not exist, insert a new record
            query = organizationDbConnection(ehrTables.employeeLevelMicrosoftIntegration)
                .insert({
                    Microsoft_Email: args.microsoftEmail,
                    Employee_Id: employeeId,
                    Added_On: currentDate,
                    Calendar_Status: args.calendarStatus ? args.calendarStatus : 'Inactive',
                    Teams_Status: args.teamsStatus ? args.teamsStatus : 'Inactive',
                    
                });
            // message and error code
            message = "Microsoft integration added successfully.";
            errorCode = "EI00221"; //An error occurred while adding the microsoft integration. Please try again later.
        }

        // execute the query
        let result = await query;
        // if the query is not successful, throw an error
        if(!result) 
            throw errorCode;

        // log the activity in the system log
        await commonLib.func.createSystemLogActivities({
            userIp: context.User_Ip, employeeId: employeeId,
            organizationDbConnection: organizationDbConnection,
            message: `Employee Id ${employeeId} ${message}`,
        });

        // return the result
        return { errorCode: "", message: message };

    } catch (err) {
        console.error('Error in addUpdateMicrosoftIntegration function main catch block.', err);
        // if the error is IVE0000, throw a UserInputError
        const errorCode = err === 'IVE0000' ? 'IVE0000' : err.code === 'ER_DUP_ENTRY' ? 'EI00223' : err;
        const errResult = commonLib.func.getError(errorCode, '_UH0001');
        if(err === 'IVE0000')
            throw new UserInputError(errResult.message, { validationError: validationError}); 
        // else throw an ApolloError
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // close the database connection
        if (organizationDbConnection) organizationDbConnection.destroy();
    }
}
