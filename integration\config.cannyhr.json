{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "dbSecretName": "PROD/CANNY/PGACCESS", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::************:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "domainName": "cannyhr", "authorizerARN": "arn:aws:lambda:ap-south-1:************:function:ATS-cannyhr-firebaseauthorizer", "customDomainName": "api.cannyhr.com", "emailFrom": "<EMAIL>", "emailTo": "<EMAIL>", "firebaseApiKey": "AIzaSyAz3TaOCY3KlDFyrNZ_feZe9dENgG1rU7g", "logoBucket": "s3.logos.cannyhr.com", "documentsBucket": "s3.taxdocs.cannyhr.com", "offlineReportBucket": "offlinereports.cannyhr.com", "sesTemplatesRegion": "us-west-2", "webAddress": ".com", "sesRegion": "us-east-1", "camuCreateStaffEndPoint": "external/staff", "camuExitStaffEndPoint": "external/staff/exit", "dailyCamuResignationArn": "arn:aws:states:ap-south-1:************:stateMachine:cannyhr-integrationCamuResignation", "irukkaCloseJobUrl": "https://aju0i48d0f.execute-api.ap-south-1.amazonaws.com/uat/partner/api/v1/partnerIntegration/closeJob/a4ae0c94-0051-4f8f-9122-1e7cbb4bf03e", "irukkaPartnerId": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:************:function:INTEGRATION-cannyhr", "signInAPI": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=", "indeedAuthTokenAPI": "https://apis.indeed.com/oauth/v2/tokens", "indeedPublishJobAPI": "https://apis.indeed.com/graphql", "sunFishesAPI": "https://sf7dev-api.dataon.com/sfapi/", "jobStreetAuthTokenAPI": "https://auth.seek.com/oauth/token", "jobStreetAuthBrowserTokenAPI": "https://graphql.seek.com/auth/token", "jobStreetTokenAPI": "https://graphql.seek.com/graphql", "pagtAPIURL": "https://testentomoapi.punongbayan-araullo.com/api", "asyncSunFishAPIPushFunction": "arn:aws:states:ap-south-1:************:stateMachine:cannyhr-asyncSunFishAPIPushFunction", "asyncPAGTAPIPushFunction": "arn:aws:states:ap-south-1:************:stateMachine:cannyhr-asyncPAGTAPIPushFunction", "asyncJobStreetWebHookFunction": "arn:aws:states:ap-south-1:************:stateMachine:cannyhr-asyncJobStreetWebHookFunction", "asyncJobStreetCloseWebHookFunction": "arn:aws:states:ap-south-1:************:stateMachine:cannyhr-asyncJobStreetCloseWebHookFunction", "recruitBucketName": "recruit.cannyhr.com", "atsNameForIndeed": "Cannyhr"}