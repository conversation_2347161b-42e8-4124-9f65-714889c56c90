const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const moment = require('moment-timezone');
const { validatCommonRuleInput } = require('../../common/commonFunction');

module.exports.addUpdateHiringForecast = async (parent, args, context, info) => {
  console.log("Inside addUpdateHiringForecast function.");
  let validationError = {};
  let organizationDbConnection;
  try {
    let employeeId = context.Employee_Id;
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check employee access rights (outside transaction)
    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      '',
      '',
      'UI',
      false,
      formId.hiringForeCast
    );

    if (Object.entries(checkRights).length > 0 && 
        ((checkRights.Role_Add === 1 && args.action.toLowerCase() === 'add') || 
         (checkRights.Role_Update === 1 && args.action.toLowerCase() === 'update'))) {
      
      // Validate input fields
      const fieldValidations = {};
      if (args.positionTitle) fieldValidations.positionTitle = 'IVE0497';
      
      validationError = await validatCommonRuleInput(args, fieldValidations);
      if (Object.keys(validationError).length > 0) throw 'IVE0000';

      // Start transaction for modifications only
      return await organizationDbConnection.transaction(async (trx) => {
        let parentPath = '0';
        if (!args.originalPositionId && args.action.toLowerCase() === 'add' && !args.organizationStructureId) {
          // Check for duplicate Pos_Name (outside transaction)
          const existingPos = await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
            .where('Pos_Name', args.positionTitle)
            .first();
          if (existingPos) throw 'EI00208';
          
          // 0.group,div,dep,sec
          if (args.groupId) parentPath += `,${args.groupId}`;
          if (args.divisionId) parentPath += `,${args.divisionId}`;
          if (args.departmentId) parentPath += `,${args.departmentId}`;
          if (args.sectionId) parentPath += `,${args.sectionId}`;
          const parentId = args.sectionId || args.departmentId || args.divisionId || args.groupId || 0;
          let insertData = await trx(ehrTables.SFWPOrganizationStructure).insert({
            Pos_Name: args.positionTitle,
            Parent_Path: parentPath,
            Parent_Id: parentId,
            Status: 'Draft'
          });
          args.organizationStructureId = insertData[0];

          let jobDescriptionData={
            Organization_Structure_Id: insertData[0]
          }
          await   await organizationDbConnection(ehrTables.sfwpJobDescriptionMaster)
          .transacting(trx).insert(jobDescriptionData);
        }

        if (args.action.toLowerCase() === 'add') {
          // Fetch parent structure IDs (outside transaction)
          let parentStructureIds={}
          if (args.originalPositionId) {
            parentStructureIds = await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
              .select('Parent_Path')
              .where('Originalpos_Id', args.originalPositionId)
              .first();
          } else {
             parentStructureIds.Parent_Path=parentPath;
            
          }
          parentStructureIds = parentStructureIds.Parent_Path ? parentStructureIds.Parent_Path.split(",") : [];

          const parentStructureDetails = await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
            .select('Originalpos_Id', 'Pos_Code', 'Org_Level')
            .whereIn('Originalpos_Id', parentStructureIds);

          let originalGroupId, originalDivisionId, originalSectionId, originalDepartmentId;
          if (parentStructureDetails && parentStructureDetails.length) {
            originalGroupId = parentStructureDetails.find(grp => grp.Org_Level === 'GRP')?.Originalpos_Id || '';
            originalDivisionId = parentStructureDetails.find(div => div.Org_Level === 'DIV')?.Originalpos_Id || '';
            originalSectionId = parentStructureDetails.find(sec => sec.Org_Level === 'SEC')?.Originalpos_Id || '';
            originalDepartmentId = parentStructureDetails.find(dept => dept.Org_Level === 'DEPT')?.Originalpos_Id || '';
          }

          // Prepare forecast list data
          let foreCastList = args.foreCastList.map(field => ({
            Original_Position_Id: args.originalPositionId,
            Organization_Structure_Id: args.organizationStructureId,
            Position_Title: args.positionTitle,
            Group_Id: originalGroupId || null,
            Division_Id: originalDivisionId || null,
            Section_Id: originalSectionId || null,
            Department_Id: originalDepartmentId || null,
            Forecast_Year: field.year,
            Forecast_Month: field.month,
            No_Of_Position: field.noOfPosition,
            Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Added_By: employeeId
          }));

          // Insert forecast list in transaction
          await trx(ehrTables.mppHiringForecast).insert(foreCastList);
          return { errorCode: "", message: "Hiring forecast added successfully." };

        } else if (args.action.toLowerCase() === 'update') {
          // Update forecast list in transaction
          await Promise.all(
            args.foreCastList.map(field =>
              trx(ehrTables.mppHiringForecast)
                .update({
                  Forecast_Year: field.year,
                  Forecast_Month: field.month,
                  No_Of_Position: field.noOfPosition,
                  Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                  Updated_By: employeeId
                })
                .where('Hiring_Forecast_Id', field.hiringForecastId)
                .andWhere('Original_Position_Id', args.originalPositionId)
            )
          );
          return { errorCode: "", message: "Hiring forecast updated successfully." };

        } else {
          throw 'IVE0097';
        }
      });
    } else {
      throw '_DB0111';
    }
  } catch (err) {
    console.error('Error in addUpdateHiringForecast function main catch block.', err);
    organizationDbConnection ? organizationDbConnection.destroy() : null;
    err = err.code === 'ER_DUP_ENTRY' ? 'EI00172' : err;
    let errResult = commonLib.func.getError(err, 'EI00173');
    throw new ApolloError(errResult.message, errResult.code);
  }
};