//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
let moment = require('moment');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const {formName} = require('../../../common/appConstants');

//function to update Integration settings
module.exports.addUpdateRecruitmentStatus = async (parent, args, context, info) => {
    console.log('Inside addUpdateRecruitmentStatus function');
    let organizationDbConnection;
    var validationError = {};
    try {
        let loginEmployeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, formName.recruitment, '', 'UI');
        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Add === 1 ||  checkRights.Role_Update === 1)) {

            if(args.Integration_Type ==='linkedin' && args.Integration_Status === 'Active' && !args.Company_Id){
                validationError['IVE0000'] = 'Linkedin integration companyId is required';
                throw 'IVE0000';
            }else if(args.Integration_Type ==='indeed' && args.Integration_Status === 'Active' && !args.Indeed_Source_Name){
                validationError['IVE0000'] = 'Indeed source name is required';
                throw 'IVE0000';
            }

            if (args.Integration_Id) {
                return (
                    organizationDbConnection(ehrTables.recruitmentIntegration)
                        .update({
                            Integration_Status: args.Integration_Status,
                            Company_Id: args.Company_Id ? args.Company_Id : null,
                            Hirer_ID: args.Hirer_ID ? args.Hirer_ID : null,
                            Indeed_Source_Name: args.Indeed_Source_Name ? args.Indeed_Source_Name: null,
                            Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                            Updated_By: loginEmployeeId,
                            
                        }).where('Integration_Id', args.Integration_Id)
                        .then((data) => {
                            if (data) {
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                return { errorCode: "", message: `${args.Integration_Type} status has been updated successfully.` };
                            } else {
                                throw 'SET0102'
                            }
                        })
                        .catch((catchError) => {
                            console.log('Error in addUpdateRecruitmentStatus .catch() block', catchError);
                            let errorCode = catchError === 'SET0102' ? catchError : '';
                            let errResult = commonLib.func.getError(errorCode, 'SET0103');
                            //Destroy DB connection
                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                            //Return error response
                            throw new ApolloError(errResult.message, errResult.code);
                        })
                )
            } else {
                return (
                    organizationDbConnection(ehrTables.recruitmentIntegration)
                    .where('Integration_Type', args.Integration_Type)
                    .then(async(result) => {

                        if(result && result.length > 0){
                            return (
                                organizationDbConnection(ehrTables.recruitmentIntegration)
                                    .update({
                                        Integration_Status: args.Integration_Status,
                                        Company_Id: args.Company_Id ? args.Company_Id : null,
                                        Hirer_ID: args.Hirer_ID ? args.Hirer_ID : null,
                                        Indeed_Source_Name: args.Indeed_Source_Name ? args.Indeed_Source_Name: null,
                                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                        Updated_By: loginEmployeeId,
                                        
                                    }).where('Integration_Id', result[0].Integration_Id)
                                    .then((data) => {
                                        if (data) {
                                            organizationDbConnection ? organizationDbConnection.destroy() : null;
                                            return { errorCode: "", message: args.Integration_Type + " status has been updated successfully."  };
                                        } else {
                                            throw 'SET0102'
                                        }
                                    })
                                    .catch((catchError) => {
                                        console.log('Error in addUpdateRecruitmentStatus .catch() block', catchError);
                                        let errorCode = catchError === 'SET0102' ? catchError : '';
                                        let errResult = commonLib.func.getError(errorCode, 'SET0103');
                                        //Destroy DB connection
                                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                                        //Return error response
                                        throw new ApolloError(errResult.message, errResult.code);
                                    })
                            )

                        }else{
                            return organizationDbConnection(ehrTables.recruitmentIntegration)
                            .insert({
                                Integration_Type: args.Integration_Type,
                                Integration_Status: 'Active',
                                Company_Id: args.Company_Id ? args.Company_Id : null,
                                Hirer_ID: args.Hirer_ID ? args.Hirer_ID : null,
                                Indeed_Source_Name: args.Indeed_Source_Name ? args.Indeed_Source_Name: null,
                                Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                                Added_By: loginEmployeeId,
                            })
                            .then(async(data) => {
                                if (data) {
                                    let systemLogParam = {
                                        userIp: context.User_Ip,
                                        employeeId: loginEmployeeId,
                                        organizationDbConnection: organizationDbConnection,
                                        message: `${args.Integration_Type} status has been added successfully.`,
                                    };
                                    await commonLib.func.createSystemLogActivities(systemLogParam);
                                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                                    return { errorCode: "", message: `${args.Integration_Type} status has been added successfully.` };
                                } else {
                                    throw 'SET0102'
                                }
                            })
                            .catch((catchError) => {
                                console.log('Error in addUpdateRecruitmentStatus .catch() block', catchError);
                                let errorCode = catchError === 'SET0102' ? catchError : '';
                                let errResult = commonLib.func.getError(errorCode, 'SET0103');
                                //Destroy DB connection
                                organizationDbConnection ? organizationDbConnection.destroy() : null;
                                //Return error response
                                throw new ApolloError(errResult.message, errResult.code);
                            })
                        }
                    })
                )
            }
                   
            
        } else {
            console.log('No rights to update the recruitment integration Status');
            throw '_DB0111';
        }
    } catch (mainCatchError) {
        console.log('Error in addUpdateRecruitmentStatus function main block', mainCatchError);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (mainCatchError === 'IVE0000') {
            let errResult = commonLib.func.getError('', 'IVE0000');
            console.log('Validation error in addCandidatesInterviewRoundFeedBack function - ', validationError);
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            let errResult = commonLib.func.getError(mainCatchError, 'SET0002');
            // return response
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}
