// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const {formId} = require('../../../common/appConstants');
let moment = require('moment');
const axios = require('axios');


module.exports.publishJobPostToIndeed = async (parent, args, context, info) => {
    console.log('Inside publishJobPostToIndeed function');
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false,  formId.jobpost);
        if(Object.keys(checkRightsForForm).length > 0  && ((checkRightsForForm.Role_Add === 1) || (checkRightsForForm.Role_Update === 1))){
            const indeedUrl = process.env.indeedPublishJobAPI;
            const companyNameObject = await getCompanyName(organizationDbConnection, args.jobPostId);
            const companyname = companyNameObject && companyNameObject.length && companyNameObject[0].companyName ? companyNameObject[0].companyName: '';
            const countryCodeObject = await getCountryCode(organizationDbConnection, args.jobPostId);
            const countryCode = countryCodeObject && countryCodeObject.length && countryCodeObject[0].countryCode ? countryCodeObject[0].countryCode : 'IN';
            let closingDate = countryCodeObject && countryCodeObject.length && countryCodeObject[0].Closing_Date ? countryCodeObject[0].Closing_Date : null;
            const formattedClosingDate = closingDate ? moment(closingDate).utcOffset(0).format('YYYY-MM-DDTHH:mm:ss[Z]'): null;
            args.input.jobPostings[0].metadata.expirationDate = formattedClosingDate;
            args.input.jobPostings[0].metadata.numberOfHires = countryCodeObject && countryCodeObject.length && countryCodeObject[0].No_Of_Vacancies ? countryCodeObject[0].No_Of_Vacancies : null;
            args.input.jobPostings[0].body.location.country = countryCode;
            args.input.jobPostings[0].metadata.jobRequisitionId = context.Org_Code + "-" + args.jobPostId;
            args.input.jobPostings[0].metadata.jobSource.companyName = companyname;
            query = `mutation CreateSourcedJobPostings($input: CreateSourcedJobPostingsInput) {
                    jobsIngest {
                      createSourcedJobPostings(input: $input) {
                        results {
                          jobPosting {
                            sourcedPostingId
                          }
                        }
                      }
                    }
                  }`,
            variables = args.input;

            let config = {
            method: 'post',
            maxBodyLength: Infinity,
            url: indeedUrl,
            headers: { 
                'Content-Type': 'application/json', 
                'Authorization': context.indeed_access_token, 
            },
            data: JSON.stringify({
                query: query,
                variables: {
                input: args.input
                }
              })
            };

            try {
                // Make the request using Axios
                console.log("Jobpost data for indeed:", config.data);
                let response = await Promise.resolve(axios.request(config))
                const responseMessage = JSON.stringify(response.data);
                if(response && response.data && !response.data.errors){
                  let sourcedPostingId = response.data.data.jobsIngest.createSourcedJobPostings.results[0].jobPosting.sourcedPostingId;
                  await addUpdateIndeedDetails(organizationDbConnection, args, loginEmployeeId, context, sourcedPostingId);
                }
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                return { errorCode: "", results: responseMessage };
              } catch (error) {
                //Destroy DB connection
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                if (error.response) {
                  // The request was made and the server responded with a status code
                  console.error('Response Error:', error.response.data);
                  console.error('Status Code:', error.response.status);
                  const responseMessage = JSON.stringify(error.response.data);
                  return { errorCode: "", results: responseMessage };
                } else if (error.request) {
                  // The request was made but no response was received
                  console.error('Request Error:', error.request);
                } else {
                  // Something else happened in setting up the request
                  console.error('Other Error:', error.message);
                }
                throw 'SET0013';
              }
        }
        else {
            console.log('This employee do not have add or edit access rights');
            throw '_DB0111';
        }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in publishJobPostToIndeed function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0013');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function getCompanyName(organizationDbConnection, jobPostId){
  try{ 
      return (
          await organizationDbConnection(ehrTables.jobPost)
          .select('*',)
          .where('Job_Post_Id', jobPostId)
          .then(async(data) => {
            if(data && data.length && data[0].Service_Provider_Id){
              let serviceProviderId = data[0].Service_Provider_Id;
              return(
                await organizationDbConnection(ehrTables.serviceProvider)
                .select('Service_Provider_Name as companyName',)
                .where('Service_Provider_Id', serviceProviderId)
                .then((data) =>{
                  return data;
                })
                .catch((err) => {
                    console.log('Error while fetching the service provider name', err);
                    throw err;
                })
              )
            }
            else{
              return(
                await organizationDbConnection(ehrTables.orgDetails)
                .select('Org_Name as companyName')
                .then((data) =>{
                  return data;
                })
                .catch((err) => {
                    console.log('Error while fetching the org name', err);
                    throw err
                })
              )
            }
          })
          .catch((err) => {
              console.log('Error in getCompanyName .catch() block', err);
              throw err;                   
          })
      )
  } catch(err){
      console.log('Error in getCompanyName main catch() block', err);
      throw err;
  }
}

async function getCountryCode(organizationDbConnection, jobPostId){
  try{ 
      return (
          await organizationDbConnection(ehrTables.jobPost + ' as jp')
          .select('jp.Job_Post_Id',
            'jp.Closing_Date',
            'jp.No_Of_Vacancies',
             organizationDbConnection.raw(`
                CONCAT_WS(' ', 
                    CASE 
                        WHEN jp.Country_Code IS NOT NULL THEN jp.Country_Code
                        WHEN state.Country_Code IS NOT NULL THEN state.Country_Code
                        ELSE location.Country_Code 
                    END
                ) as countryCode
            `))
          .leftJoin( ehrTables.jobPostLocation + ' as jpl', 'jpl.Job_Post_Id', 'jp.Job_Post_Id')
          .leftJoin( ehrTables.location + ' as location', '   location.Location_Id', 'jpl.Location_Id')
          .leftJoin( ehrTables.state + ' as state', function() {
            this.on('state.State_Id', '=', organizationDbConnection.raw('COALESCE(location.State_Id, jp.State_Id)'))
          })
          .where('jp.Job_Post_Id', jobPostId)
          .then(async(data) => {
            return data;
          })
          .catch((err) => {
              console.log('Error in getCountryCode .catch() block', err);
              throw err;                   
          })
      )
  } catch(err){
      console.log('Error in getCountryCode main catch() block', err);
      throw err;
  }
}

async function updateJobPostLocation(organizationDbConnection, args, loginEmployeeId) {
  try {
      const result = await organizationDbConnection(ehrTables.jobPost)
          .update({
              City_Id: args.cityId,
              State_Id: args.stateId,
              Country_Code: args.countryCode,
              Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
              Updated_By: loginEmployeeId
          })
          .where('Job_Post_Id', args.jobPostId);

      return result;
  } catch (error) {
      throw error;
  }
}

async function addUpdateIndeedDetails(organizationDbConnection, args, loginEmployeeId, context, sourcedPostingId){
  try{ 
      let benefits = args.input.jobPostings[0].body.benefits;
      benefits =  JSON.stringify(benefits);
      let contactType = args.input.jobPostings[0].metadata.contacts[0].contactType;
      let contactTypeString = contactType.join(', ');
      if (args.integrationId && args.integrationId > 0) {
        return (
            organizationDbConnection(ehrTables.jobPostIndeedIntegration)
                .update({
                    Job_Post_Id: args.jobPostId,
                    Benefits: benefits,
                    Dynamic_Form_Id: args.dynamicFormId,
                    Contact_Type: contactTypeString,
                    Contact_Email: args.input.jobPostings[0].metadata.contacts[0].contactInfo.contactEmail,
                    Contact_Phone: args.input.jobPostings[0].metadata.contacts[0].contactInfo.contactPhone,
                    Contact_Name: args.input.jobPostings[0].metadata.contacts[0].contactInfo.contactName,
                    Status: args.status,
                    City_Id: args.cityId,
                    City_Name: args.cityName,
                    State_Id: args.stateId,
                    State_Name: args.stateName,
                    Workplace_Type: args.workplaceType,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Updated_By: loginEmployeeId
                }).where('Integration_Id', args.integrationId)
                .then(async(data) => {
                    if (data) {
                        if(args.updateLocation.toLowerCase() === 'yes') {
                          await updateJobPostLocation(organizationDbConnection, args, loginEmployeeId);
                        }
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return true;
                    } else {
                        throw 'SET0116'
                    }
                })
                .catch((catchError) => {
                    console.log('Error in addUpdateIndeedDetails .catch() block', catchError);
                    let errResult = commonLib.func.getError(catchError, 'SET0117');
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    throw new ApolloError(errResult.message, errResult.code);
                })
        )
    } else {
        return (
            organizationDbConnection(ehrTables.jobPostIndeedIntegration)
                .insert({
                    Job_Post_Id: args.jobPostId,
                    Employer_Job_Id: sourcedPostingId,
                    Benefits: benefits,
                    Dynamic_Form_Id: args.dynamicFormId,
                    Contact_Type: contactTypeString,
                    Contact_Email: args.input.jobPostings[0].metadata.contacts[0].contactInfo.contactEmail,
                    Contact_Phone: args.input.jobPostings[0].metadata.contacts[0].contactInfo.contactPhone,
                    Contact_Name: args.input.jobPostings[0].metadata.contacts[0].contactInfo.contactName,
                    Status: args.status,
                    City_Id: args.cityId,
                    City_Name: args.cityName,
                    State_Id: args.stateId,
                    State_Name: args.stateName,
                    Workplace_Type: args.workplaceType,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: loginEmployeeId,
                })
            .then(async(data) => {
                if (data) {
                    if(args.updateLocation.toLowerCase() === 'yes') {
                      await updateJobPostLocation(organizationDbConnection, args, loginEmployeeId);
                    }
                    let systemLogParam = {
                        userIp: context.User_Ip,
                        employeeId: loginEmployeeId,
                        organizationDbConnection: organizationDbConnection,
                        message: `Indeed jobpost details has been added to jobPostIndeedIntegration table successfully by ${loginEmployeeId}.`,
                      };
                      await commonLib.func.createSystemLogActivities(systemLogParam);
                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return true;
                } else {
                    throw 'SET0116'
                }
            })
            .catch((catchError) => {
                console.log('Error in addUpdateIndeedDetails .catch() block', catchError);
                let errResult = commonLib.func.getError(catchError, 'SET0117');
                organizationDbConnection ? organizationDbConnection.destroy() : null;
                throw new ApolloError(errResult.message, errResult.code);
            })
        )
    }
  } catch(err){
      console.log('Error in addUpdateIndeedDetails main catch() block', err);
      throw err;
  }
}