{"name": "integration", "version": "1.0.0", "description": "integration node modules", "main": "rohandler.js", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "@cksiva09/validationlib": "^1.3.82", "apollo-server": "^2.4.8", "apollo-server-lambda": "^2.15.0", "aws-sdk": "^2.1097.0", "axios": "^1.7.2", "child_process": "^1.0.2", "exceljs": "^4.4.0", "graphql": "^15.1.0", "jsonwebtoken": "^8.5.1", "knex": "2.3.0", "moment": "^2.29.1", "mysql": "^2.18.0", "net": "^1.0.2", "nodemailer": "^6.10.0", "path": "^0.12.7", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2", "serverless-step-functions": "^3.17.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC"}