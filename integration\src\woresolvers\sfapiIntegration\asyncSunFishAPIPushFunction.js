//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const{getConnection}=require("../../stepFunction/commonFunctions");
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const axios = require('axios');
//Require knex to make DB connection
const knex = require('knex');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const moment = require('moment');
const { getAPIIntegrationCredentials, decryptAPICredentials } = require('../../common/commonFunction');

//List the work schedule details in the work schedule form
module.exports.asyncSunFishAPIPushFunction = async (args) => {

    console.log('Inside asyncSunFishAPIPushFunction function started ', args);
    let organizationDbConnection;
    args = args?.status || args;
    try{ 

      
        console.log(' asyncSunFishAPIPushFunction args value => ', args);
        let connection = await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region, args.orgCode);
        organizationDbConnection = knex(connection.OrganizationDb);
        
        await organizationDbConnection(ehrTables.externalApiIntegrationLog)
        .update({ Status: 'InProgress'}).whereIn('Status', ['Open', 'Failed'])
        .where(qb => { 
            if (args.candidateId) {
                qb.where('Candidate_Id', args.candidateId);
            }
        });
        
        if(args && args.stepFunction === 0){
            // Employee have check the data push access rights
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, args.Employee_Id, null, '', 'UI', false, '178');   
            if ((Object.keys(checkRights).length === 0 || (checkRights.Role_Update === 0 && checkRights.Role_Add === 0))) {
                 // Employee do not have data push access rights then Failed status update
                 await organizationDbConnection(ehrTables.externalApiIntegrationLog)
                 .update({ Employee_Info_Sync_Status: 'Failed', Employee_Info_Failure_Reason: 'Employee do not have data push access rights', Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')  })
                 .whereIn('Employee_Info_Sync_Status', ['Open', 'Failed'])
                 .where(qb => { 
                     if (args.candidateId) {
                         qb.where('Candidate_Id', args.candidateId);
                     }
                 });
                 await updateSunfishApiOverAllStatus(organizationDbConnection, args.Employee_Id);
                 console.error("User do not have datapush access rights");
                 // throw error add or update rights is not exists
                 throw ('_DB0102');
            }
        }


        // Authentication default token read from aws secrets manager
        let secretKeys = await commonLib.func.getCredentials(process.env.region, process.env.dbSecretName);

        const sunFishDNSUrl = secretKeys.SFAPI_DNS;
        // Sunfish API calling authentication every time getting the new token   
        const accessTokenData = await getSunfishAPIAccessToken(secretKeys);

        if(accessTokenData && accessTokenData.status){
            // Push to employee data for sunfish api
            await pushEmployeeInfoDetails(organizationDbConnection,  args.candidateId, accessTokenData, sunFishDNSUrl) 

            // Retrieve the Open and Failed status records for Personal, Additional, Education, Attachement details
            let [pendingPersonalRecords, pendingAdditionalRecords, pendingEducationRecords, 
                pendingAttachementRecords] = await Promise.all( [
                getEmployeeInfoLogRecords(organizationDbConnection,"Personal_Info_Sync_Status", args.candidateId),
                getEmployeeInfoLogRecords(organizationDbConnection,"Additional_Info_Sync_Status", args.candidateId),
                getEmployeeInfoLogRecords(organizationDbConnection,"Education_Info_Sync_Status", args.candidateId),
                getEmployeeInfoLogRecords(organizationDbConnection,"Attachment_Info_Sync_Status", args.candidateId), 
            ]); 
    
            // Push to sunfish api for Personal, Additional, Education, Attachement details
            await Promise.all([
                pendingPersonalRecords && pendingPersonalRecords.length > 0 ? pushEmpPersonalInfoDetails(organizationDbConnection, pendingPersonalRecords, accessTokenData, sunFishDNSUrl) : null,
                pendingAdditionalRecords && pendingAdditionalRecords.length > 0 ? pushEmpAdditionalInfoDetails(organizationDbConnection, pendingAdditionalRecords, accessTokenData,sunFishDNSUrl) : null,
                pendingEducationRecords && pendingEducationRecords.length > 0 ? pushEmpEducationalInfoDetails(organizationDbConnection, pendingEducationRecords, accessTokenData, sunFishDNSUrl) : null,
                pendingAttachementRecords && pendingAttachementRecords.length > 0  ? pushEmpAttachementInfoDetails(organizationDbConnection, pendingAttachementRecords, accessTokenData, sunFishDNSUrl, args.orgCode) : null,
            ]);
         
            // Over all status update the Success or Failed
            await updateSunfishApiOverAllStatus(organizationDbConnection, args.Employee_Id);

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode:'',message:'Employee information successfully pushed to sunfish api.'};

        } else {
            
            // Authentication is failed employee data update the Failed status
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update({ Employee_Info_Sync_Status: 'Failed', Employee_Info_Failure_Reason: accessTokenData.message, Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') })
            .whereIn('Employee_Info_Sync_Status', ['Open', 'Failed'])
            .where(qb => { 
                if (args.candidateId) {
                    qb.where('Candidate_Id', args.candidateId);
                }
            });

             // Authentication is failed personal data update the Failed status for only employee data success
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update( {Personal_Info_Sync_Status: 'Failed', Personal_Info_Failure_Reason: accessTokenData.message, Personal_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') })
            .whereIn('Personal_Info_Sync_Status', ['Open', 'Failed']).where('Employee_Info_Sync_Status', 'Success')
            .where(qb => { 
                if (args.candidateId) {
                    qb.where('Candidate_Id', args.candidateId);
                }
            });

            // Authentication is failed additional data update the Failed status for only employee data success
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update( { Additional_Info_Sync_Status: 'Failed', Additional_Info_Failure_Reason: accessTokenData.message, Additional_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')})
            .whereIn('Additional_Info_Sync_Status', ['Open', 'Failed']).where('Employee_Info_Sync_Status', 'Success')
            .where(qb => { 
                if (args.candidateId) {
                    qb.where('Candidate_Id', args.candidateId);
                }
            });

             // Authentication is failed education data update the Failed status for only employee data success
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update( {Education_Info_Sync_Status: 'Failed', Education_Info_Failure_Reason: accessTokenData.message, Education_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')})
            .whereIn('Education_Info_Sync_Status', ['Open', 'Failed']).where('Employee_Info_Sync_Status', 'Success')
            .where(qb => { 
                if (args.candidateId) {
                    qb.where('Candidate_Id', args.candidateId);
                }
            });

             // Authentication is failed attachment data update the Failed status for only employee data success
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update( {Attachment_Info_Sync_Status: 'Failed', Attachment_Info_Failure_Reason: accessTokenData.message, Attachment_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')})
            .whereIn('Attachment_Info_Sync_Status', ['Open', 'Failed']).where('Employee_Info_Sync_Status', 'Success')
            .where(qb => { 
                if (args.candidateId) {
                    qb.where('Candidate_Id', args.candidateId);
                }
            });
            
            // Over all status update the Success or Failed
            await updateSunfishApiOverAllStatus(organizationDbConnection, args.Employee_Id);
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode:'_UH0001', message: accessTokenData.message};
        }

    } catch(err){
        console.error('Error in the asyncSunFishAPIPushFunction() function main catch block. ',err);
        organizationDbConnection ? await updateSunfishApiOverAllStatus(organizationDbConnection, 0) : null;
        // destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, '_UH0001');
        throw new ApolloError(errResult.message,errResult.code);
    }
}

//function to get data according to  status from  table two level
async function getEmployeeInfoLogRecords(anyDbConnection,columnName1, candidateId)
{
    try{
        let subQuery = anyDbConnection(ehrTables.externalApiIntegrationLog)
                        .select(anyDbConnection.raw('DISTINCT Employee_Id')).whereIn(columnName1, ['Open', 'Failed'])
                        .andWhere('Employee_Info_Sync_Status', 'Success')
        if(candidateId){
            subQuery = subQuery.where('Candidate_Id', candidateId);
        }
        let employeeLogs = await subQuery;
        if(employeeLogs && employeeLogs.length > 0){
            return employeeLogs.map((el) => el.Employee_Id);
        }
        return []; 
    } catch(e){
        console.error('Error in asyncSunFishAPIPushFunction getEmployeeInfoLogRecords function main catch block.', e);
        return false;
    }
};

async function updateSunfishAPIStatusLog(organizationDbConnection, employeeId, updateRecord)
{
    try{
        console.log('inside updateSunfishAPIStatusLog() function');
         await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update(updateRecord).where('Employee_Id','in', employeeId)
        return true;
    }catch(error){
        console.error("Error occured while updating updateSunfishAPIStatusLog main catch block", error);
        return false;
    }
}



async function updateSunfishApiEmployeeLogId(organizationDbConnection, employeeId, logId)
{
    try{
        console.log('inside updateSunfishApiEmployeeLogId() function');
         await organizationDbConnection(ehrTables.empJob)
            .update({Global_Resource_Id: logId}).where('Employee_Id','=', employeeId)
        return true;
    }catch(error){
        console.error("Error Occured while updating updateSunfishApiEmployeeLogId() main catch block", error);
        return false;
    }
}

async function updateSunfishApiOverAllStatus(organizationDbConnection, employeeId)
{
    try{
        console.log('Inside updateSunfishApiOverAllStatus() function');
         await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update({
                status: organizationDbConnection.raw(`
                  CASE WHEN Personal_Info_Sync_Status in ('Failed', 'Open') OR
                        Additional_Info_Sync_Status in ('Failed', 'Open') OR
                        Education_Info_Sync_Status in ('Failed', 'Open') OR
                        Attachment_Info_Sync_Status in ('Failed', 'Open') OR
                        Employee_Info_Sync_Status in ('Failed', 'Open')
                    THEN 'Failed'
                    ELSE 'Success'
                  END
                `),
                Updated_By: employeeId
              });
        return true;
    }catch(e){
        console.error("Error Occured while updating updateSunfishApiOverAllStatus() main catch block",e);
        return false;
    }
}




async function callingSunfishesAPI(url, headers, data, type){

    try {
        console.log('Inside callingSunfishesAPI function. ');
        const config = {
            method: 'post', 
            url: url,
            maxBodyLength: Infinity,
            data : data,
            headers: {
                'Authorization':  headers.data.TOKEN_TYPE+' '+headers.data.ACCESS_TOKEN,
                'REFRESH_TOKEN': headers.data.REFRESH_TOKEN,
                'Content-Type': 'application/json', 
            },
        };

        
        return await Promise.resolve(axios.request(config)
        .then((response) => {
                console.log('Successfully callingSunfishesAPI success response: ', response.data);
                if(typeof response.data === "string"){
                    const jsonString = response.data.match(/{.*}/)[0];
                    // Parse the JSON string to a JavaScript object
                    if(jsonString){
                        response.data = JSON.parse(jsonString);
                    }
                }
                if(response.data && response.data.DATA){
                    return response.data.DATA;
                }
             //   console.log("Inside callingSunfishesAPI  type= "+type+", request= ", config);
                return { status: false, message: 'Sunfishes api result is not found.'}; 
        })
        .catch((err) => {
                console.log("Error in callingSunfishesAPI request type= "+type+", request= ", config);
                console.error("Error occurred in callingSunfishesAPI catch block", err?.response?.data || err?.response || err);
                if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
                    return { status: false, message: `408 - Request Timeout. Oops! The server took too long to respond. Please try again in a moment.`};
                }
                const { status, statusText, data, data: { MESSAGE: errorMessage, HSTATUS: statusCode} = {} } = err?.response || {};
                const errorDetails = `Error: ${status || ' Unknown Status Code '} - ${statusText || ''}. Message: ${statusCode || ''} - ${errorMessage || data.toString() || 'An error occurred while calling the Sunfishe API '}`;
                return { status: false, message: errorDetails};
        }));

    } catch(err){
        console.error("Error occured in callingSunfishesAPI main catch block :  ", err?.response || err);
        const { status, statusText, data} = err?.response || {};
        const errorDetails = `Error: ${status || ' Unknown Status Code '} - ${statusText || ''}. Message: ${data ? data.toString() : 'Something went wrong while calling the api'}`;
        return { status: false, message: errorDetails};
    }
}  


async function pushEmpPersonalInfoDetails(organizationDbConnection, employeeIds, accessTokenData, sunFishDNSUrl) {
    try{
        console.log('Inside pushEmpPersonalInfoDetails() function');   
        let personalInfoList = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EMP')
            .select('EJ.Global_Resource_Id as LOGID','EMP.Employee_Id as employeeId', 'SFWPOS.Company_Id as COMPANYID', 'EJ.User_Defined_EmpId as EMPNO', 
            organizationDbConnection.raw( "CONCAT_WS(' ',EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name, EMP.Appellation) as FULLNAME"),
            'EMP.Emp_First_Name as FIRSTNAME', 'EMP.Emp_Middle_Name as MIDDLENAME', 'EMP.Emp_Last_Name as LASTNAME', 'EMP.Appellation as SUFFIX', 
            'EMP.Salutation as SALUTATION', 'EMP.Gender_Id as GENDER', 'NATION.Nationality as NATIONALITY', 'EMP.Place_Of_Birth as BIRTHPLACE', 'REL.Religion as RELIGION', 'MS.Marital_Status_Id as MARITALSTATUS',
            organizationDbConnection.raw("CASE WHEN EMP.DOB IS NOT NULL THEN  DATE_FORMAT(EMP.DOB, '%m/%d/%Y') ELSE NULL END as BIRTHDATE"),
            organizationDbConnection.raw('CASE WHEN EMP.Work_Email IS NOT NULL AND EMP.Work_Email !="" THEN EMP.Work_Email ELSE EMP.Personal_Email END as EMAIL'),
            organizationDbConnection.raw( "CONCAT_WS(' ',CD.pApartment_Name, CD.pStreet_Name) as ADDRESS"),
            organizationDbConnection.raw("'ID card' as ADDRESSTYPE"), 'CD.pCity as CITY', 'CD.pState as STATEPROVINCE', 'COUN.Country_Name as COUNTRY', 'CD.pPincode as ZIPCODE', 
            organizationDbConnection.raw("CASE WHEN CD.Mobile_No_Country_Code IS NOT NULL AND CD.Mobile_No IS NOT NULL THEN  CONCAT(CD.Mobile_No_Country_Code, CD.Mobile_No) ELSE CD.Mobile_No END as MOBILEPHONE"),
            'CD.Fax_No as EMERGENCYCONTACT', 'CD.Emergency_Contact_Name as CONTACTNAME', 'CD.Emergency_Contact_Relation as CONTACTREL', 'EMP.PAN as TAXFILENO', 
            organizationDbConnection.raw("CASE WHEN EJ.Date_Of_Join IS NOT NULL THEN  DATE_FORMAT(EJ.Date_Of_Join, '%m/%d/%Y') ELSE NULL END as TAXFILEDATE"))
            .leftJoin( ehrTables.contactDetails + ' as CD', 'CD.Employee_Id', 'EMP.Employee_Id')
            .leftJoin( ehrTables.religion + ' as REL' , 'REL.Religion_Id', 'EMP.Religion_Id')
            .leftJoin( ehrTables.nationality + ' as NATION' , 'NATION.Nationality_Id', 'EMP.Nationality_Id')
            .leftJoin( ehrTables.maritalStatus + ' as MS' , 'MS.Marital_Status_Id', 'EMP.Marital_Status')
            .innerJoin( ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EMP.Employee_Id')
            .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
            .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')  
            .leftJoin( ehrTables.country + ' as COUN', 'COUN.Country_Code', 'CD.pCountry')  
            .where('EMP.Employee_Id','in', employeeIds).groupBy('EJ.User_Defined_EmpId')

            if(personalInfoList && personalInfoList.length > 0 ) {
                for(let personalInfo of personalInfoList){
                    let employeeId = personalInfo.employeeId; delete personalInfo.employeeId;
                    personalInfo =  await replaceNullValues(personalInfo)
                    
                    // Each Personal information push to Sunfish API
                    const personalInfoResult = await callingSunfishesAPI(sunFishDNSUrl+'?ofid=SFAPIPJInbound.insertPersonalinfo', accessTokenData, personalInfo, "Personal");  
                    if(personalInfoResult.status) {
                        await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId],  { Personal_Info_Sync_Status: 'Success', Personal_Info_Failure_Reason:null, Personal_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                    } else {
                        await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Personal_Info_Sync_Status: 'Failed', Personal_Info_Failure_Reason: personalInfoResult.message.toString(), Personal_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')});
                    }
                }
                personalInfoList = [];
                console.log("Sunfish API pushEmpPersonalInfoDetails() Successfully Completed");
            }else{
                await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Personal_Info_Sync_Status: 'Success', Personal_Info_Failure_Reason: null, Personal_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                console.log("Sunfish API pushEmpPersonalInfoDetails() query no records ", personalInfoList)
            }

    }catch(error){
        console.error("Error occured in pushEmpPersonalInfoDetails() main catch block ", error);
        const { status, statusText, data } = error?.response || {};
        const errorDetails = `Error: ${status || 'Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${data ? data.toString() : 'Something went wrong while calling the api'}`;
        await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Personal_Info_Sync_Status: 'Failed', Personal_Info_Failure_Reason: errorDetails, Personal_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
    }
}


async function pushEmpAdditionalInfoDetails(organizationDbConnection, employeeIds, accessTokenData, sunFishDNSUrl) {
    try{
        console.log('inside pushEmpAdditionalInfoDetails() function');   
        const empDependentSubquery =  organizationDbConnection('emp_dependent as ED1')
          .select('ED1.Employee_Id','ED1.Dependent_First_Name','ED1.Dependent_Last_Name','ED1.Gender','ED1.Dependent_DOB','ED1.Relationship')
          .innerJoin(organizationDbConnection('emp_dependent')
            .select('Employee_Id').min('Dependent_Id as min_dep_id').groupBy('Employee_Id').as('ED2'),
            function() {
              this.on('ED1.Employee_Id', '=', 'ED2.Employee_Id')
                .andOn('ED1.Dependent_Id', '=', 'ED2.min_dep_id');
            }).as('ED');

        let additionalInfoList = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EMP')
        .select('EJ.Global_Resource_Id as LOGID','SFWPOS.Company_Id as COMPANYID', 'EMP.Employee_Id as employeeId', 'SAIL.Candidate_Id as candidateId', 'EJ.User_Defined_EmpId as EMPNO', 'BDG.Blood_Group_Code as BLOOD_TYPE', 'WSD.WorkSchedule_Code as SHIFTSTARTCODE',
            'EMP.Statutory_Insurance_Number as PHILHEALTHNO', 'EMP.PRAN_No as HDMFNO', 'EMP.UAN as SSSNO', 'EJ.Message as REMARK', 'ED.Relationship as FAMILYREL', 
             organizationDbConnection.raw("CONCAT_WS(' ',ED.Dependent_First_Name, ED.Dependent_Last_Name) as FAMILYNAME"), organizationDbConnection.raw("'N' as DECEASED"),
             organizationDbConnection.raw("CASE WHEN ED.Gender IS NOT NULL  THEN SUBSTRING(ED.Gender, 1, 1) ELSE NULL END as FAMILYGENDER"),
             organizationDbConnection.raw("CASE WHEN EJ.Date_Of_Join IS NOT NULL THEN  DATE_FORMAT(EJ.Date_Of_Join, '%m/%d/%Y') ELSE NULL END as SALARYEFFECTIVEDATE"),           
             organizationDbConnection.raw("CASE WHEN ED.Dependent_DOB IS NOT NULL THEN  DATE_FORMAT(ED.Dependent_DOB, '%m/%d/%Y') ELSE NULL END as FAMILYBIRTH"),
             organizationDbConnection.raw("'' as CustomField1"), 'EMP.Gender_Orientations as CustomField2', 'EMP.Pronoun as CustomField3',
             organizationDbConnection.raw("'' as CustomField4"),  organizationDbConnection.raw("'' as CustomField5"), organizationDbConnection.raw("'' as CustomField6"), 'TAXDE.Tax_Code as CustomField7' ,'BEA.Branch_Email_Address as CustomField8', 
            organizationDbConnection.raw("'' as CustomField9"), organizationDbConnection.raw("'' as CustomField10"), 'TKP.Timekeeping_Name as CustomField11', 'CER.Career_Name as CustomField12')
        .leftJoin( empDependentSubquery, 'ED.Employee_Id', 'EMP.Employee_Id')
        .innerJoin( ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EMP.Employee_Id')
        .leftJoin( ehrTables.careerPIC + ' as CER', 'CER.Career_Id', 'EJ.Career_Id')
        .leftJoin( ehrTables.timekeepingPIC + ' as TKP', 'TKP.Timekeeping_Id', 'EJ.Timekeeping_Id')
        .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
        .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')  
        .leftJoin ( ehrTables.location + ' as LOC', 'LOC.Location_Id', 'EJ.Location_Id')
        .leftJoin ( ehrTables.branchEmailAddress + ' as BEA', 'BEA.Location_Id', 'LOC.Location_Code')
        .leftJoin ( ehrTables.taxDetails + ' as TAXDE', 'TAXDE.Tax_Code', 'EMP.Tax_Code')
        .leftJoin ( ehrTables.workSchedule + ' as WSD', 'WSD.WorkSchedule_Id', 'EJ.Work_Schedule')
        .leftJoin ( ehrTables.bloodGroup + ' as BDG', 'BDG.Blood_Group_Name', 'EMP.Blood_Group')
        .join( ehrTables.externalApiIntegrationLog +' as SAIL', 'SAIL.Employee_Id', 'EMP.Employee_Id') 
        .where('EMP.Employee_Id','in', employeeIds).groupBy('EJ.User_Defined_EmpId')

        if(additionalInfoList && additionalInfoList.length > 0 ) {
            for(let additionalInfo of additionalInfoList){
                let employeeId = additionalInfo.employeeId; delete additionalInfo.employeeId;
                let candidateId = additionalInfo.candidateId; delete additionalInfo.candidateId;
                additionalInfo =  await replaceNullValues(additionalInfo)
                additionalInfo.SALARY = await getEmployeeSalary(organizationDbConnection, candidateId);
                
                // Employee additional information push to Sunfish API
                const additionalInfoResult = await callingSunfishesAPI(sunFishDNSUrl+'?ofid=SFAPIPJInbound.insertAdditionalinfo', accessTokenData, additionalInfo, " Additional");  
                if(additionalInfoResult.status) {
                    await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Additional_Info_Sync_Status: 'Success', Additional_Info_Failure_Reason: null, Additional_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')});
                } else {
                    await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Additional_Info_Sync_Status: 'Failed', Additional_Info_Failure_Reason: additionalInfoResult.message.toString(), Additional_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')});
                }
            }
            additionalInfoList = [];
            console.log("Sunfish API pushEmpAdditionalInfoDetails() Successfully Completed");
        } else{
            await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Additional_Info_Sync_Status: 'Success', Additional_Info_Failure_Reason: null, Additional_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
            console.log("Sunfish API pushEmpAdditionalInfoDetails() query no records ", additionalInfoList)
        }
       
    } catch(error){
        console.error("Error occured in pushEmpAdditionalInfoDetails() main catch block", error);
        const { status, statusText, data } = error.response || {};
        const errorDetails = `Error: ${status || 'Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${data ? data.toString() : 'Something went wrong while calling the api'}`;
        await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Additional_Info_Sync_Status: 'Failed', Additional_Info_Failure_Reason: errorDetails, Additional_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
    }
}


async function pushEmpEducationalInfoDetails(organizationDbConnection, employeeIds, accessTokenData, sunFishDNSUrl) {
    try{
        console.log('inside pushEmpEducationalInfoDetails() function');  
        let educationInfoList = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EMP')
        .select('EJ.Global_Resource_Id as LOGID', 'SFWPOS.Company_Id as COMPANYID', 'EJ.User_Defined_EmpId as EMPNO', 
         'EMP.Employee_Id as employeeId', 'EE.City as EDUCCITY', 'EE.State as EDUCSTATE', 
         organizationDbConnection.raw('CASE WHEN EE.Start_Date IS NOT NULL  THEN YEAR(EE.Start_Date) ELSE EE.Year_Of_Start END as EDUCLEVELSTART'),
         organizationDbConnection.raw('CASE WHEN EE.End_Date IS NOT NULL  THEN YEAR(EE.End_Date) ELSE EE.Year_Of_Passing END as EDUCLEVELEND'),
        'EE.Country as EDUCCOUNTRY', 'EDUI.Institution_Code as INSTITUTION', 'EDUS.Specialization_Code as COURSE', 'CD.Course_Code as EDUCLEVEL')
        .leftJoin(ehrTables.empEducation + ' as EE', 'EE.Employee_Id', 'EMP.Employee_Id')
        .leftJoin(ehrTables.eduSpecialization + ' as EDUS', 'EDUS.Specialization_Id', 'EE.Specialization_Id')
        .leftJoin(ehrTables.eduInstitution + ' as EDUI', 'EDUI.Institution_Id', 'EE.Institution_Id')
        .innerJoin( ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EMP.Employee_Id')
        .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
        .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')  
        .leftJoin(ehrTables.courseDetails + ' as CD', 'CD.Course_Id', 'EE.Education_Type')        
        .where('EMP.Employee_Id','in', employeeIds);

        if(educationInfoList && educationInfoList.length > 0 ) {
            for(let employeeId of employeeIds) {
                let educationInfo = educationInfoList.filter(s=> s.employeeId === employeeId);
                if(educationInfo && educationInfo.length > 0){
                    let educationInfoObject = {}
                    educationInfo.forEach((obj , index) => {delete obj.employeeId; Object.keys(obj).forEach(key => key !== 'EMPNO' && key !== 'COMPANYID' && key !== 'LOGID' ? educationInfoObject[`${key}${index+1}`] = obj[key] : educationInfoObject[`${key}`] = obj[key])});
                    
                    const courseKeys = ["INSTITUTION1", "COURSE1", "EDUCLEVEL1", "EDUCLEVELSTART1", "EDUCLEVELEND1", "EDUCCITY1", "EDUCSTATE1", "EDUCCOUNTRY1",
                        "INSTITUTION2", "COURSE2", "EDUCLEVEL2", "EDUCLEVELSTART2", "EDUCLEVELEND2", "EDUCCITY2", "EDUCSTATE2", "EDUCCOUNTRY2",
                        "INSTITUTION3", "COURSE3", "EDUCLEVEL3", "EDUCLEVELSTART3", "EDUCLEVELEND3", "EDUCCITY3", "EDUCSTATE3", "EDUCCOUNTRY3"
                    ];
                    courseKeys.forEach(key =>  !educationInfoObject.hasOwnProperty(key) ? educationInfoObject[key] = "" : null);
                    
                    educationInfoObject =  await replaceNullValues(educationInfoObject)
                    const educationInfoResult = await callingSunfishesAPI(sunFishDNSUrl+'?ofid=SFAPIPJInbound.insertEducationinfo', accessTokenData, educationInfoObject, 'Education');  
                    if(educationInfoResult.status) {
                        await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Education_Info_Sync_Status: 'Success', Education_Info_Failure_Reason:null, Education_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                    } else {
                        await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId] , { Education_Info_Sync_Status: 'Failed', Education_Info_Failure_Reason: educationInfoResult.message.toString(), Education_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                    }
                }
            }
            employeeIds = [], educationInfoList = [];
            console.log("Sunfish API pushEmpEducationalInfoDetails() Successfully Completed")
        }else{
            await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Education_Info_Sync_Status: 'Success', Education_Info_Failure_Reason: null, Education_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
            console.log("Sunfish API pushEmpEducationalInfoDetails() query no records ", educationInfoList)
        }
    } catch(error){
        console.error("Error occured in pushEmpEducationalInfoDetails main catch block", error);
        const { status, statusText, data } = error.response || {};
        const errorDetails = `Error: ${status || 'Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${data ? data.toString() : 'Something went wrong while calling the api'}`;
        await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Education_Info_Sync_Status: 'Failed', Education_Info_Failure_Reason: errorDetails, Education_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
    }
}

async function pushEmpAttachementInfoDetails(organizationDbConnection, employeeIds, accessTokenData, sunFishDNSUrl, orgCode) {
    try{
        console.log('inside pushEmpAttachementInfoDetails() function');  
        const education = [

            organizationDbConnection(ehrTables.empDocumentCategory + " as EDC")
            .select( 
                'EJ.Global_Resource_Id as LOGID', 'SFWPOS.Company_Id as COMPANYID', 'EJ.User_Defined_EmpId as EMPNO', 'EDC.Employee_Id as employeeId',
                'ED.File_Name as ATTACHMENTFILE', 'DST.Document_Sub_Type as DOCUMENTNAME',
                organizationDbConnection.raw("CONCAT_WS('', EDC.Document_Id, EDC.Employee_Id, EDC.Document_Sub_Type_Id) as DOCUMENTNO"),
                organizationDbConnection.raw("CASE WHEN EDC.Added_On IS NOT NULL THEN  DATE_FORMAT(EDC.Added_On, '%m/%d/%Y') ELSE NULL END as EFFECTIVEDATE"),
                organizationDbConnection.raw("'' as EXPIRYDATE"))
            .innerJoin(ehrTables.documentSubType + ' as DST', 'DST.Document_Sub_Type_Id', 'EDC.Document_Sub_Type_Id')
            .innerJoin(ehrTables.empDocuments + " as ED", "ED.Document_Id", "EDC.Document_Id")
            .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EDC.Employee_Id')
            .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
            .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')  
            .where('EDC.Employee_Id', 'in', employeeIds)
            .groupBy(['EDC.Document_Id', 'EJ.User_Defined_EmpId']),



            organizationDbConnection(ehrTables.employeeAccreditationDetails + " as EAD")
                .select('EJ.Global_Resource_Id as LOGID', 'SFWPOS.Company_Id as COMPANYID','EJ.User_Defined_EmpId as EMPNO', 'EAD.Employee_Id as employeeId',
                    'EAD.File_Name as ATTACHMENTFILE', 'ACT.Accreditation_Type as DOCUMENTNAME',
                    organizationDbConnection.raw("CONCAT_WS('', EAD.Accreditation_Detail_Id, EAD.Employee_Id, EAD.Accreditation_Category_And_Type_Id) as DOCUMENTNO"), 
                    organizationDbConnection.raw("CASE WHEN EAD.Received_Date IS NOT NULL THEN  DATE_FORMAT(EAD.Received_Date, '%m/%d/%Y') ELSE NULL END as EFFECTIVEDATE"),
                    organizationDbConnection.raw("CASE WHEN EAD.Expiry_Date IS NOT NULL THEN  DATE_FORMAT(EAD.Expiry_Date, '%m/%d/%Y') ELSE NULL END as EXPIRYDATE"))
                .innerJoin(ehrTables.accreditationCategoryAndType + " as ACT", "ACT.Accreditation_Category_And_Type_Id", "EAD.Accreditation_Category_And_Type_Id")
                .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EAD.Employee_Id')
                .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
                .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')   
                .where('EAD.Employee_Id', 'in', employeeIds)
                .groupBy(['EAD.Accreditation_Detail_Id', 'EJ.User_Defined_EmpId']),

            
            organizationDbConnection(ehrTables.empDrivingLicense + ' as ED').select('EJ.Global_Resource_Id as LOGID', 'ED.File_Name as ATTACHMENTFILE',
                'SFWPOS.Company_Id as COMPANYID', 'EJ.User_Defined_EmpId as EMPNO', 'ED.Issuing_Authority as DOCUMENTNAME', 'ED.Driving_License_No as DOCUMENTNO', 'ED.Employee_Id as employeeId',
                organizationDbConnection.raw("CASE WHEN ED.License_Issue_Date IS NOT NULL THEN  DATE_FORMAT(ED.License_Issue_Date, '%m/%d/%Y') ELSE NULL END as EFFECTIVEDATE"),
                organizationDbConnection.raw("CASE WHEN ED.License_Expiry_Date IS NOT NULL THEN  DATE_FORMAT(ED.License_Expiry_Date, '%m/%d/%Y') ELSE NULL END as EXPIRYDATE"))
                .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'ED.Employee_Id')
                .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
                .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')  
                .where('ED.Employee_Id', 'in', employeeIds).whereNotNull('ED.Driving_License_No')
                .groupBy(['ED.Driving_License_No', 'EJ.User_Defined_EmpId']),
            
            organizationDbConnection(ehrTables.empPassport + ' as EP').select('EJ.Global_Resource_Id as LOGID', 'EP.Employee_Id as employeeId', 'EP.File_Name as ATTACHMENTFILE',
                'SFWPOS.Company_Id as COMPANYID', 'EJ.User_Defined_EmpId as EMPNO', 'EP.Issuing_Authority as DOCUMENTNAME', 'EP.Passport_No  as DOCUMENTNO', 
                organizationDbConnection.raw("CASE WHEN EP.Issue_Date IS NOT NULL THEN  DATE_FORMAT(EP.Issue_Date, '%m/%d/%Y') ELSE NULL END as EFFECTIVEDATE"),
                organizationDbConnection.raw("CASE WHEN EP.Expiry_Date IS NOT NULL THEN  DATE_FORMAT(EP.Expiry_Date, '%m/%d/%Y') ELSE NULL END as EXPIRYDATE"))
                .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EP.Employee_Id')
                .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
                .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', 'SFWPOS.Pos_Code', 'DES.Designation_Code')  
                .where('EP.Employee_Id', 'in', employeeIds).whereNotNull('EP.Passport_No')
                .groupBy(['EP.Passport_No','EJ.User_Defined_EmpId']),

            organizationDbConnection('sunfish_attachment_status_log').whereIn('Employee_Id', employeeIds)
                
        ]

        let [ empDocumentCategory, employeeAccreditationDetails, empDrivinglicense, empPassport, attachmentStatusLog]  =  await Promise.all(education)
        let attachmentInfoList = empDrivinglicense.concat(empPassport).concat(empDocumentCategory).concat(employeeAccreditationDetails);

        if(attachmentInfoList && attachmentInfoList.length > 0 ) {
            for(let employeeId of employeeIds){
                
                let attachmentInfo = attachmentInfoList.filter(s=> s.employeeId === employeeId);
                attachmentInfo = await removeDuplicates(attachmentInfo);
                attachmentInfo = await filterByAttachmentStatusLog(attachmentInfo, attachmentStatusLog)

                if(attachmentInfo && attachmentInfo.length > 0){
                    let successFlag = true, message = '';
                    for(let employeeDocument of attachmentInfo){
                        delete employeeDocument.employeeId;
                        if(employeeDocument.ATTACHMENTFILE)
                            employeeDocument.ATTACHMENTFILE = await getBase64Attachment(employeeDocument.ATTACHMENTFILE, orgCode)
                        
                        employeeDocument =  await replaceNullValues(employeeDocument)
                        let attachmentInfoResult = await callingSunfishesAPI( sunFishDNSUrl+'?ofid=SFAPIPJInbound.insertAttachmentinfo', accessTokenData, employeeDocument, 'Attachment');
                        if(attachmentInfoResult.status){
                            await addSuccessAttachmentLog(organizationDbConnection, employeeDocument);
                        }else{
                            message = message ? message + ', '+ attachmentInfoResult.message : attachmentInfoResult.message
                            successFlag = false;
                        }
                    }
                    if(successFlag) {
                        await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Attachment_Info_Sync_Status: 'Success', Attachment_Info_Failure_Reason: null, Attachment_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                    } else {
                        await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Attachment_Info_Sync_Status: 'Failed', Attachment_Info_Failure_Reason: message.toString(), Attachment_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                    } 
                } else {
                    await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId],  { Attachment_Info_Sync_Status: 'Success', Attachment_Info_Failure_Reason: null, Attachment_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                }
            }
            employeeIds = [], attachmentInfoList = []
            console.log("Sunfish API pushEmpAttachementInfoDetails() Successfully Completed");
        }else{
            await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Attachment_Info_Sync_Status: 'Success', Attachment_Info_Failure_Reason: null, Attachment_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
            console.log("Sunfish API pushEmpAttachementInfoDetails() query no records ", attachmentInfoList)
        } 
    }
    catch(error){
        console.error("Error occured in pushEmpAttachementInfoDetails() main catch block", error);
        const { status, statusText, data } = error.response || {};
        const errorDetails = `Error: ${status || 'Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${data ? data.toString() : 'Something went wrong while calling the api'}`;
        await updateSunfishAPIStatusLog(organizationDbConnection, employeeIds,  { Attachment_Info_Sync_Status: 'Failed', Attachment_Info_Failure_Reason: errorDetails, Attachment_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
    }
}

function removeDuplicates(array) {
    const seen = new Set();
    return array.filter(item => {
      const key = `${item.LOGID}-${item.COMPANYID}-${item.DOCUMENTNO}-${item.EMPNO}`;
      if (seen.has(key)) {
        return false;
      } else {
        seen.add(key);
        return true;
      }
    });
}

function filterByAttachmentStatusLog(array, statusLog) {
    return array.filter(item1 => 
      !statusLog.some(item2 => 
        item1.LOGID == item2.Log_Id && 
        item1.COMPANYID == item2.Company_Id && 
        item1.DOCUMENTNO == item2.Document_No && 
        item1.EMPNO == item2.Employee_Id
      )
    );
  }

async function addSuccessAttachmentLog(organizationDbConnection, employeeDocument){

    try{
        await organizationDbConnection('sunfish_attachment_status_log')
        .insert({
            Log_Id: employeeDocument.LOGID,
            Employee_Id: employeeDocument.EMPNO,
            Company_Id: employeeDocument.COMPANYID,
            Document_No:employeeDocument.DOCUMENTNO
        })
    } catch(err){
        console.error("Error: addSuccessAttachmentLog catch block ", err)
    }
    
}


async function pushEmployeeInfoDetails(organizationDbConnection, candidateId, accessTokenData, sunFishDNSUrl) {

    try {
        console.log('inside pushEmployeeInfoDetails() function');  

        const apiIntegrationCredentials = await getAPIIntegrationCredentials(organizationDbConnection, 'Entomo');
        let decryptedCredentials = [];
        if (apiIntegrationCredentials) {
            decryptedCredentials = await decryptAPICredentials(apiIntegrationCredentials);
        }

        let employeeInfoList = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EPI')
        .select('EPI.Employee_Id as employeeId', 'EJ.User_Defined_EmpId as EMPNO', 'SFWPOS.Pos_Code as POSITIONCODE', 'SFWPOS.Job_Title_Code as JOBTITLECODE', 'SFWPOS.Lst_Grade_Code as GRADECODE', 'SFWPOS.Global_Grade as GLOBALGRADE',
            'SFWPOS.Cost_Code as COSTCENTERCODE', 'LOC.Location_Code as WORKLOCATIONCODE', 'SFWPOS.Company_Id as COMPANYID', 'SP.Service_Provider_Code as COMPANYCODE','SFWPOS.Department_Code as ORGANIZATIONUNITCODE', 
            organizationDbConnection.raw("CASE WHEN EJ.Date_Of_Join IS NOT NULL THEN  DATE_FORMAT(EJ.Date_Of_Join, '%m/%d/%Y') ELSE NULL END as JOINDATE"),
            organizationDbConnection.raw("CASE WHEN EJ.Confirmation_Date IS NOT NULL THEN  DATE_FORMAT(EJ.Confirmation_Date, '%m/%d/%Y') ELSE NULL END as PERMANENTDATE"),
            organizationDbConnection.raw("CASE WHEN EJ.Probation_Date IS NOT NULL THEN  DATE_FORMAT(EJ.Probation_Date, '%m/%d/%Y') ELSE NULL END as EMPLOYMENTENDDATE"),
            organizationDbConnection.raw('\'\' AS GROUPCODE'), organizationDbConnection.raw('\'\' AS DIVISIONCODE'),organizationDbConnection.raw('\'\' AS SECTIONCODE'),
            organizationDbConnection.raw('\'\' AS DEPTCODE'), 'SFWPOS.Parent_Path as parentPath',

           // organizationDbConnection.raw("CONCAT_WS(' ',EPI1.Emp_First_Name, EPI1.Emp_Middle_Name, EPI1.Emp_Last_Name) as DIRECTSPV"),
            'EJ1.User_Defined_EmpId as DIRECTSPV',
            'ET.Employee_Type_Code as EMPLOYMENTSTATUSCODE','JF.Job_Family_Level_Code as JOBFAMILYLEVEL', 'JF.Job_Family_Code as JOBFAMILY', 'JF.Job_Family_Grade_Code as JOBFAMILYGRADE')
       
        .join( ehrTables.externalApiIntegrationLog +' as SAIL', 'SAIL.Employee_Id', 'EPI.Employee_Id') 
        .innerJoin( ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EPI.Employee_Id')
        .innerJoin( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
        .innerJoin( ehrTables.SFWPOrganizationStructure + ' as SFWPOS', function() {
            this.on('SFWPOS.Pos_Code', '=', 'DES.Designation_Code')
          })
        .leftJoin( ehrTables.serviceProvider + ' as SP', 'SP.Service_Provider_Id', 'EJ.Service_Provider_Id')
        .leftJoin( ehrTables.sfwpJobFamily + ' as JF', 'JF.Job_Title_Code', 'SFWPOS.Job_Title_Code')
        .leftJoin( ehrTables.employeeType + ' as ET', 'ET.EmpType_Id', 'EJ.EmpType_Id')
        .leftJoin( ehrTables.empJob +' as EJ1', 'EJ1.Employee_Id', 'EJ.Manager_Id')
        .leftJoin( ehrTables.location + ' as LOC', 'LOC.Location_Id', 'EJ.Location_Id')
        .whereIn('SAIL.Employee_Info_Sync_Status', ['Open', 'Failed'])
        .where(qb => {
            if (candidateId) {
                qb.where('SAIL.Candidate_Id', candidateId);
            }
        }).groupBy('EJ.User_Defined_EmpId')

        if(employeeInfoList && employeeInfoList.length > 0 ) {
                
            for(let employeeInfo of employeeInfoList){

                let employeeId = employeeInfo.employeeId; delete employeeInfo.employeeId;
                let parentStructureIds = employeeInfo.parentPath ? employeeInfo.parentPath.split(",") : []; 
                delete employeeInfo.parentPath;

                let [secondLineManagerId, parentStructureDetails, rehireData] = await Promise.all([
                    commonLib.func.getSecondLineManager(organizationDbConnection, employeeId),
                    organizationDbConnection(ehrTables.SFWPOrganizationStructure).select('Pos_Code', 'Org_Level')
                    .whereIn('Originalpos_Id', parentStructureIds),
                    getRehireLicenseNumber(organizationDbConnection, employeeId, apiIntegrationCredentials, decryptedCredentials)
                ]);

                employeeInfo.LOG_ID = "";
                if(rehireData){

                    let updateRehireData = { IsRehire:rehireData.isReHire,  Rehire: { Sync_Status: rehireData.status ? 'Success' : 'Failed', Failure_Reason: rehireData.message,  Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')}}
                    employeeInfo.LOG_ID = rehireData?.licenseNumber || "";

                    if(!rehireData.status){
                        await organizationDbConnection(ehrTables.externalApiIntegrationLog)
                        .update(
                        {
                            Employee_Info_Sync_Status: 'Failed', 
                            Employee_Info_Failure_Reason: rehireData?.message?.toString(), 
                            Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'), 
                            Data_Sync_Result: JSON.stringify(updateRehireData)
                        })
                        .where('Employee_Id', employeeId);
    
                        continue;
                    }
                    await organizationDbConnection(ehrTables.externalApiIntegrationLog)
                    .update({ Data_Sync_Result: JSON.stringify(updateRehireData) })
                    .where('Employee_Id', employeeId);

                   
                }

                let secondLineManager = await organizationDbConnection(ehrTables.empJob)
                .select('User_Defined_EmpId').where('Employee_Id', secondLineManagerId);
                employeeInfo.DIRECTMGR =  secondLineManager && secondLineManager.length > 0 ? secondLineManager[0]?.User_Defined_EmpId : null

                if(parentStructureDetails && parentStructureDetails.length){
                    employeeInfo.GROUPCODE = parentStructureDetails.filter(grp=> grp.Org_Level === 'GRP')[0]?.Pos_Code || '';
                    employeeInfo.DIVISIONCODE = parentStructureDetails.filter(div=> div.Org_Level === 'DIV')[0]?.Pos_Code || '';
                    employeeInfo.SECTIONCODE = parentStructureDetails.filter(sec=> sec.Org_Level === 'SEC')[0]?.Pos_Code || '';
                    employeeInfo.DEPTCODE = parentStructureDetails.filter(dept=> dept.Org_Level === 'DEPT')[0]?.Pos_Code || '';
                }

                employeeInfo =  await replaceNullValues(employeeInfo);
                
                // Each Employee information push to Sunfish API 
                let employeeInfoResult = await callingSunfishesAPI(sunFishDNSUrl+'?ofid=SFAPIPJInbound.insertEmployeeData', accessTokenData, employeeInfo, ' Employee');  
                if(employeeInfoResult.status) {

                    // Employee data success message receives logId.
                    var match = employeeInfoResult.message.match(/\{([A-F0-9-]+)\}/);
                    if (match) {
                        employeeInfoResult.logId = match[1];
                    }else{
                        employeeInfoResult.logId = employeeInfoResult.data;
                    }
                    // Each employee records Logid update to emp_job table
                    await updateSunfishApiEmployeeLogId(organizationDbConnection, employeeId, employeeInfoResult.logId)
                    await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Employee_Info_Sync_Status: 'Success', Employee_Info_Failure_Reason: null, Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                } else {
                    await updateSunfishAPIStatusLog(organizationDbConnection, [employeeId], { Employee_Info_Sync_Status: 'Failed', Employee_Info_Failure_Reason: employeeInfoResult.message.toString(), Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') });
                }
            }
            employeeInfoList = [];
            console.log("Sunfish API pushEmployeeInfoDetails() Successfully Completed")
        }else{
            await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update({ Employee_Info_Sync_Status: 'Failed', Employee_Info_Failure_Reason: 'Employee details is not found. please try again after some time', Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') })
            .whereIn('Employee_Info_Sync_Status', ['Open', 'Failed'])
            .where(qb => { 
                if (candidateId) {
                    qb.where('Candidate_Id', candidateId);
                }
            });
            console.log("Sunfish API pushEmployeeInfoDetails() query no found records ", employeeInfoList);
        }
    } catch(error){
        console.error("Error occured in pushEmployeeInfoDetails() main catch block", error);
        const { status, statusText, data } = error?.response || {};
        const errorDetails = `Error: ${status || 'Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${data ? data.toString() : 'Something went wrong while calling the api'}`;
        await organizationDbConnection(ehrTables.externalApiIntegrationLog)
            .update({ Employee_Info_Sync_Status: 'Failed', Employee_Info_Failure_Reason: errorDetails, Employee_Info_Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss') })
            .whereIn('Employee_Info_Sync_Status', ['Open', 'Failed'])
            .where(qb => { 
                if (candidateId) {
                    qb.where('Candidate_Id', candidateId);
                }
            });
    }
}

async function getEmployeeSalary(organizationDbConnection, candidateId) {
    try {
        console.log("Inside getEmployeeSalary() function ")
        const employeeSalaryDetails = await organizationDbConnection('emp_generated_documents')
        .select('Additional_Details').whereNotNull('Additional_Details')
        .where('Candidate_Id', candidateId)
        .andWhere('Additional_Details', 'like', '%basicPay%');
        if(employeeSalaryDetails && employeeSalaryDetails.length > 0){
            let additional = JSON.parse(employeeSalaryDetails[0].Additional_Details);
            
            if(typeof additional === 'string'){
                additional = JSON.parse(additional)
            }
            return additional.basicPay || 0;
        }
        return 0;
    } catch(error){
        console.error("Error Occured While getting from getEmployeeSalary() main catch block ", error);
        return 0;
    }
}


async function getSunfishAPIAccessToken(paramsValue){

    try {
        console.log("Inside getSunfishAPIAccessToken() function ")

        if(!paramsValue.SFAPI_DNS || !paramsValue.SFAPI_Account_Name || !paramsValue.SFAPI_Api_Key){
            return { status: false, message: `Authentication failed due to missing in DNS or Account Name or API Key. Please verify your configuration.`}
        }

        const url = paramsValue.SFAPI_DNS+'?accname='+paramsValue.SFAPI_Account_Name+'&api_key='+paramsValue.SFAPI_Api_Key;

        console.log("Inside getSunfishAPIAccessToken() request", url)
        const result =  await Promise.resolve(await axios.post(url, {}, { timeout: 15000}));
      
        if(result && result.data){
            const authResponse = result.data
            if( authResponse.DATA && authResponse.DATA.ACCESS_TOKEN){
                return { status: true, message: 'Authentication Success', data: {...authResponse.DATA}};
            }
            return { status: authResponse.DATA ? authResponse.DATA.status : false, message:  authResponse.DATA ? authResponse.DATA.message : 'Authentication data is not available'};
        }else{
            return { status: false, message: 'Authentication data is not provided from API.'}
        }

      } catch (error) {
        console.error('Error Occured While getting from getSunfishAPIAccessToken() main catch block ', error?.response?.data || error?.response || error);
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
            return { status: false, message: `408 - Request Timeout. The authentication server more than 15 seconds took too long to respond. Please try again in a moment`};
        }
        const { status, statusText, data } = error?.response || {};
        const errorDetails = `Error: ${status || ' Unknown API Status Code '} - ${statusText || ''}. Message: ${data?.MESSAGE || data?.toString() || 'Unhandled authentication error. Something went wrong while calling the authentication API. '}`;
        return { status: false, message: errorDetails};
      }
    
} 

function replaceNullValues(objectValue) {
    if (typeof objectValue === undefined || objectValue === null) {
        return '';
    }
    if (Array.isArray(objectValue)) {
        return objectValue.map(item => replaceNullValues(item));
    }
    if (typeof objectValue === 'object') {
        const newObj = {};
        for (const key in objectValue) {
            if (objectValue.hasOwnProperty(key)) {
                newObj[key] = replaceNullValues(objectValue[key]);
            }
        }
        return newObj;
    }
    return objectValue;
}

async function getBase64Attachment(fileKey, orgCode) {

    const AWS = require('aws-sdk');
    const s3 = new AWS.S3({ region: process.env.region });

    try {
        fileKey = `${process.env.domainName}/${orgCode}/Employees Document Upload/${fileKey}`;
        console.log(fileKey)
        const data = await s3.getObject({Bucket: process.env.documentsBucket, Key: fileKey}).promise();
        const base64String = data.Body.toString('base64');
        return base64String;
      } catch (error) {
        return  '';
      }
}


async function getRehireLicenseNumber(organizationDbConnection, employeeId, apiIntegrationCredentials, decryptedCredentials) {
  
    let isRehire = false;
    const customFieldValues = await getCustomFieldValues(organizationDbConnection, employeeId);
        
    if(customFieldValues?.IsRehire) {
        isRehire = true;
        if (!apiIntegrationCredentials) {
            console.error('Entomo API configuration is missing', apiIntegrationCredentials);
            return { isReHire: isRehire, status: false, message: 'Rehired employee processing failed due to missing Entomo API configuration.', licenseNumber: null};
        }
        if(!decryptedCredentials || decryptedCredentials.length != 1 || decryptedCredentials.includes("")){
            console.error('Entomo API decription credentials are missing.', decryptedCredentials);
            return { isReHire: isRehire, status: false, message: 'Rehired employee processing failed due to Entomo API decryption failure.', licenseNumber: null}
        }
        try{
        
            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: apiIntegrationCredentials.baseURL,
                headers: { 
                    'x-api-key': decryptedCredentials[0], 
                    'Content-Type': 'application/json'
                },
                data : JSON.stringify({ "emp_code": customFieldValues.PreviousEmployeeId })
            };

            const response = await axios.request(config)
            const data = typeof response?.body == "string" ? JSON.parse(response?.body || '{}') : response?.body || {};
            const statusCode  = response.statusCode;

            const messages = {
                200: `Successfully retrieved License Number: ${data?.license_number || ''} and Username: ${data?.username || ''}.`,
                400: `400 - `+ data?.error || 'Employee is rehired, but emp_code is required for processing.',
                404: '404 - '+ data?.message || 'Employee is rehired, but the employee does not exist in the system.',
                500: '500 - '+ data?.message || data?.error || 'Internal server error occurred while processing the rehired employee.'
            };

            if (statusCode === 200 && data.license_number) {
                return { isReHire: isRehire, status: true, message: messages[200], licenseNumber: data.license_number };
            }

            const message = messages[statusCode] || `Employee is rehired, Unhandled status code: ${statusCode}`;
            return { status: isRehire, message: message, licenseNumber: null};

        } catch(error){
            // Log the error details
            console.error(`Error Entomo API function main catch block.`, error?.response?.data || error?.response || error);
            // Destructure error response details, if available
            const { status, statusText, data } = error.response || error || {};
            if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
                return { status: isRehire, message: `408 - Request Timeout: Rehire process for the employee timed out. The server took too long to respond..`, licenseNumber: null };
            }
            // Return failure status with error details
            return { status: isRehire, message: `${status || ' Unknown Status Code '} - ${statusText || 'Unknown Status Text'}`, licenseNumber: null };
        }
    }
    return { isReHire: isRehire, status: true, message: 'Employee has been newly hired.', licenseNumber: null};
}


async function getCustomFieldValues(organizationDbConnection, employeeId) {
    try {
        const [teamSummaryCustomValues, customFieldAssociated] = await Promise.all([
            organizationDbConnection(ehrTables.teamSummaryCustomFieldValues + ' as TSCV').pluck('TSCV.Custom_Field_Value')
            .whereNotNull('TSCV.Custom_Field_Value').where('TSCV.Primary_Id', employeeId),

            organizationDbConnection('custom_field_associated_forms as CFAF')
                .select('CFAF.Integration_Mapping_Key', 'CFAF.Custom_Field_Id', 'CF.Custom_Field_Type')
                .innerJoin('custom_fields as CF', 'CF.Custom_Field_Id', 'CFAF.Custom_Field_Id')
                .whereIn('CFAF.Form_Id', ['243'])
                .whereIn('CFAF.Integration_Mapping_Key', ['PreviousEmployeeId', 'IsRehire'])
        ]);

        const teamSummary = teamSummaryCustomValues && teamSummaryCustomValues.length ? JSON.parse(teamSummaryCustomValues[0]) : {};
        if(customFieldAssociated && customFieldAssociated.length > 0) {
            return customFieldAssociated.reduce((acc, { Integration_Mapping_Key, Custom_Field_Id, Custom_Field_Type }) => {
                const value = teamSummary[Custom_Field_Id];
                acc[Integration_Mapping_Key] = Integration_Mapping_Key === 'IsRehire'
                    ? String(value).toLowerCase() === 'yes'
                    : value || '';
            
                return acc;
            }, {});
        }
        return {PreviousEmployeeId: null, IsRehire: false};
    } catch (error) {
        console.error("Error occurred in getCustomFieldValues() main catch block", error);
        throw error;
    }
}

