//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
const { getConnection } = require('../../stepFunction/commonFunctions')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')
//Require knex to make DB connection
const knex = require('knex')
const moment = require('moment')
const axios = require('axios');
const {
  uploadFileToS3Bucket
} = require('../../common/commonFunction')

let indeedApplyId;

module.exports.asyncIndeedWebHookFunction = async (args) => {
  console.log('Inside asyncIndeedWebHookFunction function started ');
  let organizationDbConnection
  try {
    if ( args && Object.keys(args).length ) {
      let dataObject = args;
      console.log("My dataObject:", dataObject.response.applicant.resume.json);
      let applicantDetails  = dataObject?.response?.applicant?.resume?.json;
      let jobpostId = dataObject?.response?.job?.jobId;
      indeedApplyId = dataObject?.response?.id;
      let indeedScreenerAnswers = dataObject?.response?.screenerQuestionsAndAnswers?.questionsAndAnswers;
      if(jobpostId){
        jobpostId = jobpostId.split('-');
      }
      let orgCode = jobpostId[0] ? jobpostId[0] : null;
      let hrappJobpostId = jobpostId[1] ? jobpostId[1] : null
      let resumeFile = null
      let databaseConnection = await getConnection(
        process.env.stageName,
        process.env.dbPrefix,
        process.env.dbSecretName,
        process.env.region,
        orgCode
      )
      // check whether data exist or not
      if (databaseConnection && Object.keys(databaseConnection).length) {
        organizationDbConnection = knex(databaseConnection.OrganizationDb)
        let dob = applicantDetails?.personalDetails?.dateOfBirth ? moment(applicantDetails.personalDetails.dateOfBirth, 'DD-MM-YYYY').format('YYYY-MM-DD') : null;
        let disability = applicantDetails?.personalDetails?.disability || applicantDetails?.personalDetails?.disability?.toLowerCase() === 'yes' ? 1: 0;
        let gender = applicantDetails?.personalDetails?.gender;
        let genderId = null; 
        let jobpostData = await getJobpostId(organizationDbConnection, hrappJobpostId);
        if(jobpostData && jobpostData.length == 0){
          throw "InvalidJobpost";
        }
        // let jobClosingDate = jobpostData[0]?.Closing_Date;
        // Get the current date
        // const currentDate = moment();
        // Convert the JobClosing_Date to a moment object
        // const closingDate = moment(jobClosingDate, 'YYYY-MM-DD HH:mm:ss');
        // Compare the dates to check if the job post is expired
        // if (currentDate.isAfter(closingDate)) {
        //   throw "JobPostClosed";
        // }
        if(gender){
          genderId = await getId(organizationDbConnection, [gender], "gender", "Gender_Id", "Gender");
          console.log("my genderId:", genderId);
          genderId = genderId[0]?.Gender_Id;
        }
        let fullName = dataObject?.response?.applicant?.fullName;
        let parts = fullName?.trim().split(/\s+/); // splits by one or more spaces
        let firstName = parts && parts[0] || '';
        let lastName = parts?.slice(1).join(' ') || ''; // handles middle/last names
        return (
          organizationDbConnection
            .transaction(function (trx) {
              let candidatePersonalInfoObject = {
                Emp_First_Name: applicantDetails?.firstName ? applicantDetails.firstName: firstName,
                Emp_Last_Name: applicantDetails?.lastName ? applicantDetails.lastName: lastName,
                DOB: dob,
                Gender:applicantDetails?.personalDetails?.gender,
                Gender_Id: genderId,
                Physically_Challenged: disability ? disability : 0,
                Personal_Email: dataObject?.response?.applicant?.email,
                Ethnic_Race: applicantDetails?.personalDetails?.ethnicity,
                Source_Type: 'ATS',
                Hobbies:	applicantDetails?.personalDetails?.hobbiesAndInterests,
                Added_On: moment.utc().format('YYYY-MM-DD'),
                Added_By: 1
              }
              return (
                organizationDbConnection('candidate_personal_info')
                  .insert(candidatePersonalInfoObject)
                  .transacting(trx)
                  .then(async (data) => {
                    console.log("My data after insertion:", data);
                    var lastInsertedId = data[0] /**last inserted ID */
                    let contactNumber = applicantDetails?.phoneNumber;
                    console.log("My contact number:", contactNumber, typeof contactNumber);
                    if(contactNumber){
                      contactNumber = contactNumber.split(' ');
                    }
                    let candidateContactDetailsObject = {
                      Candidate_Id: lastInsertedId,
                      Mobile_No_Country_Code: contactNumber && contactNumber[0] ? contactNumber[0] : null,
                      Mobile_No: contactNumber && contactNumber[1] ? contactNumber[1] : dataObject?.response?.applicant?.phoneNumber ? dataObject.response.applicant.phoneNumber: null,
                      pCity: applicantDetails?.location?.city || null,
                      pCountry:applicantDetails?.location?.country || null,
                      pPincode: applicantDetails?.location?.postalcode || null
                    }
                    console.log("candidateContactDetailsObject", candidateContactDetailsObject);

                    if (dataObject?.response?.applicant?.resume?.file?.data) {
                      let resumeData = dataObject.response.applicant.resume;
                      let domainName = process.env.domainName;
                      const bucketName = process.env.documentsBucket;
                      const formattedDate = moment().format(
                        'YYYY-MM-DD.HH:mm:ss'
                      )
                      let name = resumeData?.file?.fileName || lastInsertedId;
                      let finalFileName = `resume?${formattedDate}??${name}`;
                      const fileKey =
                        domainName +
                        '_' +
                        '/' +
                        orgCode +
                        '/resume/' +
                        finalFileName;
                      let uploadedresult = await uploadFileToS3Bucket(fileKey, resumeData.file, bucketName);
                      console.log('uploadedresult', uploadedresult, uploadedresult.length);
                      if (!uploadedresult) {
                        resumeFile = null
                      }
                      resumeFile = finalFileName+uploadedresult;
                      console.log("My resumeFileName is here:", resumeFile);
                    }
                    
                    /**insert candidate's contact details */
                    return organizationDbConnection(
                      'candidate_contact_details'
                    )
                      .insert(candidateContactDetailsObject)
                      .transacting(trx)
                      .then(async () => {
                      await Promise.all([
                          insertEducationDetails(organizationDbConnection, applicantDetails, lastInsertedId, trx),
                          addCandidateCertificationDetails(organizationDbConnection, applicantDetails, lastInsertedId, trx),
                          addlanguages(organizationDbConnection, applicantDetails, lastInsertedId, trx),
                          addCandidateRecruitmentDetails(organizationDbConnection, hrappJobpostId, applicantDetails, lastInsertedId, resumeFile, dataObject, trx),
                          insertCandidateExperienceDetails(organizationDbConnection, applicantDetails, lastInsertedId, trx),
                          addScreenerQuestionsAndAnswers(organizationDbConnection, indeedScreenerAnswers, indeedApplyId, trx)
                        ])
                        // Call the API only after DB insertions are successful
                        await indeedDispositionSyncAPI(indeedApplyId);
                        return {
                          errorCode: '',
                          message: 'Candidate details added successfully.',
                          validationError: null,
                        }
                      })
                  }).catch(async err=>{
                    console.log('Error in the asyncIndeedWebHookFunction() function main catch block.', err);
                    throw err;
                  })
                  /**if the organizationDbConnection queries are executed successfully */
                  .then(trx.commit)
                  .catch(trx.rollback)
              ) /**rollback if any error occurs */
            })
            /**return the success result to the user */
            .then(function (result) {
              return result
            })

            /**check and return if any error occured */
            .catch(function (err) {
              console.log(
                'Error in insertJobCandidates .catch function',
                err
              )
              throw err;
            })
            /**close the database connection */
            .finally(() => {
              organizationDbConnection.destroy()
            })
        )
      } else {
        console.log('Error while creating database connection')
      }
    }
  } catch (err) {
    console.error(
      'Error in the asyncIndeedWebHookFunction() function main catch block. ',
      err
    )
    // destroy DB connection
    organizationDbConnection ? organizationDbConnection.destroy() : null
    throw err;
  }
}

async function getJobpostId(organizationDbConnection, hrappJobpostId) {
  try {
    return organizationDbConnection('job_post')
      .select('Job_Post_Id', 'Status')
      .where('Job_Post_Id', hrappJobpostId) 
      .then((data) => {
        return data
      })
      .catch(function (err) {
        console.log('Error in getJobpostId  catch() block', err);
        throw err
      })
  } catch (err) {
    console.log('Error in getJobpostId main catch() block', err);
    throw err
  }
}

async function addCandidateRecruitmentDetails(organizationDbConnection, hrappJobpostId, applicantDetails, lastInsertedId, resumeFileName, dataObject, trx) {
  try {
    candidateRecruitmentInfo = {
      Candidate_Id: lastInsertedId,
      Indeed_Apply_Id: indeedApplyId,
      Job_Post_Id: parseInt(hrappJobpostId),
      Resume: resumeFileName,
      Total_Experience_In_Years: applicantDetails?.personalDetails?.totalYearsOfExperience,
      Skill_Set: applicantDetails && applicantDetails.skills ? JSON.stringify(applicantDetails.skills.split(',')) : null,
      Source: "Indeed",
      Candidate_Status: 10,
      Sponsored: dataObject?.response?.analytics?.sponsored && typeof dataObject.response.analytics.sponsored == "boolean" ? 1: 0,
      Added_On: moment.utc().format('YYYY-MM-DD'),
      Added_By: 1
    }
    return organizationDbConnection('candidate_recruitment_info')
      .insert(candidateRecruitmentInfo)
      .transacting(trx)
      .then(async (data) => {
        console.log("my recruitment info data inserted:", data);
        return true
      })
      .catch(function (err) {
        throw err
      })
  } catch (err) {
    console.log('Error in addCandidateRecruitmentDetails main catch() block', err)
    throw err
  }
}

async function insertCandidateExperienceDetails(organizationDbConnection, applicantDetails, lastInsertedId, trx) {
  try {
    let candidateExperiences = applicantDetails?.positions?.values || []
    if (candidateExperiences && candidateExperiences.length > 0) {
      const candidateExperienceDetails = [];
      for (const field of candidateExperiences) {
        let startMonth = field?.startDateMonth;
        let startYear = field?.startDateYear;
        let endMonth = field?.endDateMonth;
        let endYear = field?.endDateYear;
        let startDate = convertToDate(startMonth, startYear);
        let endDate = convertToDate(endMonth, endYear);
        const entry = {
          Candidate_Id: lastInsertedId,
          Designation: field?.title,
          Prev_Company_Name: field?.company,
          Prev_Company_Location: field?.location,
          Start_Date: startDate? startDate: null,
          End_Date: endDate ? endDate : null,
        }
        candidateExperienceDetails.push(entry)
      }
      return organizationDbConnection('candidate_experience')
        .insert(candidateExperienceDetails)
        .transacting(trx)
        .then((data) => {
          console.log("My experience data inserted:", data);
          return true
        })
        .catch(function (err) {
          throw err
        })
    } else {
      return true
    }
  } catch (err) {
    console.log(
      'Error in insertCandidateExperienceDetails main catch() block',
      err
    )
    throw err
  }
}

async function addCandidateCertificationDetails(organizationDbConnection, applicantDetails, lastInsertedId, trx) {
  try {
    const certifications = applicantDetails?.certifications?.values || []
    let candidateCertifications = []

    if (certifications.length > 0) {
      for (const field of certifications) {
        let endMonth = field?.endDateMonth;
        let endYear = field?.endDateYear;
        let endDate = convertToDate(endMonth, endYear);
        const entry = {
          Candidate_Id: lastInsertedId,
          Certification_Name: field.title,
          Received_Date: endDate ? endDate : null,
        }
        candidateCertifications.push(entry)
      }
      return organizationDbConnection('candidate_certifications')
        .insert(candidateCertifications)
        .transacting(trx)
        .then(async (data) => {
          console.log("My certification inserted:", data);
          return true
        })
    } else {
      return true
    }
  } catch (err) {
    console.log(
      'Error in addCandidateCertificationDetails main catch() block',
      err
    )
    throw err
  }
}

async function insertEducationDetails(organizationDbConnection, applicantDetails, lastInsertedId, trx) {
  try {
    const education = applicantDetails?.educations?.values || [];
    let candidateEducation = []
    if (education.length > 0) {
      for (const field of education) {
        let startMonth = field?.startDateMonth;
        let startYear = field?.startDateYear;
        let endMonth = field?.endDateMonth;
        let endYear = field?.endDateYear;
        let startDate = convertToDate(startMonth, startYear);
        let endDate = convertToDate(endMonth, endYear);

        let specialisation = null;
        field?.degree + field?.field;
        if(field.degree){
          specialisation = field.degree;
        }
        if(field.field && specialisation){
          specialisation += field.field;
        }
        const entry = {
          Candidate_Id: lastInsertedId,
          Specialisation: specialisation,
          City: field?.location,
          University: field?.school,
          Start_Date: startDate ? startDate : null,
          End_Date: endDate ? endDate : null,
        }
        candidateEducation.push(entry)
      }
      return organizationDbConnection('candidate_education')
        .insert(candidateEducation)
        .transacting(trx)
        .then(async (data) => {
          console.log("My education data inserted:", data);
          return true
        })
        .catch(function (err) {
          console.log('Error inserting education details:', err)
          throw err
        })
    } else {
      return true
    }
  } catch (err) {
    console.log('Error in insertEducationDetails main catch() block', err)
    throw err
  }
}

async function addlanguages(organizationDbConnection, applicantDetails, lastInsertedId, trx) {
  try {
    /**insert candidate's language details */
    let languages = applicantDetails?.languages?.values || [];
    let candidateLanguages = [];
    if(languages && languages.length > 0){
      for (let language of languages){
        candidateLanguages.push(language.language);
      }
      let languageId = await getId(organizationDbConnection, candidateLanguages, "languages", "Lang_Id", "Language_Name");
      let languageData = [];
      for (const data of languageId) {
        const entry = {
          Candidate_Id: lastInsertedId,
          Lang_Known: data.Lang_Id
        }
        languageData.push(entry)
      }
      return organizationDbConnection('candidate_language')
      .insert(languageData)
      .transacting(trx)
      .then((data) => {
        console.log("My language data inserted:", data);
        return true
      })
      .catch(function (err) {
        console.log('Error in addlanguages  catch() block', err)
        throw err
      })
    } else{
      return true;
    }

  } catch (err) {
    console.log('Error in addlanguages main catch() block', err)
    throw err
  }
}

async function addScreenerQuestionsAndAnswers(organizationDbConnection, screenerQuestionsAndAnswers, indeedApplyId, trx) {
  try {
    /**insert screener questions and answer details */
    if(screenerQuestionsAndAnswers && screenerQuestionsAndAnswers.length > 0){
      const transformedData = screenerQuestionsAndAnswers.map(item => {
        let userData;
        // Check if the answer is an object with "label" and "value"
        if (typeof item.answer === 'object' && 'value' in item.answer) {
          // Convert the object to userData containing only the value
          userData = [item.answer.value];
        } else if (Array.isArray(item.answer) && item.answer.length > 0 && typeof item.answer[0] === 'object' && 'value' in item.answer[0]) {
          // Extract only the "value" properties from the array of objects
          userData = item.answer.map(answerItem => answerItem.value);
        } else if (typeof item.answer === 'string' && moment(item.answer, 'MM/DD/YYYY', true).isValid()) {
          // If the answer is a valid date string in MM/DD/YYYY format, convert it to YYYY-MM-DD using moment
          userData = [moment(item.answer, 'MM/DD/YYYY').format('YYYY-MM-DD')];
        } else {
          // Otherwise, convert 'answer' to 'userData' and handle undefined or empty values
          userData = item.answer ? (Array.isArray(item.answer) ? item.answer : [item.answer]) : [""];
        }

        // Change 'type' from 'multiselect' to 'checkbox-group'
        let type = item.question.type === 'multiselect' ? 'checkbox-group' : item.question.type;
        // If the format is 'decimal', change the type to 'number'
        if (item.question.format === 'decimal') {
          type = 'number';
        }
        // Return the transformed object
        const transformedObject = {
          name: item.question.id,
          type: type,
          label: item.question.question,
          ...('required' in item.question && { required: item.question.required }),
          ...('min' in item.question && { min: item.question.min }),
          ...('max' in item.question && { max: item.question.max }),
          ...('options' in item.question && { values: item.question.options }),
          userData: userData
        };
        // Add "className": "form-control" to all objects except when type is "checkbox-group"
        if (type !== 'checkbox-group') {
          transformedObject.className = "form-control";
        }
        return transformedObject;      
      });
      await organizationDbConnection('dynamic_form_responses')
      .insert({
        Task_Id: indeedApplyId,
        Response: JSON.stringify(transformedData),
        Added_On: moment.utc().format('YYYY-MM-DD'),
        Added_By: 1
      })
      .transacting(trx);

      return true;
    } else{
      return true;
    }

  } catch (err) {
    console.log('Error in addScreenerQuestionsAndAnswers main catch() block', err)
    throw err
  }
}

async function getId(organizationDbConnection, candidateLanguages, tableName, idName, conditionCol) {
  try {
    return organizationDbConnection(tableName)
      .select(idName)
      .whereIn(conditionCol, candidateLanguages) 
      .then((data) => {
        return data
      })
      .catch(function (err) {
        console.log('Error in addCandidateCareerInfo  catch() block', err)
        throw err
      })
  } catch (err) {
    console.log('Error in addCandidateCareerInfo main catch() block', err)
    throw err
  }
}

async function getAuthCredentialsForIndeed() {
  try {
    // Define the request URL
    const url = "https://apis.indeed.com/oauth/v2/tokens";
    const AWS = require('aws-sdk');
        
    // Create client for secrets manager
    let client = new AWS.SecretsManager({
        region: process.env.region
    });
    // Get secrets from aws secrets manager
    let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
    secretKeys = JSON.parse(secretKeys.SecretString);
    let indeedClientid = secretKeys.indeed_clientid;
    let indeedClientsecret = secretKeys.indeed_clientsecret;
    // Define the request headers
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded",
      Accept: "application/json",
    };
    // Define the request body
    const data = new URLSearchParams({
      scope: "employer_access",
      client_id: indeedClientid,
      client_secret: indeedClientsecret,
      grant_type: "client_credentials",
    });
    try {
      // Make the POST request using Axios
      const response = await axios.post(url, data, { headers });
      let authTokens = response.data;
      return authTokens?.access_token;
    } catch (error) {
      console.log('Error in getAuthCredentialsForIndeed catch block', error);
      throw 'SET0012';
    }
  } catch (err) {
    console.log('Error in getAuthCredentialsForIndeed main catch block', err)
    throw err
  }
}

async function indeedDispositionSyncAPI(indeedApplyId) {
  try {
    const formattedDate = moment().utc().format('YYYY-MM-DDTHH:mm:ss.SSZ');
    let indeedAccessToken = await getAuthCredentialsForIndeed();
    indeedAccessToken = "Bearer " + indeedAccessToken;
    const indeedData = {
      // "jobpostId": args.Job_Post_Id,
      "input": {
      "dispositions": [{
          "dispositionStatus": "NEW",
          "rawDispositionStatus": "New",
          "rawDispositionDetails": "",
          "identifiedBy": {
          "indeedApplyID": indeedApplyId,
          "ittk": null
          },
          "atsName": process.env.atsNameForIndeed,
          "statusChangeDateTime": formattedDate
      }]
      }
    };
    const indeedUrl = "https://apis.indeed.com/graphql";
    query = `mutation Send($input : SendPartnerDispositionInput !) {
      partnerDisposition {
        send(input : $input) {
          numberGoodDispositions 
          failedDispositions {
            identifiedBy {
              indeedApplyID 
              ittk 
              alternateIdentifier {
                jobIdentifier {
                  indeedJobKey 
                  atsApplicationIdentifier{
                    requisitionId 
                    companyName
                  }
                }
                jobSeekerIdentifier 
                  { 
                    indeedJobSeekerKey 
                    emailAddress 
                  }
              }
            }
            rationale
          }
        }
      }
    }
    `,
    variables = indeedData.input;
    let config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: indeedUrl,
    headers: { 
        'Content-Type': 'application/json', 
        'Authorization': indeedAccessToken, 
    },
    data: JSON.stringify({
        query: query,
        variables: {
        input: indeedData.input
        }
      })
    };

    try {
        console.log("Disposition api payload:", indeedData.input);
        // Make the request using Axios
        let response = await Promise.resolve(axios.request(config));
        const responseMessage = JSON.stringify(response.data);
        console.log("My disposition api response:", responseMessage);
        return true;
      } catch (error) {
        console.log('Error in indeedDispositionSyncAPI catch block', error);
        throw 'SET0018';
      }
  } catch (err) {
    console.log('Error in indeedDispositionSyncAPI main catch block', err)
    throw err
  }
}


function convertToDate(startDateMonth, startDateYear) {
  if(startDateMonth && startDateYear){
    // Default the day to '01'
    const day = '01';
    // Construct the date string in 'YYYY-MM-DD' format
    const formattedDate = `${startDateYear}-${startDateMonth}-${day}`;
    return formattedDate;
  }
  return null;
}

