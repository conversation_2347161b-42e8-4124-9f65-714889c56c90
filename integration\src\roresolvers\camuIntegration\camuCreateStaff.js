//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const { formName } = require('../../../common/appConstants');

//Require commonfunctions
const { getOrgDetails, camuConversion, updateCamuIdInEmpJob, updateEmployeeInfoTimestampLog, callCreateStaffApi } = require('../../common/commonFunction');

//List the work schedule details in the work schedule form
module.exports.camuCreateStaff = async (parent, args, context, info) => {
    console.log('Inside camuCreateStaff function.');
    let organizationDbConnection;
    let appmanagerDbConnection;
    let errResult;
    let orgCode;
    let input;
    let employeeIdToBeProcessed = [];
    let openRecordsToBeProcessed = [];
    let failedRecordsToBeProcessed = [];
    let camuToken;
    let camuBaseUrl;
    let instituteCode;
    let missingFields = []
    try{
        orgCode=context.Org_Code;
        
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // get the appmanager database connections
        appmanagerDbConnection = knex(context.connection.AppManagerDb);
        /** If the employeeId not exists in the input then process for the failed records */
        if(!args.employeeId || !args.employeeId.length){
            let logInEmpId = context.Employee_Id;
            
            // Check access rights exist for employee or not
            let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, logInEmpId, formName.userAccounts, '', 'UI');   
            if (Object.keys(checkRights).length === 0 || checkRights.Role_Update === 0 || checkRights.Employee_Role.toLowerCase() !== 'admin') {
                // throw error if view rights is not exists
                throw ('_DB0102');
            }
            let failedRecords = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.employeeInfoTimestampLog,"Camu_Push_Status","Failed","Action","Add");
            let openRecords = await commonLib.func.getDataFromTableAccordingToStatus(organizationDbConnection,ehrTables.employeeInfoTimestampLog,"Camu_Push_Status","Open","Action","Add");
            if(failedRecords.length > 0){
                //Get the list of User_Defined_EmpId
                failedRecordsToBeProcessed = failedRecords.map((el) => {
                    return el.Employee_Id;
                });
            }
            if(openRecords.length > 0){
                //Get the list of User_Defined_EmpId
                openRecordsToBeProcessed = openRecords.map((el) => {
                    return el.Employee_Id;
                });
            }
            const mergedArray = failedRecordsToBeProcessed.concat(openRecordsToBeProcessed);

            // Remove duplicates using a Set
            employeeIdToBeProcessed = [...new Set(mergedArray)];

            if(employeeIdToBeProcessed.length == 0) {
                throw("CDG0170");
            }
        } else{
            employeeIdToBeProcessed = employeeIdToBeProcessed.concat(args.employeeId);
        }

        let [staffDetailsEmpJob,orgDetails,staffDetailsEmpPersonalInfo,staffContactDetails,staffDependentDetails,staffQulificationDetails] = await Promise.all([getEmpJobDetails(organizationDbConnection,employeeIdToBeProcessed),getOrgDetails(organizationDbConnection,orgCode), getEmpPersonalInfoDetails(organizationDbConnection,employeeIdToBeProcessed),getContactDetails(organizationDbConnection,employeeIdToBeProcessed),getDependentDetails(organizationDbConnection,employeeIdToBeProcessed),getEductionDetails(organizationDbConnection,employeeIdToBeProcessed)]);
        
        if(staffDetailsEmpJob['error'] || orgDetails['error'] || staffDetailsEmpPersonalInfo['error'] || staffContactDetails['error'] || staffDependentDetails['error'] || staffQulificationDetails['error'])
        {
            console.log("get error while retrieving the employee details");
            throw("EI00101");
        }

        let camuDetails=await commonLib.employees.getCamuClientToken(appmanagerDbConnection,orgCode,organizationDbConnection,employeeIdToBeProcessed);
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;

        if(camuDetails && Object.keys(camuDetails).length>0)
        {
            if(camuDetails.fieldForceEnabled !== 1){
                camuToken=camuDetails['empCamuDetails']['Client_Access_Token'];
                instituteCode = camuDetails['empCamuDetails']['Institution_Code'];
                camuBaseUrl = camuDetails['empCamuDetails']['Camu_Base_Url'];
            }
        }
        else{
            throw("EI00103");
        }
        let camuSyncCreateStaffEndPoint=camuBaseUrl+process.env.camuCreateStaffEndPoint;

        
        let dummyEmail = "";
        let updateDummyEmailStatus = true;
        let successCount = 0;
        
        for (const id of employeeIdToBeProcessed) {
            if(camuDetails.fieldForceEnabled == 1){
                camuToken='';
                camuSyncCreateStaffEndPoint='';
                instituteCode='';
                camuBaseUrl = '';
                let employeeCamuDetails = camuDetails['empCamuDetails'][id];
                
                if(employeeCamuDetails && employeeCamuDetails.length){
                    camuToken = employeeCamuDetails[0]['Client_Access_Token'];
                    instituteCode = employeeCamuDetails[0]['Institution_Code'];
                    camuBaseUrl = employeeCamuDetails[0]['Camu_Base_Url'];
                    camuSyncCreateStaffEndPoint=camuBaseUrl+process.env.camuCreateStaffEndPoint;
                }
            }
            //Validate if camu
            if (!camuToken || !camuToken.length) {
                missingFields.push("CAMU token");
            }
            if (!camuBaseUrl || !camuBaseUrl.length) {
                missingFields.push("CAMU base URL");
            }
            if (!instituteCode || !instituteCode.length) {
                missingFields.push("Institute code");
            }
            if (!camuSyncCreateStaffEndPoint || !camuSyncCreateStaffEndPoint.length) {
                missingFields.push("CAMU endpoint");
            }

            if(missingFields && missingFields.length){
                throw("EI00103");
            }

            // console.log("EID:",id," camuSyncCreateStaffEndPoint",camuSyncCreateStaffEndPoint," camuToken",camuToken," instituteCode",instituteCode," camuBaseUrl",camuBaseUrl);
            const personalInfo = staffDetailsEmpPersonalInfo.find((details) => details.Employee_Id === id);
            const jobDetails = staffDetailsEmpJob.find((details) => details.Employee_Id === id);
            const contactDetails = staffContactDetails.find((details) => details.Employee_Id === id);
            const dependentDetails = staffDependentDetails.find((details) => details.Employee_Id === id);
            const qualificationDetails = staffQulificationDetails.find((details) => details.Employee_Id === id);

            input = { ...personalInfo, ...jobDetails, ...contactDetails, ...dependentDetails, ...qualificationDetails };
            // console.log("input:",input);

            delete input.Employee_Id;
            input['instituteCode']=instituteCode;
            input = await camuConversion(input);
            // console.log("after conversion input:",input);
            if(!input['email'] && staffDetailsEmpPersonalInfo['email'])
            {
                input['email']=staffDetailsEmpPersonalInfo['email'];
                updateDummyEmailStatus = true;
            }
            if(!input['email'] && input['staffID'])
            {  
                dummyEmail=input['staffID']+'@'+orgCode+'.com';
                input['email']=dummyEmail;
                updateDummyEmailStatus= true;
            }
            if(updateDummyEmailStatus){
                let {camuId, error} = await callCreateStaffApi(input,camuSyncCreateStaffEndPoint,camuToken);
                if(camuId.length>0)
                {
                    let [statusUpdate,camuIdUpdate] = await Promise.all([updateEmployeeInfoTimestampLog(organizationDbConnection,id,"Add","Success",null),updateCamuIdInEmpJob(organizationDbConnection,id,camuId)])
                    if(statusUpdate && camuIdUpdate){
                        successCount++;
                    }
                } else {
                    //Update the messages based on the error
                    switch(error.toLowerCase()){
                        case 'duplicate staff found':
                            error = 'The Staff ID configured in HRAPP is already associated with another employee in CAMU.';
                            break;
                        case 'duplicate email found':
                            error = 'Employee Email ID configured in HRAPP is already associated with another employee in CAMU.'
                            break;
                        case 'invalid reporting staff':
                            error = "Missing Manager data in CAMU or a mismatch in the associated Manager's staff ID in CAMU.";
                            break;
                        default:
                            error = error;
                            break;
                    }
                    await updateEmployeeInfoTimestampLog(organizationDbConnection,id,"Add","Failed",error);
                }
            }
        }
        if(successCount === employeeIdToBeProcessed.length){
            return { errorCode:'',message:'Staff Created in Camu Successfully'};
        } else {
            let errorCode = successCount == 0 ? "CDG0168" : "CDG0169";
            throw(errorCode);
        }
    }catch(camuSyncCreateStaffDetailsMainCatchErr) {
        appmanagerDbConnection?appmanagerDbConnection.destroy():null;
        console.log('Error in the camuCreateStaff() function main catch block. ',camuSyncCreateStaffDetailsMainCatchErr);
        // destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(camuSyncCreateStaffDetailsMainCatchErr, 'CDG0122');

        if(camuSyncCreateStaffDetailsMainCatchErr === 'EI00103' && missingFields.length){
            errResult.message = "Missing or empty fields: " + missingFields.join(', ');
        }
        // return error response
        throw new ApolloError(errResult.message,errResult.code);
    }
};

async function getEmpJobDetails(organizationDbConnection,employeeIdToBeProcessed)
{
    try{
        return(
        organizationDbConnection(ehrTables.empJob)
        .select('EJ.Employee_Id as Employee_Id','EJ.User_Defined_EmpId as staffID','EJ.Date_Of_Join as DOJ','M.User_Defined_EmpId as reportingToStaffId','ET.Employee_Type as staffCategory','D.Designation_Name as designation','EJ.Previous_Employee_Experience as yearsOfExperience','EJ.Emp_Email as email')
        .from(ehrTables.empJob + " as EJ")
        .leftJoin(ehrTables.employeeType + " as ET",'EJ.EmpType_Id','ET.EmpType_Id')
        .leftJoin(ehrTables.designation +' as D','EJ.Designation_Id','D.Designation_Id')
        .leftJoin(ehrTables.empJob+' as M','EJ.Manager_Id','M.Employee_Id')
        .whereIn('EJ.Employee_Id',employeeIdToBeProcessed)
        .then(data=>{
            if(data.length>0)
            {
                return data;
            }
            else{
                return [];
            }
           
        })
        .catch(e=>{
            console.log("Error while getting staff details from emp_job",e);
            return {error:e};
        })
        )
    }
    catch(e){
        console.log("Error while getting staff details from emp_job main catch block",e);
        return {error:e};
    }
}


async function getEmpPersonalInfoDetails(organizationDbConnection,employeeIdToBeProcessed)
{
    try{
        return(
            organizationDbConnection(ehrTables.empPersonalInfo)
            .select(organizationDbConnection.raw( "CONCAT_WS(' ',Emp_First_Name,Emp_Middle_Name) as firstName"),
            organizationDbConnection.raw('CASE WHEN Work_Email IS NOT NULL AND Work_Email !="" THEN Work_Email ELSE Personal_Email END as email'),
            'Employee_Id','Emp_Last_Name as lastName','DOB','Gender as gender','Place_Of_Birth as placeOfBirth',
            'Blood_Group as bloodGroup','Marital_Status as maritalStatus','PAN as panNumber','Aadhaar_Card_Number as aadharNumber',
            'Caste as communityDetails','Nationality as citizenOf','Salutation as title')
            .where('Employee_Id','in',employeeIdToBeProcessed)
            .then(data=>{
                if(data.length>0)
                {   
                    return data;
                }
                return[];
                
            })
            .catch(e=>{
                console.log("Error Occured While getting data from EmpPersonalInfo",e);
                return {error:e};
            })
        )
    }
    catch(e){
        console.log("Error Occured While getting data from EmpPersonalInfo from main catch block",e);
        return {error:e};
    }
}

async function getContactDetails(organizationDbConnection,employeeIdToBeProcessed)
{
    try{
        return(
            organizationDbConnection(ehrTables.contactDetails)
            .select('Employee_Id','Mobile_No as mobileNumber')
            .where('Employee_Id','in',employeeIdToBeProcessed)
            .then(data=>{
                if(data.length>0)
                {
                    return data;
                }
                return[];
            })
            .catch(e=>{
                console.log("Error while getting data from contact details",e);
                return {error:e};
            })
        )
    }
    catch(e){
        console.log("Error while getting data from contact details from main catch block",e);
        return {error:e};
    }
}

async function getDependentDetails(organizationDbConnection,employeeIdToBeProcessed)
{
    try{
        return(
            organizationDbConnection(ehrTables.empDependent)
            .select(organizationDbConnection.raw( "CONCAT_WS(' ',Dependent_First_Name,Dependent_Last_Name) as fatherOrSpouseName"), 'Employee_Id')
            .where('Employee_Id','in',employeeIdToBeProcessed)
            .whereIn('Relationship',['Father','Spouse'])
            .groupBy('Employee_Id')
            .then(data=>{
                if(data.length>0)
                {
                    return data;
                }
                return [];
            })
            .catch(e=>{
                console.log("Error While getting dependent deatails",e);
                return {error:e};
            })
        )
    }
    catch(e){
        console.log("Error While getting dependent deatails from main catch block",e);
        return {error:e};
    }
    
}


async function getEductionDetails(organizationDbConnection,employeeIdToBeProcessed)
{
    try{
        // SELECT Employee_Id, GROUP_CONCAT(Specialisation SEPARATOR ',') AS qualification FROM emp_education GROUP BY Employee_Id;

        return(
        organizationDbConnection(ehrTables.empEducation)
        .select('Employee_Id',organizationDbConnection.raw("GROUP_CONCAT(Specialisation SEPARATOR ',') AS qualification"))
        .where('Employee_Id','in',employeeIdToBeProcessed)
        .groupBy('Employee_Id')
        .then(data=>{
            return data;
        })
        .catch(e=>{
            console.log("Error while getting education details",e);
            return {error:e};
        })
        )
    }
    catch(e)
    {
        console.log("error while getting education details main catch block",e);
        return {error:e};
    }
}