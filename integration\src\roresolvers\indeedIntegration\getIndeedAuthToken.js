// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const {formId} = require('../../../common/appConstants');
const axios = require('axios');
const { getDecryptedString } = require('../../common/commonFunction');



module.exports.getIndeedAuthToken = async (parent, args, context, info) => {
  let organizationDbConnection;
    try {
      organizationDbConnection = knex(context.connection.OrganizationDb);
      let loginEmployeeId = context.Employee_Id;
      let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', "UI", false,  formId.jobpost);
      if(Object.keys(checkRightsForForm).length > 0  && ((checkRightsForForm.Role_Add === 1) || (checkRightsForForm.Role_Update === 1))){
        // Define the request URL
        const url = process.env.indeedAuthTokenAPI;
        const AWS = require('aws-sdk');
            
        // Create client for secrets manager
        let client = new AWS.SecretsManager({
            region: process.env.region
        });
        // Get secrets from aws secrets manager
        let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
        secretKeys = JSON.parse(secretKeys.SecretString);
        let indeedClientid = secretKeys.indeed_clientid;
        let indeedClientsecret = secretKeys.indeed_clientsecret;
        let sourceNameObject = await getSourceName(organizationDbConnection);
        let sourceName = sourceNameObject?.sourceName
        // Define the request headers
        const headers = {
          "Content-Type": "application/x-www-form-urlencoded",
          Accept: "application/json",
        };
        // Define the request body
        const data = new URLSearchParams({
          scope: args.scope,
          client_id: indeedClientid,
          client_secret: indeedClientsecret,
          grant_type: args.grantType,
        });

        try {
          // Make the POST request using Axios
          const response = await axios.post(url, data, { headers });
          let authTokens = JSON.stringify(response.data);
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null;
          return{ message: "Indeed auth tokens retrieved successfully.", data:authTokens, sourceName: sourceName};
        } catch (error) {
          //Destroy DB connection
          organizationDbConnection ? organizationDbConnection.destroy() : null;
          if (error.response) {
            // The request was made and the server responded with a status code
            console.error('Response Error:', error.response.data);
            console.error('Status Code:', error.response.status);
          } else if (error.request) {
            // The request was made but no response was received
            console.error('Request Error:', error.request);
          } else {
            // Something else happened in setting up the request
            console.error('Error:', error.message);
          }
          throw 'SET0012';
        }
      }
      else {
        console.log('This employee do not have add or edit access rights');
        throw '_DB0111';
      }
    }
    catch (e) {
        //Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        console.log('Error in getIndeedAuthToken function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0012');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

const getSourceName = (organizationDbConnection) =>{
  try {
    return(
      organizationDbConnection('recruitment_integration')
      .select('Indeed_Source_Name as sourceName')
      .where('Integration_Type', 'indeed')
      .first()
      .then(data =>{                
          // return response
          return data
      })
    )
  } catch (error) {
    console.log("Error in getSourceName function catch block", error);
    throw error;
  }
};