// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appConstants');



module.exports.getAWSCognitoIdentities = async (parent, args, context, info) => {
    try {
        // let secretName=process.env.irukkaSecret;
        const AWS = require('aws-sdk');
        
        // Create client for secrets manager
        let client = new AWS.SecretsManager({
            region: process.env.region
        });
        // Get secrets from aws secrets manager
        let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
        secretKeys = JSON.parse(secretKeys.SecretString);
        const type = args.Type?.toLowerCase() || 'irukka';

        let response;

        // Check the type and process accordingly
        if (type === 'microsoft calendar') {
            response = {workwiselymsapplicationID:secretKeys.workwiselymsapplicationID,
                workwiselymstenantID: secretKeys.workwiselymstenantID 
            } // Provide actual JSON or logic here
        } else {
            const irukkaSecret = JSON.parse(secretKeys.irukka);
            response = JSON.parse(irukkaSecret);
        }
        
        return{ message: "Configuration details retrieved successfully", data:response}
        
    }
    catch (e) {
        //Destroy DB connection
        console.log('Error in getAWSCognitoIdentities function main catch block.', e);
        let errResult = commonLib.func.getError(e, 'SET0007');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
