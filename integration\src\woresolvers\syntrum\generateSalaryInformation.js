
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { generateSyntrumSalary } = require('./generateSyntrumSalary');
const { ehrTables } = require('../../../common/tablealias');
const { syntrumValues } = require('../../../common/appConstants');

module.exports.generateSalaryInformation = async (parent, args, context, info) => {
    console.log('Inside getSyntrumSalaryInformation function started');
    let organizationDbConnection;

    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        const [externalApiSyncDetail, employeeJob] = await Promise.all([
            organizationDbConnection(ehrTables.externalApiSyncDetails).first()
            .where({  Integration_Type: syntrumValues.integrationType, Sync_Direction: 'Pull', 
            Action: 'Retrieve',  Entity_Type: 'Refetch-Salary', Status:'Active' }),

            organizationDbConnection(ehrTables.empJob + ' as EJ').select('EJ.User_Defined_EmpId', 'HSD.Salary_Payslip')
            .leftJoin(ehrTables.hmcEmpSalaryPayslip + ' as HSD', 'EJ.User_Defined_EmpId', 'HSD.Employee_Code')
            .where('EJ.Employee_Id', context.Employee_Id).first()
        ]);  
        
        if(!externalApiSyncDetail && employeeJob?.Salary_Payslip){
            return { errorCode:'', message: 'Syntrum salary information retrieved successfully', data: employeeJob.Salary_Payslip};
        }

        let response = await generateSyntrumSalary(organizationDbConnection, context);
        const salaryDetails = JSON.parse(response.data);

        const status = salaryDetails?.result?.data?.json?.Status;
        const remarksData = salaryDetails?.result?.data?.json?.remarks?.data;
        if (!status || !Array.isArray(remarksData) || remarksData.length === 0) {
            const errorCodes = {
                'EMPLOYEE-NOT-FOUND': 'SYN0028'
            };
            throw salaryDetails?.result?.data?.json?.remarks?.code === "EMPLOYEE-NOT-FOUND" ? "SYN0028" : 'SYN0029';
        }
        
        await organizationDbConnection(ehrTables.hmcEmpSalaryPayslip).
        insert({Employee_Code: employeeJob.User_Defined_EmpId, Salary_Payslip: JSON.stringify(salaryDetails) })
        .onConflict(['Employee_Code'])
        .merge();

        return response;


    } catch (e) {
        console.error('Error in getSyntrumSalaryInformation function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}