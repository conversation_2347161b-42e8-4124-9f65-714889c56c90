// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
const axios = require('axios');
//Require moment
const moment = require('moment-timezone');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const { validatCommonRuleInput } = require('../../common/commonFunction');
module.exports.addUpdateNewPosition = async (parent, args, context) => {
  let validationError = {};
  try {
    const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
  let organizationDbConnection = knex(context.connection.OrganizationDb);
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId.newPosition
    );
    if (Object.keys(checkRights).length <= 0 || (args.positionRequestId && checkRights.Role_Update === 0) || (!args.positionRequestId && checkRights.Role_Add === 0)) {
     throw !args.positionRequestId && checkRights.Role_Add === 0 ? '_DB0101' : '_DB0102';
    }
    const {
      positionRequestId,
      originalPositionId,
      positionCode,
      positionTitle,
      groupName,
      groupCode,
      divisionCode,
      divisionName,
      departmentCode,
      departmentName,
      sectionCode,
      sectionName,
      costCenter,
      employeeType,
      employeeTypeName,
      noOfPosition,
      licenseCertificate,
      PositionLevel,
      workflowId,
      status,
      reasonForReplacement,
      groupId,
      divisionId,
      departmentId,
      sectionId,
      organizationStructureId
    } = args;
      if( args.noOfPosition && +args.noOfPosition > 500){
        validationError['IVE0488'] = commonLib.func.getError('IVE0488', '').message;
        throw 'IVE0000';
      }
      const fieldValidations = {
      }
      if (positionTitle && status==='Draft') {
        fieldValidations.positionTitle = 'IVE0497'
      }

      if(reasonForReplacement){
        fieldValidations.reasonForReplacement='IVE0507'
      }
      validationError = await validatCommonRuleInput(
        args,
        fieldValidations
      )
      if (Object.keys(validationError).length > 0) {
        throw 'IVE0000'
      }
      if(!originalPositionId){
      const isDuplicate = await checkDuplicatePositionTitle(organizationDbConnection, positionTitle, positionRequestId);
      if (isDuplicate) {
        throw 'EI00203'
      }
    }
    const mppNewRequestData = {
      Position_Request_Id: positionRequestId,
      Position_Title:positionTitle || null,
      Original_Position_Id: originalPositionId,
      Organization_Structure_Id: organizationStructureId || null,
      Group_Code: groupCode || null,
      Division_Code: divisionCode || null,
      Department_Code: departmentCode || null,
      Section_Code: sectionCode || null,
      Cost_Center: costCenter || null,
      Employee_Type: employeeType || null,
      No_Of_Position: noOfPosition,
      Workflow_Id: workflowId || null,
      Status: status,
      License_Certificate: licenseCertificate || null,
      Position_Level:PositionLevel || null,
      Reason_For_Replacement:reasonForReplacement || null,
      Custom_Group_Id: args.customGroupId || null,
      Request_Type: originalPositionId ? 'ADDPOS' : 'NEWPOS'
    };

    const instanceData = {
      originalPositionId: originalPositionId || '',
      organizationStructureId: organizationStructureId || '',
      positionCode: positionCode || '',
      positionTitle: positionTitle || '',
      groupName: groupName || '',
      groupCode: groupCode || '',
      divisionCode: divisionCode || '',
      divisionName: divisionName || '',
      departmentCode: departmentCode || '',
      departmentName: departmentName || '',
      sectionCode: sectionCode || '',
      sectionName: sectionName || '',
      costCenter: costCenter || '',
      Approval_Status:status || '',
      employeeType: employeeType || '',
      employeeTypeName: employeeTypeName || '',
      noOfPosition: noOfPosition || 0,
      customGroupId: args.customGroupId || '',
      requestType: originalPositionId ? 'ADDPOS' : 'NEWPOS'
    };
    let responseObject={};
    let newPosition;
    if(positionRequestId){
    responseObject = await getWorkflowProcessInstanceId(organizationDbConnection, positionRequestId);
    mppNewRequestData.Updated_On= moment().utc().format('YYYY-MM-DD HH:mm:ss');
    mppNewRequestData.Updated_By=loginEmployeeId;
    instanceData.Updated_On= moment().utc().format('YYYY-MM-DD HH:mm:ss');
    instanceData.Updated_By=loginEmployeeId;
    }
    else{
      mppNewRequestData.Added_On= moment().utc().format('YYYY-MM-DD HH:mm:ss');
      mppNewRequestData.Added_By=loginEmployeeId;
    }
    return await organizationDbConnection.transaction(async (trx) => {   
      if (!originalPositionId && !positionRequestId && !organizationStructureId) {
        const existingPosName = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as SFWP')
          .transacting(trx)
          .where('Pos_Name', positionTitle)
          .first();
        if (existingPosName) {
          throw 'EI00208';
        }
        // Construct `Parent_Path`
        let parentPath = '0';
        if (groupId) parentPath += `,${groupId}`;
        if (divisionId) parentPath += `,${divisionId}`;
        if (departmentId) parentPath += `,${departmentId}`;
        if (sectionId) parentPath += `,${sectionId}`;
    
        // Determine `Parent_Id` (highest level code available)
        const parentId = sectionId || departmentId || divisionId || groupId || 0;
    
        // Prepare data for insertion
        const newOrgStructureData = {
          Pos_Name: positionTitle,
          Cost_Code: costCenter,
          Parent_Path: parentPath,
          Parent_Id: parentId,
          Position_Level: PositionLevel,
          Status:'Draft'
        };
    
        // Insert new entry into SFWPOrganizationStructure
        let insertData=await organizationDbConnection(ehrTables.SFWPOrganizationStructure)
        .transacting(trx)
          .insert(newOrgStructureData)
          .catch(err => {
            console.error('Error inserting into SFWPOrganizationStructure', err);
            throw err;
          });
          instanceData.organizationStructureId = insertData[0];
          mppNewRequestData.Organization_Structure_Id=insertData[0];

          let jobDescriptionData={
            Organization_Structure_Id: insertData[0]
          }

          await organizationDbConnection(ehrTables.sfwpJobDescriptionMaster)
          .transacting(trx).insert(jobDescriptionData);
      }
      newPosition  =
        positionRequestId !== 0
          ? await organizationDbConnection(ehrTables.mppPositionRequest).transacting(trx).where('Position_Request_Id', positionRequestId).update(mppNewRequestData)
          : await organizationDbConnection(ehrTables.mppPositionRequest).transacting(trx).insert(mppNewRequestData);
      const updateResult = await newPosition;
      if (!updateResult) throw 'EI00179';
      instanceData.positionRequestId = positionRequestId || newPosition[0];
      if(workflowId && args.eventId &&  status.toLowerCase()==="waiting for approval"){
      await initaiteWorkflow(args.eventId, instanceData, orgCode, formId.newPosition, organizationDbConnection,loginEmployeeId,trx);
      if (positionRequestId) {
        if (responseObject && responseObject[0]?.Process_Instance_Id) {
          await commonLib.func.deleteOldApprovalRecords(organizationDbConnection, responseObject[0].Process_Instance_Id, trx);
        }
      }
    }
    }).then(async (result) => {
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `New Position & Additional Headcount ${ positionCode
          +positionTitle} ${args.positionRequestId ? 'updated' : 'added'} successfully`,
      });
      if (organizationDbConnection) organizationDbConnection.destroy();
      return { errorCode: '', message: `New Position & Additional Headcount request ${args.positionRequestId ? 'updated' : 'added'} successfully`,positionRequestId:positionRequestId || newPosition[0] };
    }).catch((error) => {
      throw error;
    });
  } catch (error) {
    if (error === 'IVE0000') {
      console.log('Validation error in addUpdateNewPosition function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      const errResult = commonLib.func.getError(error, 'EI00184');
      throw new ApolloError(errResult.message, errResult.code);
    }
  }
};
async function getWorkflowProcessInstanceId(organizationDbConnection, uniqueId) {
  try {
    return await organizationDbConnection(ehrTables.mppPositionRequest)
      .select('Process_Instance_Id')
      .where('Position_Request_Id', uniqueId);
  } catch (err) {
    console.log('Error in getWorkflowProcessInstanceId', err);
    throw err;
  }
}
async function initaiteWorkflow(eventId, instanceData, orgCode, formId, organizationDbConnection,loginEmployeeId,trx) {
  try {
    instanceData.formId = formId
    instanceData.Employee_Id=loginEmployeeId
    instanceData.Added_By=loginEmployeeId
    instanceData.Added_On=moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const config = {
      method: 'post',
      url: `https://${process.env.customDomainName}/workflowEngine/workflow/initiate`,
      data: { event_id: eventId, instance_data: instanceData },
      headers: {
        'Content-Type': 'application/json',
        org_code: orgCode,
        employee_id: loginEmployeeId,
        db_prefix: process.env.dbPrefix,
      },
    };
    const response = await axios.request(config);
    if (response.status === 200 && response.data?.workflowProcessInstanceId) {
      await updateWorkflowProcessInstanceId(organizationDbConnection, instanceData.positionRequestId, response.data.workflowProcessInstanceId,trx);
    } else {
      console.log('Error in the initaiteWorkflow endpoint.', response);
      throw 'ATS0009';
    }
  } catch (mainError) {
    console.log('Error in initaiteWorkflow', mainError);
    throw 'ATS0009';
  }
}
async function updateWorkflowProcessInstanceId(organizationDbConnection, uniqueId, workflowProcessInstanceId,trx) {
  try {
    return await organizationDbConnection(ehrTables.mppPositionRequest)
      .where('Position_Request_Id', uniqueId)
      .transacting(trx)
      .update({ Process_Instance_Id: workflowProcessInstanceId });
  } catch (err) {
    console.log('Error in updateWorkflowProcessInstanceId', err);
    throw err;
  }
}

async function checkDuplicatePositionTitle(organizationDbConnection, positionTitle, positionRequestId = null) {
  try {
    let query = organizationDbConnection(ehrTables.mppPositionRequest)
  .select('Position_Request_Id', 'Position_Title')
  .where(function() {
    this.whereNull('Original_Position_Id').orWhere('Original_Position_Id', '');
  })
  .andWhere('Position_Title', positionTitle)
  .andWhere('Status', '!=', 'Rejected');
    if (positionRequestId) {
      query = query.andWhere('Position_Request_Id', '!=', positionRequestId);
    }

    const result = await query;
    return result.length > 0;
  } catch (err) {
    console.log('Error in checkDuplicatePositionTitle', err);
    throw err;
  }
}