
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants')

module.exports.retrievePositionJobSummary = async (parent, args, context, info) => {

    console.log("Inside retrievePositionJobSummary function.")
    let organizationDbConnection;

    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, args.formId);
        
        if(Object.entries(checkRights).length > 0 && checkRights.Role_View === 1){
            let vacancyAvailable;
        if(args.source && args.source.toLowerCase()==='recruitment' && args.mppPositionType && args.positionRequestId){
            vacancyAvailable=await checkVacancyAvailablity(organizationDbConnection,args);
        }
            let jobSummary =  await  organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as SFWP')
            .select('JS.Job_Description', 'SFWP.Company_Id', 'JS.Career_Band', 'JS.Job_Summary', 'JS.Skills_Competencies', 'JS.Education',
              'JS.Experience', 'JS.Working_Conditions', 'JS.Working_Relationship_Internal', 'JS.Working_Relationship_External', 'JS.Minimum_Requirements'
            )
            .leftJoin(ehrTables.sfwpJobDescriptionMaster + ' as JS', 'JS.Organization_Structure_Id', 'SFWP.Organization_Structure_Id')
            .where('SFWP.Organization_Structure_Id', args.originalPositionId).first();

            if(jobSummary){
              jobSummary.Working_Relationship_Internal = jobSummary.Working_Relationship_Internal ? JSON.parse(jobSummary.Working_Relationship_Internal) :  [];
              jobSummary.Working_Relationship_External = jobSummary.Working_Relationship_External ? JSON.parse(jobSummary.Working_Relationship_External) :  [];
            }
            
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Position job summary retrieved successfully.", summary: jobSummary,vacancyAvailable:vacancyAvailable};

        } else {
            throw '_DB0100';
        }

    }catch(err){
        //Destroy DB connection
        console.error('Error in retrievePositionJobSummary function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function checkVacancyAvailablity(organizationDbConnection, args) {
    try {
      let table = args.mppPositionType === 'New Position' ? 'mpp_position_request' : 'mpp_recruitment_request';

      const jobPostData = await organizationDbConnection('job_post as JP')
        .leftJoin('candidate_recruitment_info as CR', 'JP.Job_Post_Id', 'CR.Job_Post_Id')
        .modify(function (queryBuilder) {
          if (args.mppPositionType === 'New Position') {
            queryBuilder.innerJoin(table + ' as MPP', 'MPP.Position_Request_Id', 'JP.Position_Request_Id');
          } else {
            queryBuilder.innerJoin(table + ' as MPP', 'MPP.Recruitment_Id', 'JP.Position_Request_Id');
          }
        })
        .where('JP.Position_Request_Id', args.positionRequestId)
        .andWhere(function () {
          this.where('JP.Status', 5)
              .orWhere(function () {
                this.where('JP.Status', 6)
                    .whereNotNull('CR.Candidate_Id');
              });
        })
        .select(
          organizationDbConnection.raw('COALESCE(SUM(DISTINCT JP.No_Of_Vacancies), 0) AS total_active_vacancies'),
          'MPP.No_Of_Position'
        );

      if(jobPostData?.length){
        let totalActiveVacancies = jobPostData[0].total_active_vacancies;
        let noOfPosition = jobPostData[0].No_Of_Position;
        return (noOfPosition - totalActiveVacancies);
      }
      return -1;

    } catch (err) {
      console.log("Error in checkVacancyValid function:", err);
      throw err;
    }
  }


