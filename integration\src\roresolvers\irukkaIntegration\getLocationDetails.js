// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appConstants');


let organizationDbConnection;
module.exports.getLocationDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside getLocationDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.recruitment, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1 && checkRights.Employee_Role.toLowerCase() === 'admin') {
            return (
                organizationDbConnection(ehrTables.location + " as LOC")
               .select('LOC.Location_Id', 'LOC.Location_Name', 'C.City_Id','C.City_Name', 'S.State_Id', 'S.State_Name', 'LOC.Pincode', 'CT.Country_Code', 'CT.Country_Name')
               .innerJoin(ehrTables.city + " as C", "LOC.City_Id", "C.City_Id")
               .innerJoin(ehrTables.state + " as S", 'LOC.State_Id', 'S.State_Id')
               .innerJoin(ehrTables.country + " as CT", 'LOC.Country_Code', 'CT.Country_Code')
                    .then((data) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Location Details retrieved successfully.", locationDetails: data };
                    })
                    .catch((err) => {
                        console.log('Error in getLocationDetails .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'SET0108');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        console.log('Error in getLocationDetails function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'SET0006');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
