// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId,s3FileUpload } = require('../../../common/appConstants');
const { generateAndUploadReport } = require('../../../src/common/commonFunction');

module.exports.listReqruitmentRequest = async (parent, args, context, info) => {

    console.log("Inside listReqruitmentRequest function.")
    let organizationDbConnection;

    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false,formId.reqruitmentRequest);

        if (Object.entries(checkRights).length > 0 && checkRights.Role_View === 1) {
            // Handle backward compatibility for orgLevel in response
            let getOrgLevel = null;

            if (!args.alexport) {
                // When postionParentCode is '0', treat it as valid and set orgLevel to null
                if (args.postionParentCode === '0') {
                    getOrgLevel = { Org_Level: null };
                } else if (!args.postionParentCode) {
                    // Only fetch from employee designation when postionParentCode is not provided (null/undefined)
                    let orgStructureResult = await organizationDbConnection(ehrTables.empJob + ' as EJ')
                      .select(
                        organizationDbConnection.raw(`CASE
                          WHEN OS.Parent_Path IS NOT NULL AND OS.Parent_Path != '0'
                          THEN SUBSTRING_INDEX(SUBSTRING_INDEX(OS.Parent_Path, ',', 2), ',', -1)
                          ELSE OS.Originalpos_Id
                        END AS firstParentPathId`)
                      )
                      .join(ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
                      .join(ehrTables.SFWPOrganizationStructure + ' as OS', 'OS.Pos_Code', 'DES.Designation_Code')
                      .where('EJ.Employee_Id', employeeId)
                      .first();

                    args.positionParentId = orgStructureResult?.firstParentPathId || '';
                    let orgStructurePosition = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
                      .select('OS.Pos_Code')
                      .where('OS.Originalpos_Id', args.positionParentId)
                      .first();
                        args.postionParentCode = orgStructurePosition?.Pos_Code || '';
                } else {
                    // When postionParentCode is provided (not null/undefined and not '0')
                    let orgStructurePosition = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
                        .select('OS.Originalpos_Id')
                        .where('OS.Pos_Code', args.postionParentCode)
                        .first();
                    args.positionParentId = orgStructurePosition?.Originalpos_Id || '';
                }

                // Get org level only if postionParentCode is not '0' and is provided
                if(!args.postionParentCode || args.postionParentCode.length===0){
                  organizationDbConnection ? organizationDbConnection.destroy() : null;
                  return { errorCode: "", message: "Reqruitment Request details retrieved successfully.", groupCode: args.postionParentCode, positionParentId:args.positionParentId, reqruitmentRequestDetails: [], orgLevel:''};
                }
                if (args.postionParentCode && args.postionParentCode !== '0') {
                    getOrgLevel = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
                        .select('OS.Org_Level')
                        .where('OS.Pos_Code', args.postionParentCode)
                        .first();
                }
            }
            const reqruitmentRequestDetails = await organizationDbConnection(ehrTables.mppRecruitmentRequest + ' as MRR')
            .select(
                'MRR.*',
                'SFWP.Pos_Code',
                'SFWP.Pos_Name',
                'SFWP.Approved_Position',
                'SFWP.Warm_Bodies',
                'ET.Employee_Type AS Employee_Type_Name',
                'PL.Position_Level',
                'PL.Position_Level_Id',
                'GRP.Pos_Name as Group_Name',
                'DIV.Pos_Name as Division_Name',
                'DEPT.Pos_Name as Department_Name',
                'SEC.Pos_Name as Section_Name',
                'ATS.Status AS jobPostStatus',
                'JP.Job_Post_Name',
                "CEG.Group_Name as CustomGroupName",
                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Added_By"),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as Updated_By"),
                 organizationDbConnection.raw("CONCAT_WS(' ',EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as approvedByName"),
                organizationDbConnection.raw(`
                    (SELECT sum(MRR2.No_Of_Position) 
                     FROM ?? as MRR2 
                     WHERE MRR2.Original_Position_Id = MRR.Original_Position_Id 
                     AND MRR2.Status != 'Rejected') as totalRecords`, [ehrTables.mppRecruitmentRequest]
                  )
            )
            .leftJoin( ehrTables.positionLevel + ' as PL', 'MRR.Position_Level', 'PL.Position_Level_Id')
            .leftJoin(ehrTables.employeeType + " as ET",'MRR.Employee_Type','ET.EmpType_Id')
            .leftJoin('emp_personal_info as EPI', 'EPI.Employee_Id', 'MRR.Added_By')
            .leftJoin('emp_personal_info as EPI2', 'EPI2.Employee_Id', 'MRR.Updated_By')
            .leftJoin('emp_personal_info as EPI3', 'EPI3.Employee_Id', 'MRR.Approver_Id')
            .leftJoin(ehrTables.jobPost + ' as JP', function () {
              this.on('JP.Position_Request_Id', '=', 'MRR.Recruitment_Id')
                  .andOnVal('JP.MPP_Position_Type', '=', 'Recruitment Request');
            })
            .leftJoin(
              ehrTables.atsStatusTable + ' as ATS',
              'JP.Status',
              'ATS.Id'
            )
            .leftJoin(ehrTables.candidateRecruitmentInfo+' as CRI','CRI.Job_Post_Id','JP.Job_Post_Id')
            .leftJoin('SFWP_Organization_Structure as SFWP', 'MRR.Original_Position_Id', 'SFWP.Originalpos_Id')
            .leftJoin(
              'SFWP_Organization_Structure as GRP',
              'MRR.Group_Code',
              'GRP.Pos_Code'
            )
            .leftJoin(
              'SFWP_Organization_Structure as DIV',
              'MRR.Division_Code',
              'DIV.Pos_Code'
            )
            .leftJoin(
              'SFWP_Organization_Structure as DEPT',
              'MRR.Department_Code',
              'DEPT.Pos_Code'
            )
            .leftJoin(
              'SFWP_Organization_Structure as SEC',
              'MRR.Section_Code',
              'SEC.Pos_Code'
            )
            .leftJoin(
              "custom_employee_group" + " as CEG",
              "CEG.Group_Id",
              "MRR.Custom_Group_Id"
            )
            .modify((queryBuilder)=>{
              // New organizational filtering logic - apply to both export and non-export
              const orgFilters = [];

              // Check for new organizational filters
              if (args.groupFilter && args.groupFilter.code) {
                if (args.groupFilter.code === '0') {
                  orgFilters.push(['MRR.Group_Code', 'NULL_OR_EMPTY']);
                } else {
                  orgFilters.push(['MRR.Group_Code', args.groupFilter.code]);
                }
              }
              if (args.divisionFilter && args.divisionFilter.code) {
                if (args.divisionFilter.code === '0') {
                  orgFilters.push(['MRR.Division_Code', 'NULL_OR_EMPTY']);
                } else {
                  orgFilters.push(['MRR.Division_Code', args.divisionFilter.code]);
                }
              }
              if (args.departmentFilter && args.departmentFilter.code) {
                if (args.departmentFilter.code === '0') {
                  orgFilters.push(['MRR.Department_Code', 'NULL_OR_EMPTY']);
                } else {
                  orgFilters.push(['MRR.Department_Code', args.departmentFilter.code]);
                }
              }
              if (args.sectionFilter && args.sectionFilter.code) {
                if (args.sectionFilter.code === '0') {
                  orgFilters.push(['MRR.Section_Code', 'NULL_OR_EMPTY']);
                } else {
                  orgFilters.push(['MRR.Section_Code', args.sectionFilter.code]);
                }
              }

              // Apply organizational filters
              if (orgFilters.length > 0 && !args.alexport) {
                orgFilters.forEach(([field, value]) => {
                  if (value === 'NULL_OR_EMPTY') {
                    queryBuilder.andWhere(function() {
                      this.whereNull(field).orWhere(field, '');
                    });
                  } else {
                    queryBuilder.andWhere(field, value);
                  }
                });
              }

              // Apply form-specific filtering
              if (args.formId && args.formId===15) {
                queryBuilder.andWhere(function () {
                  this.where('MRR.Status', 'Approved')
                })
              }
          })
          .count('CRI.Candidate_Id as candidateCount')
          .groupBy('MRR.Recruitment_Id','ATS.Status');

            // Handle alexport functionality
            if (args.alexport) {
              // Transform data for export
              const formattedData = transformAlexportDataRecruitment(reqruitmentRequestDetails);

              // Get organization code
              const orgCode = context.Org_Code || 'default';

              // Generate and upload file to S3
              const uploadResult = await generateAndUploadReport({
                  orgCode: orgCode,
                  reportData: formattedData,
                  reportType: 'APPROVED_FORECAST',
                  fileName: null
              });

              organizationDbConnection ? organizationDbConnection.destroy() : null;
              return {
                  errorCode: "",
                  message: "Approved & Forecasted Positions export data retrieved successfully.",
                  reqruitmentRequestDetails: null,
                  groupCode: null,
                  positionParentId: null,
                  orgLevel: '',
                  s3Url: uploadResult.success ? uploadResult.s3Url : null,
                  s3Path: uploadResult.success ? uploadResult.s3Path : null
              };
            }

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Approved & Forecasted Positions details retrieved successfully.", reqruitmentRequestDetails: reqruitmentRequestDetails,groupCode:args.postionParentCode, positionParentId:args.positionParentId
              ,orgLevel:getOrgLevel?.Org_Level
              };

        } else {
            throw '_DB0100';
        }

    } catch (err) {
        //Destroy DB connection
        console.error('Error in listReqruitmentRequest function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'EI00178');
        throw new ApolloError(errResult.message, errResult.code);
    }
}



function transformAlexportDataRecruitment(rawData) {
    return rawData.map(row => ({
        'Position Name': row.Pos_Name || '',
        'Position Code': row.Pos_Code || '',
        'Group Code': row.Group_Code || '',
        'Group Name': row.Group_Name || '',
        'Employee Type': row.Employee_Type_Name || '',
        'Division Code': row.Division_Code || '',
        'Division Name': row.Division_Name || '',
        'Department Code': row.Department_Code || '',
        'Department Name': row.Department_Name || '',
        'Section Code': row.Section_Code || '',
        'Section Name': row.Section_Name || '',
        'Position Level': row.Position_Level || '',
        'Cost Code': row.Cost_Code || '',
        'Employee Type': row.Employee_Type_Name || '',
        'Replacement For': row.Replacement_For || '',
        'Reason for Replacement': row.Reason_For_Replacement || '',
        'No of Positions': row.No_Of_Position || 0,
        'Status': row.Status || '',
        'Added On': row.Added_On || '',
        'Added By': row.Added_By || '',
        'Updated On': row.Updated_On || '',
        'Updated By': row.Updated_By || '',
        'Approver': row.approvedByName || '',
    }));
}




