
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants')

module.exports.retrieveEmployeeRoleEmail = async (parent, args, context, info) => {

    console.log("Inside retrieveEmployeeRoleEmail function.")
    let organizationDbConnection;

    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let accessFormId= args.formId?args.formId:formId.hiringForeCastSettings;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, accessFormId);
        
        if(Object.entries(checkRights).length > 0 && checkRights.Role_View === 1 ){

            let employeeEmailList =  await  organizationDbConnection(ehrTables.empJob + ' as EJ')
            .select('EJ.User_Defined_EmpId', 'EJ.Emp_Email', 'EJ.Employee_Id',organizationDbConnection.raw("CONCAT_WS(' ',EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name) as Employee_Name"))
            .join(ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'EJ.Employee_Id')
            .where('EJ.Emp_Status', 'Active').modify(builder => {
                if (args.roleIds && args.roleIds.length > 0) {
                    builder.whereIn('EJ.Roles_Id', args.roleIds);
                }
            })
            .whereNotNull('EJ.Emp_Email').where('EJ.Emp_Email', '!=', '')

            let nonRoleEmployeeEmailList =  await  organizationDbConnection(ehrTables.empJob + ' as EJ')
            .select('EJ.User_Defined_EmpId', 'EJ.Emp_Email','EJ.Employee_Id',organizationDbConnection.raw("CONCAT_WS(' ',EMP.Emp_First_Name, EMP.Emp_Middle_Name, EMP.Emp_Last_Name) as Employee_Name"))
            .join(ehrTables.empPersonalInfo + ' as EMP', 'EMP.Employee_Id', 'EJ.Employee_Id')
            .where('EJ.Emp_Status', 'Active')
            .modify(builder => {
                if (args.roleIds && args.roleIds.length > 0) {
                    builder.whereNotIn('EJ.Roles_Id', args.roleIds);
                }
            })
            .whereNotNull('EJ.Emp_Email').where('EJ.Emp_Email', '!=', '')

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Employee role email retrieved successfully.", emailList: employeeEmailList,nonRoleEmailList:nonRoleEmployeeEmailList };

        } else {
            throw '_DB0100';
        }

    }catch(err){
        //Destroy DB connection
        console.error('Error in retrieveEmployeeRoleEmail function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'EI00177');
        throw new ApolloError(errResult.message, errResult.code);
    }
}


