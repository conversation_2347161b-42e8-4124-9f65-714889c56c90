// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib
// require knex
const knex = require('knex')
// require common table alias
const { ehrTables } = require('../../../common/tablealias')
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda')

module.exports.listDetailsBasedOnGroupCode = async (
  parent,
  args,
  context,
  info
) => {
  console.log('Inside listDetailsBasedOnGroupCode function.')
  let organizationDbConnection

  try {
    let employeeId = context.Employee_Id
    organizationDbConnection = knex(context.connection.OrganizationDb)

    let checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      employeeId,
      '',
      '',
      'UI',
      false,
      args.formId
    )

    if (
      Object.entries(checkRights).length > 0 &&
      checkRights.Role_View === 1
    ) {
      const parentStructureDetails = await organizationDbConnection(
        ehrTables.SFWPOrganizationStructure
      )
        .select('Pos_Code', 'Originalpos_Id','Pos_Name', 'Org_Level', 'Parent_Path','Cost_Code',
          organizationDbConnection.raw(
            "CASE WHEN Pos_Code IS NOT NULL THEN CONCAT(Pos_Name,' - ',Pos_Code) ELSE Pos_Name END AS Pos_full_Name"
          )
        )
        .groupBy('Originalpos_Id', 'Pos_Code')
        .whereNotIn('Status', ['InActive'])
        .modify((queryBuilder)=>{
          if (args.postionParentId && args.postionParentId.length) {
            queryBuilder.andWhere(function () {
              this.where('Parent_Id', '=', args.postionParentId).orWhere('Originalpos_Id', args.postionParentId);
            })
          }
          if (args.offset) {
            queryBuilder.offset(args.offset);
          }
          if (args.limit) {
            queryBuilder.limit(args.limit);
          }
      })
  
      let countQuery = organizationDbConnection(ehrTables.SFWPOrganizationStructure).count({ totalRecords: "*" })
      .whereNotIn('Status', ['InActive'])
      .modify((queryBuilder)=>{
        if (args.postionParentId && args.postionParentId.length) {
          queryBuilder.andWhere(function () {
            this.where('Parent_Id', '=', args.postionParentId).orWhere('Originalpos_Id', args.postionParentId);
          })
        
        }
    })
    const countResult = await countQuery;
    const totalRecords = countResult[0].totalRecords;
      let positionDetailsObject = {}
      if (parentStructureDetails && parentStructureDetails.length) {
        positionDetailsObject.divisionList = parentStructureDetails.filter(
          (div) => div.Org_Level === 'DIV'
        )
        positionDetailsObject.sectionList = parentStructureDetails.filter(
          (sec) => sec.Org_Level === 'SEC'
        )
        positionDetailsObject.deptList = parentStructureDetails.filter(
          (dept) => dept.Org_Level === 'DEPT'
        )
        let costCodeList = parentStructureDetails.filter(
          (cost) => cost.Cost_Code!==''
        )
        positionDetailsObject.costCodeList=costCodeList.map(item=>(item.Cost_Code))
      }
      organizationDbConnection ? organizationDbConnection.destroy() : null
      return {
        errorCode: '',
        message: 'Position details retrieved successfully.',
        positionDetails: positionDetailsObject,
        totalRecords:totalRecords
      }
    
  }
 } catch (err) {
    //Destroy DB connection
    console.error(
      'Error in listDetailsBasedOnGroupCode function main catch block.',
      err
    )
    organizationDbConnection ? organizationDbConnection.destroy() : null
    let errResult = commonLib.func.getError(err, 'EI00184')
    throw new ApolloError(errResult.message, errResult.code)
  }
}
