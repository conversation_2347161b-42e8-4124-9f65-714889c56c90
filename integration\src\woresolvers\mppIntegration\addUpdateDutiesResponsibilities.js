const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const { validatCommonRuleInput } = require('../../common/commonFunction');

module.exports.addUpdateDutiesResponsibilities = async (parent, args, context) => {
  let validationError = {};
  const { Employee_Id: loginEmployeeId, User_Ip: userIp } = context;
  let organizationDbConnection;
  try {
    // Check employee access rights
    organizationDbConnection = knex(context.connection.OrganizationDb);
    const checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formId.newPosition);
    if (Object.keys(checkRights).length <= 0 || (args.dutiesResponsibilityId && checkRights.Role_Update === 0) || (!args.dutiesResponsibilityId && checkRights.Role_Add === 0)) {
      throw !args.dutiesResponsibilityId && checkRights.Role_Add === 0 ? '_DB0101' : '_DB0102';
    }

    const {
      dutiesResponsibilityId,
      positionRequestId,
      regularDuties,
      noOfHoursPeriod,
      period,
      competenciesRequired,
      ratingOfCompetency,
      competency,
    } = args;

    const fieldValidations = {};
    if (regularDuties) {
      fieldValidations.regularDuties = 'IVE0490';
    }
    if(competency){
      fieldValidations.competency='IVE0504'
    }

    // Validate input fields
    validationError = await validatCommonRuleInput(args, fieldValidations);
    if (Object.keys(validationError).length > 0) {
      throw 'IVE0000';
    }

    // Prepare the duties responsibilities data
    const dutiesResponsibilitiesData = {
      Position_Request_Id: positionRequestId,
      Regular_Duties: regularDuties || null,
      No_Of_Hours_Period: +noOfHoursPeriod || null,
      Period: period || null,
      Competencies_Required: competenciesRequired || null,
      Competency:competency || null,
      Rating_Of_Competency: ratingOfCompetency || null,
    };

    // Start a transaction
    return await organizationDbConnection.transaction(async (trx) => {
      // Perform database operations (Insert/Update) within the transaction
      let result;
      if (dutiesResponsibilityId) {
        result = await trx(ehrTables.mppDutiesResponsibilities)
          .where('Duties_Responsibility_Id', dutiesResponsibilityId)
          .update(dutiesResponsibilitiesData);
      } else {
        result = await trx(ehrTables.mppDutiesResponsibilities)
          .insert(dutiesResponsibilitiesData);
      }

      // Handle failure to insert/update
      if (!result) throw 'EI00179';
      // Log activity
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection: trx, // Pass the transaction
        message: `Duties responsibilities ${dutiesResponsibilityId ? 'updated' : 'added'} successfully`,
      });

      // Commit transaction
      return {
        errorCode: '',
        message: `Duties responsibilities ${dutiesResponsibilityId ? 'updated' : 'added'} successfully`
      };
    });
  } catch (error) {
    console.error('Error in addUpdateDutiesResponsibilities function main catch block.', error);
    if (error === 'IVE0000') {
      console.log('Validation error in addUpdateDutiesResponsibilities function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      error = error && error.code === 'ER_DUP_ENTRY' ? 'EI00200': error;
      const errResult = commonLib.func.getError(error, 'EI00185');
      throw new ApolloError(errResult.message, errResult.code);
    }
  } finally {
    // Clean up the database connection
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};