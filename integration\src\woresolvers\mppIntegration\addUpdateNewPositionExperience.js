// Require common libraries and modules as before
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { validatCommonRuleInput } = require('../../common/commonFunction');
const { formId } = require('../../../common/appConstants');

module.exports.addUpdateExperience = async (parent, args, context) => {
  let validationError = {};
  let organizationDbConnection;
  try {
    const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check access rights for the employee
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection, loginEmployeeId, '', '', 'UI', false, formId.newPosition
    );

    if (Object.keys(checkRights).length <= 0 || 
        (args.experienceId && checkRights.Role_Update === 0) || 
        (!args.experienceId && checkRights.Role_Add === 0)) {
      throw !args.experienceId ? '_DB0101' : '_DB0102';
    }

    // Destructure input arguments
    const {
      experienceId,
      typeOfJobs,
      months,
      years,
      positionRequestId,
      status,
      eventId,
    } = args;

    // Validate months input
    if (months && +months > 12) {
      validationError['IVE0488'] = commonLib.func.getError('IVE0488', '').message;
      throw 'IVE0000';
    }

    // Field validation logic
    const fieldValidations = {};
    if (typeOfJobs) {
      fieldValidations.typeOfJobs = 'IVE0493';
    }

    // Validate input based on rules
    validationError = await validatCommonRuleInput(args, fieldValidations);
    if (Object.keys(validationError).length > 0) {
      throw 'IVE0000';
    }

    // Prepare the update data
    const experienceData = {
      Type_Of_Jobs: typeOfJobs || null,
      Months: months || 0,
      Years: years || 0,
      Position_Request_Id: positionRequestId,
    };
    // Start a transaction
    return await organizationDbConnection.transaction(async (trx) => {
      // Perform database operations (Insert/Update) within the transaction
      let updateResult;
      if (experienceId) {
        updateResult = await trx(ehrTables.mppExperience)
          .where('Experience_Id', experienceId)
          .update(experienceData);
      } else {
        updateResult = await trx(ehrTables.mppExperience)
          .insert(experienceData);
      }

      if (!updateResult) throw 'EI00179';  // Handle failure to insert/update


      // Log activity
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection: trx, // Pass the transaction
        message: `Experience requirement ${experienceId ? 'updated' : 'added'} successfully`,
      });

      // Return success message
      return { errorCode: '', message: `Experience requirement ${experienceId ? 'updated' : 'added'} successfully` };
    });
  } catch (error) {
    console.log('Error in addUpdateExperience function:', error);
    
    // Rollback will happen automatically since we're using transaction
    if (error === 'IVE0000') {
      console.log('Validation error in addUpdateExperience function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      const errResult = commonLib.func.getError(error, 'EI00189');
      throw new ApolloError(errResult.message, errResult.code);
    }
  }
  finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};
