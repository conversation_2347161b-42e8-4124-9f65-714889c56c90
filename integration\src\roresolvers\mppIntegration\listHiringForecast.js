
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment');
const { formId,s3FileUpload } = require('../../../common/appConstants');
const { generateAndUploadReport } = require('../../../src/common/commonFunction');

module.exports.listHiringForecast = async (parent, args, context, info) => {

    console.log("Inside listForecastPosition function.")
    let organizationDbConnection;

    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, formId.hiringForeCast);
        
        if(Object.entries(checkRights).length > 0 && checkRights.Role_View === 1){

            let forecastStartYear = args.forecastingYear ? args.forecastingYear : moment().year();
            
            let mppForecastSettings = await organizationDbConnection(ehrTables.mppForecastSettings).select('End_Month').first()
            let forecastEndMonth = mppForecastSettings ? mppForecastSettings.End_Month : 12;

            let {forecastStartMonth, forecastEndYear} = calculateStartMonth(forecastEndMonth, forecastStartYear)

            // Handle backward compatibility for orgLevel in response
            let getOrgLevel = null;

            if (!args.alexport) {
                // When postionParentId is '0', treat it as valid and set orgLevel to null
                if (args.postionParentId === '0') {
                    getOrgLevel = { Org_Level: null };
                } else if (!args.postionParentId || args.postionParentId.length === 0) {
                    // Only fetch from employee designation when postionParentId is not provided (null/undefined/empty)
                    let orgStructureResult = await organizationDbConnection(ehrTables.empJob + ' as EJ')
                        .select(organizationDbConnection.raw(`CASE WHEN OS.Parent_Path IS NOT NULL  AND OS.Parent_Path != '0' THEN SUBSTRING_INDEX(SUBSTRING_INDEX(OS.Parent_Path, ',', 2), ',', -1) ELSE OS.Originalpos_Id END AS firstParentPathId`))
                        .join( ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
                        .join( ehrTables .SFWPOrganizationStructure + ' as OS', 'OS.Pos_Code', 'DES.Designation_Code')
                        .where('EJ.Employee_Id', employeeId).first();

                    args.postionParentId = orgStructureResult?.firstParentPathId;

                    if(!args.postionParentId || args.postionParentId.length===0){
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Hiring forecast list retrieved successfully.", result: null, groupId: args.postionParentId,orgLevel: '' };
                    }
                }

                // Get org level only if postionParentId is not '0' and is provided
                if (args.postionParentId && args.postionParentId !== '0') {
                    getOrgLevel = await organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as OS')
                        .select('OS.Org_Level')
                        .where('OS.Originalpos_Id', args.postionParentId)
                        .first();
                }
            }
            
            const [countResult, foreCastingList] = await Promise.all([
              // Query to get the total count of records
              organizationDbConnection(
                ehrTables.mppHiringForecast + ' as MPPHF'
              )
                .countDistinct('OS.Organization_Structure_Id as totalRecords')
                .join(
                  ehrTables.SFWPOrganizationStructure + ' as OS',
                  'OS.Organization_Structure_Id',
                  'MPPHF.Organization_Structure_Id'
                )
                .where(function () {
                  this.where('MPPHF.Forecast_Year', forecastStartYear)
                    .andWhereBetween('MPPHF.Forecast_Month', [
                      forecastStartMonth,
                      12
                    ])
                    .orWhere('MPPHF.Forecast_Year', forecastEndYear)
                    .andWhereBetween('MPPHF.Forecast_Month', [
                      1,
                      forecastEndMonth
                    ])
                })
                .modify((queryBuilder) => {
                  // New organizational filtering logic - apply to both export and non-export
                  const orgFilters = [];

                  // Check for new organizational filters
                  if (args.groupFilter && args.groupFilter.id) {
                    if (args.groupFilter.id === '0') {
                      orgFilters.push(['MPPHF.Group_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Group_Id', args.groupFilter.id]);
                    }
                  }
                  if (args.divisionFilter && args.divisionFilter.id) {
                    if (args.divisionFilter.id === '0') {
                      orgFilters.push(['MPPHF.Division_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Division_Id', args.divisionFilter.id]);
                    }
                  }
                  if (args.departmentFilter && args.departmentFilter.id) {
                    if (args.departmentFilter.id === '0') {
                      orgFilters.push(['MPPHF.Department_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Department_Id', args.departmentFilter.id]);
                    }
                  }
                  if (args.sectionFilter && args.sectionFilter.id) {
                    if (args.sectionFilter.id === '0') {
                      orgFilters.push(['MPPHF.Section_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Section_Id', args.sectionFilter.id]);
                    }
                  }

                  // Apply organizational filters
                  if (orgFilters.length > 0 && !args.alexport) {
                    orgFilters.forEach(([field, value]) => {
                      if (value === 'NULL_OR_EMPTY') {
                        queryBuilder.andWhere(function() {
                          this.whereNull(field).orWhere(field, '');
                        });
                      } else {
                        queryBuilder.andWhere(field, value);
                      }
                    });
                  }
                }),

              organizationDbConnection(ehrTables.mppHiringForecast + ' as MPPHF')
                .select(
                  'MPPHF.Organization_Structure_Id',
                  'OS.Originalpos_Id',
                  'OS.Pos_Code',
                  'OS.Pos_Name',
                  'OS.Approved_Position',
                  'OS.Warm_Bodies',
                  'MPPHF.Position_Title',
                  'MPPHF.Added_On',
                  'MPPHF.Division_Id',
                  'MPPHF.Department_Id',
                  'MPPHF.Section_Id',
                  'MPPHF.Group_Id',
                  'MPPHF.Updated_On', 'DIV.Pos_Name as Division_Name', 'DIV.Pos_Code as Division_Code',
                  'SEC.Pos_Name as Section_Name', 'SEC.Pos_Code as Section_Code',
                  'DEPT.Pos_Name as Department_Name', 'DEPT.Pos_Code as Department_Code',
                  'GRP.Pos_Name as Group_Name', 'GRP.Pos_Code as Group_Code',
                  'GRP.Originalpos_Id as Group_Id',
                  'GRP.Organization_Structure_Id as Group_Organization_Structure_Id',
                  organizationDbConnection.raw(
                    'CASE WHEN (SUM(MPPHF.No_Of_Position) > (OS.Approved_Position - OS.Warm_Bodies)) THEN OS.Approved_Position - OS.Warm_Bodies ELSE SUM(MPPHF.No_Of_Position) END AS To_Be_Hired'
                  ),
                  organizationDbConnection.raw(
                    'CASE WHEN (SUM(MPPHF.No_Of_Position) > (OS.Approved_Position - OS.Warm_Bodies)) THEN SUM(MPPHF.No_Of_Position) - (OS.Approved_Position - OS.Warm_Bodies) ELSE 0 END AS To_Be_Source'
                  ),
                  organizationDbConnection.raw(
                    "CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) AS Added_By"
                  ),
                  organizationDbConnection.raw(
                    "CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) AS Updated_By"
                  ),
                )
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id','MPPHF.Added_By')
                .leftJoin(
                  ehrTables.empPersonalInfo + ' as EPI2',
                  'EPI2.Employee_Id',
                   'MPPHF.Updated_By'
                    )
                .leftJoin(
                  ehrTables.SFWPOrganizationStructure + ' as OS',
                  'OS.Organization_Structure_Id',
                  'MPPHF.Organization_Structure_Id'
                )
                .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DIV','DIV.Originalpos_Id', 'MPPHF.Division_Id')
                .leftJoin(ehrTables.SFWPOrganizationStructure + ' as SEC','SEC.Originalpos_Id', 'MPPHF.Section_Id')
                .leftJoin(ehrTables.SFWPOrganizationStructure + ' as DEPT','DEPT.Originalpos_Id', 'MPPHF.Department_Id')
                .leftJoin(ehrTables.SFWPOrganizationStructure + ' as GRP','GRP.Originalpos_Id', 'MPPHF.Group_Id')
                .modify(function (queryBuilder) {
                  queryBuilder.select(
                    organizationDbConnection.raw(
                      'JSON_ARRAYAGG(JSON_OBJECT(' +
                        "'Hiring_Forecast_Id', MPPHF.Hiring_Forecast_Id, " +
                        "'Forecast_Year', MPPHF.Forecast_Year, " +
                        "'Forecast_Month', MPPHF.Forecast_Month, " +
                        "'No_Of_Position', MPPHF.No_Of_Position)) AS foreCastList"
                    )
                  )

                  // Apply pagination only for non-export cases
                  if (!args.alexport) {
                    if (args.offset) {
                      queryBuilder.offset(args.offset)
                    }
                    if (args.limit) {
                      queryBuilder.limit(args.limit)
                    }
                  }
                })
                .where(function () {
                  this.where('MPPHF.Forecast_Year', forecastStartYear)
                    .andWhereBetween('MPPHF.Forecast_Month', [
                      forecastStartMonth,
                      12
                    ])
                    .orWhere('MPPHF.Forecast_Year', forecastEndYear)
                    .andWhereBetween('MPPHF.Forecast_Month', [
                      1,
                      forecastEndMonth
                    ])
                })
                .modify((queryBuilder) => {
                  // New organizational filtering logic - apply to both export and non-export
                  const orgFilters = [];

                  // Check for new organizational filters
                  if (args.groupFilter && args.groupFilter.id) {
                    if (args.groupFilter.id === '0') {
                      orgFilters.push(['MPPHF.Group_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Group_Id', args.groupFilter.id]);
                    }
                  }
                  if (args.divisionFilter && args.divisionFilter.id) {
                    if (args.divisionFilter.id === '0') {
                      orgFilters.push(['MPPHF.Division_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Division_Id', args.divisionFilter.id]);
                    }
                  }
                  if (args.departmentFilter && args.departmentFilter.id) {
                    if (args.departmentFilter.id === '0') {
                      orgFilters.push(['MPPHF.Department_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Department_Id', args.departmentFilter.id]);
                    }
                  }
                  if (args.sectionFilter && args.sectionFilter.id) {
                    if (args.sectionFilter.id === '0') {
                      orgFilters.push(['MPPHF.Section_Id', 'NULL_OR_EMPTY']);
                    } else {
                      orgFilters.push(['MPPHF.Section_Id', args.sectionFilter.id]);
                    }
                  }

                  // Apply organizational filters
                  if (orgFilters.length > 0 && !args.alexport) {
                    orgFilters.forEach(([field, value]) => {
                      if (value === 'NULL_OR_EMPTY') {
                        queryBuilder.andWhere(function() {
                          this.whereNull(field).orWhere(field, '');
                        });
                      } else {
                        queryBuilder.andWhere(field, value);
                      }
                    });
                  }
                })
               .groupBy('Organization_Structure_Id')
            ])
            let totalCountResult=countResult[0].totalRecords;

            // Handle alexport functionality
            if (args.alexport) {
              // Get individual monthly data for export
              const monthlyData = await organizationDbConnection(ehrTables.mppHiringForecast + ' as MPPHF')
                  .select(
                      'MPPHF.Organization_Structure_Id',
                      'MPPHF.Forecast_Year',
                      'MPPHF.Forecast_Month',
                      'MPPHF.No_Of_Position'
                  )
                  .where(function () {
                      this.where('MPPHF.Forecast_Year', forecastStartYear)
                          .andWhereBetween('MPPHF.Forecast_Month', [forecastStartMonth, 12])
                          .orWhere('MPPHF.Forecast_Year', forecastEndYear)
                          .andWhereBetween('MPPHF.Forecast_Month', [1, forecastEndMonth])
                  })
                  .modify((queryBuilder) => {
                    // Apply same organizational filtering for export
                    const orgFilters = [];

                    if (args.groupFilter && args.groupFilter.id) {
                      if (args.groupFilter.id === '0') {
                        orgFilters.push(['MPPHF.Group_Id', 'NULL_OR_EMPTY']);
                      } else {
                        orgFilters.push(['MPPHF.Group_Id', args.groupFilter.id]);
                      }
                    }
                    if (args.divisionFilter && args.divisionFilter.id) {
                      if (args.divisionFilter.id === '0') {
                        orgFilters.push(['MPPHF.Division_Id', 'NULL_OR_EMPTY']);
                      } else {
                        orgFilters.push(['MPPHF.Division_Id', args.divisionFilter.id]);
                      }
                    }
                    if (args.departmentFilter && args.departmentFilter.id) {
                      if (args.departmentFilter.id === '0') {
                        orgFilters.push(['MPPHF.Department_Id', 'NULL_OR_EMPTY']);
                      } else {
                        orgFilters.push(['MPPHF.Department_Id', args.departmentFilter.id]);
                      }
                    }
                    if (args.sectionFilter && args.sectionFilter.id) {
                      if (args.sectionFilter.id === '0') {
                        orgFilters.push(['MPPHF.Section_Id', 'NULL_OR_EMPTY']);
                      } else {
                        orgFilters.push(['MPPHF.Section_Id', args.sectionFilter.id]);
                      }
                    }

                    orgFilters.forEach(([field, value]) => {
                      if (value === 'NULL_OR_EMPTY') {
                        queryBuilder.andWhere(function() {
                          this.whereNull(field).orWhere(field, '');
                        });
                      } else {
                        queryBuilder.andWhere(field, value);
                      }
                    });
                  });

              // Transform data into the required format
              const formattedData = transformAlexportData(foreCastingList, monthlyData, forecastStartYear, forecastEndYear, forecastStartMonth, forecastEndMonth);

              // Get organization code
              const orgCode = context.Org_Code || 'default';

              // Generate and upload file to S3
              const uploadResult = await generateAndUploadReport({
                  orgCode: orgCode,
                  reportData: formattedData,
                  reportType: 'HIRING_FORECAST',
                  fileName: null
              });

              organizationDbConnection ? organizationDbConnection.destroy() : null;
              return {
                  errorCode: "",
                  message: "Hiring forecast export data retrieved successfully.",
                  result: uploadResult.success ? uploadResult.s3Url : JSON.stringify(formattedData),
                  groupId: null,
                  totalCountResult: formattedData.length,
                  orgLevel: '',
                  s3Url: uploadResult.success ? uploadResult.s3Url : null,
                  s3Path: uploadResult.success ? uploadResult.s3Path : null
              };
            }

            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Hiring forecast list retrieved successfully.", result: JSON.stringify(foreCastingList), groupId: args.postionParentId ,totalCountResult:totalCountResult,orgLevel:getOrgLevel?.Org_Level};

        } else {
            throw '_DB0100';
        }

    }catch(err){
        //Destroy DB connection
        console.error('Error in listForecastPosition function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, 'EI00175');
        throw new ApolloError(errResult.message, errResult.code);
    }
}



function calculateStartMonth(endMonth, startYear) {
    let forecastStartMonth;
    let forecastEndYear = startYear;

    if (endMonth === 12) {
        // Scenario 1: End month is December, start month is January of the same year
        forecastStartMonth = 1;
    } else {
        // Scenario 2: End month is any month other than December
        forecastStartMonth = endMonth + 1;
        forecastEndYear = startYear + 1;
    }

    return { forecastStartMonth, forecastEndYear };
}



function transformAlexportData(aggregatedData, monthlyData, forecastStartYear, forecastEndYear, forecastStartMonth, forecastEndMonth) {
    // Create a map of monthly data by Organization_Structure_Id
    const monthlyMap = {};
    monthlyData.forEach(row => {
        const key = row.Organization_Structure_Id;
        if (!monthlyMap[key]) {
            monthlyMap[key] = {};
        }
        const monthName = getMonthName(row.Forecast_Month, row.Forecast_Year, forecastStartYear, forecastEndYear, forecastStartMonth, forecastEndMonth);
        if (monthName) {
            monthlyMap[key][monthName] = (monthlyMap[key][monthName] || 0) + (row.No_Of_Position || 0);
        }
    });

    const result = [];

    aggregatedData.forEach(row => {
        const orgId = row.Organization_Structure_Id;
        const monthlyValues = monthlyMap[orgId] || {};

        const positionData = {
            'Position Title': row.Position_Title || row.Pos_Name || '',
            'Position Code': row.Pos_Code || '',
            'Approved Positions': row.Approved_Position || 0,
            'Warm Bodies': row.Warm_Bodies || 0,
            'Group': row.Group_Name || '',
            'Group Code': row.Group_Code || '',
            'Division': row.Division_Name || '',
            'Division Code': row.Division_Code || '',
            'Department': row.Department_Name || '',
            'Department Code': row.Department_Code || '',
            'Section': row.Section_Name || '',
            'Section Code': row.Section_Code || '',
            'Jan': monthlyValues['Jan'] || 0,
            'Feb': monthlyValues['Feb'] || 0,
            'Mar': monthlyValues['Mar'] || 0,
            'Apr': monthlyValues['Apr'] || 0,
            'May': monthlyValues['May'] || 0,
            'Jun': monthlyValues['Jun'] || 0,
            'Jul': monthlyValues['Jul'] || 0,
            'Aug': monthlyValues['Aug'] || 0,
            'Sep': monthlyValues['Sep'] || 0,
            'Oct': monthlyValues['Oct'] || 0,
            'Nov': monthlyValues['Nov'] || 0,
            'Dec': monthlyValues['Dec'] || 0,
            'Approved Vacant Positions': row.To_Be_Hired || 0,
            'Vacancies for TO Review': row.To_Be_Source || 0,
            'Added_By': row.Added_By || '',
            'Added_On': row.Added_On || '',
            'Updated_By': row.Updated_By || '',
            'Updated_On': row.Updated_On || ''
        };

        result.push(positionData);
    });

    return result;
}

function getMonthName(month, year, forecastStartYear, forecastEndYear, forecastStartMonth, forecastEndMonth) {
    const monthNames = {
        1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
        7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
    };

    // Only include months that are within the forecast period
    if ((year === forecastStartYear && month >= forecastStartMonth) ||
        (year === forecastEndYear && month <= forecastEndMonth)) {
        return monthNames[month];
    }

    return null;
}


