//Require common library to access common function
var moment = require('moment-timezone');
const knex = require('knex');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { validateWithRulesAndReturnMessages } = require('@cksiva09/validationlib/src/validator');
const { ehrTables } = require('../../common/tablealias');
const { formId } = require('../../common/appConstants');

module.exports.uploadBulkCandidateDetails = async (parent, args, context, info) => {

    console.log('Inside uploadBulkCandidateDetails function');
    let organizationDbConnection;
    let validationErrorList = []
    try{ 

        args.Employee_Id = context.Employee_Id;
        args.orgCode = context.Org_Code;
    
        organizationDbConnection = knex(context.connection.OrganizationDb);

        if(!args.candidateList || args.candidateList.length == 0){
          
            validationErrorList.push({'ERR-001': 'Please upload at least one candidate to proceed.'})
            throw 'IVE0000'
        }

        let indexValue = 1;
        args.candidateList.forEach(async (candidate)=>{
            candidate.rowNo =  candidate.rowNo ?  candidate.rowNo : indexValue;
            let validationError =jobCandidatesValidation(candidate)
            indexValue++;
            if (Object.keys(validationError).length > 0) {
                validationErrorList.push(validationError)
            }
        })
        
        if(validationErrorList.length){
            throw 'IVE0000'
        }

        const statusDetail = await organizationDbConnection('ats_status_table').where('Status', 'Applied').first();

        let candidateRecruitmentList = [], candidateLanguageList = [], candidateContactList = [], candidatePassportList = [], 
        candidateEducationList = [], candidatePreferredLocationList = [], candidateReferenceList = [], candidateCertificationList = [], 
        dataPrivacyStatementResponses = [], candidateExperienceList=[], workAuthorizationList = [], candidateDependentList = [], candidateCustomFieldList = [];
       
        await organizationDbConnection
        .transaction(async function (trx) {

            await Promise.all(args.candidateList.map(async (candidate) => {

                const Employee_Id = candidate.Employee_Id;

                /**insert candidate's personal info */
                const [lastInsertedId] = await organizationDbConnection('candidate_personal_info').insert({
                    Emp_First_Name: candidate.firstName,
                    Emp_Last_Name: candidate.lastName,
                    Emp_Middle_Name: candidate.middleName ? candidate.middleName : null,
                    Emp_Pref_First_Name: candidate.knownAs,
                    Gender_Identity_Id: candidate.genderIdentityId,
                    Gender_Expression_Id: candidate.genderExpressionId,
                    Physically_Challenged: candidate.physicallyChallenged ? 1 : 0,
                    Salutation: candidate.salutation ? candidate.salutation : null,
                    Suffix: candidate.suffix ? candidate.suffix : null,
                    Gender_Id: candidate.genderId ? candidate.genderId : null,
                    Gender: candidate.gender,
                    DOB: candidate.dob,
                    Blood_Group: candidate.bloodGroup ? candidate.bloodGroup : null,
                    Marital_Status: candidate.maritalStatus,
                    Nationality: candidate.nationality,
                    Nationality_Id:candidate.nationalityId,
                    Personal_Email: candidate.emailId,
                    Source_Type: 'ATS',
                    Photo_Path: null,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: Employee_Id,
                    Gender_Orientations: candidate.genderOrientations ? candidate.genderOrientations : null,
                    Pronoun: candidate.pronoun ? candidate.pronoun : null,
                    Statutory_Insurance_Number: candidate.statutoryInsuranceNumber ? candidate.statutoryInsuranceNumber : null,
                    PRAN_No: candidate.pranNo ? candidate.pranNo : null,
                    Candidate_Status: statusDetail?.Status ||'Applied',
                    Data_Privacy_Statement: candidate.dataPrivacyStatement ? 1 : 0
                }).transacting(trx);

                candidateRecruitmentList.push({
                    Candidate_Id: lastInsertedId,
                    Job_Post_Id: candidate.jobPost,
                    Current_Employer: candidate.currentEmployer ? candidate.currentEmployer : null,
                    Notice_Period: candidate.noticePeriod ? candidate.noticePeriod : null,
                    Current_CTC: candidate.currentCTC,
                    Expected_CTC: candidate.expectedCTC,
                    Resume:  null,
                    Currency : candidate.currency,
                    Resume_File_Size: null,
                    National_Identification_Number: candidate.nationalIdentificationNumber ? candidate.nationalIdentificationNumber : null,
                    Candidate_Status : candidate?.status || statusDetail?.Id || 10,
                    Total_Experience_In_Years: candidate.totalExperienceInYears ? candidate.totalExperienceInYears : null,
                    Total_Experience_In_Months: candidate.totalExperienceInMonths ? candidate.totalExperienceInMonths : null,
                    Source: candidate.source,
                    Skill_Set: candidate.skillSet ? JSON.stringify(candidate.skillSet) : null,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: Employee_Id
                });

                candidateContactList.push({
                    Candidate_Id: lastInsertedId,
                    Mobile_No: candidate.mobileNo,
                    Mobile_No_Country_Code: candidate.mobileNoCountryCode,
                    pApartment_Name: candidate.apartmentName,
                    pStreet_Name: candidate.street,
                    pCity: candidate.city,
                    pState: candidate.state,
                    pCountry: candidate.country,
                    pPincode: candidate.pincode,
                    pBarangay: candidate.barangay,
                    pRegion: candidate.region,
                    Fax_No: candidate.emergencyContactNo ? candidate.emergencyContactNo : null,
                    Emergency_Contact_Name: candidate.emergencyContactName ? candidate.emergencyContactName : null,
                    Emergency_Contact_Relation: candidate.emergencyContactRelation ? candidate.emergencyContactRelation : null,
                });

                if(candidate.passportNo){
                    candidatePassportList.push({
                        Candidate_Id: lastInsertedId,
                        Passport_No: candidate.passportNo
                    });
                }

                if(candidate.languagesKnown && candidate.languagesKnown.length > 0){
                    const canidateLangKnown = candidate.languagesKnown.filter(field => field.langKnown)
                    .map(field => ({
                        Candidate_Id: lastInsertedId,
                        Lang_Known: field.langKnown,
                        Lang_Spoken: field.langSpoken,
                        Lang_Read_Write: field.langReadWrite,
                        Lang_Proficiency: field.langProficiency
                    }));
                    candidateLanguageList.push(...canidateLangKnown);
                }

                if (candidate.preferredLocation && candidate.preferredLocation.length > 0) {
                    /**candidate's preferred location */
                    const candidatePreferredLocation = candidate.preferredLocation
                    .filter(field => !!field)
                    .map(field => ({
                        Candidate_Id: lastInsertedId,
                        Preferred_Location: field
                    }));
                    candidatePreferredLocationList.push(...candidatePreferredLocation);
                }

                if(candidate.candidateEducation && candidate.candidateEducation.length > 0){
                    const candidateEducation = candidate.candidateEducation.map(field => ({
                        Candidate_Id: lastInsertedId,
                        Education_Type: field.educationType,
                        Specialisation: field.speacialisation,
                        Specialization_Id:  field.specializationId,
                        Institute_Name: field.institute,
                        Institution_Id: field.institutionId,
                        Year_Of_Start: field.yearOfStart,
                        Year_Of_passing: field.yearOfPassing,
                        Percentage: field.percentage,
                        Grade:  field.grade,
                        Start_Date: field.startDate,
                        End_Date: field.endDate,
                        City: field.city,
                        State: field.state,
                        Country: field.country
                    }));
                    candidateEducationList.push(...candidateEducation);
                }

                if (candidate.verifierName || candidate.verifierPhoneNo || candidate.verifierEmailId) {
                    candidateReferenceList.push({
                        Candidate_Id: lastInsertedId,
                        Verifier_Name: candidate.verifierName,
                        Verifier_Phone_Number: candidate.verifierPhoneNo,
                        Verifier_Email_Id: candidate.verifierEmailId
                    })
                }

                if (candidate.candidateCertification && candidate.candidateCertification.length > 0) {
                    /**candidate's certification details */
                    const candidateCertification = candidate.candidateCertification.filter(field => field.certificationName)
                    .map(
                        field => ({
                            Candidate_Id: lastInsertedId,
                            Certification_Name: field.certificationName,
                            Received_Date: field.receivedDate ? field.receivedDate : null,
                            Certificate_Received_From: field.certificateReceivedFrom ? field.certificateReceivedFrom : null,
                            Ranking: field.ranking ? field.ranking : null
                        })
                    );
                    candidateCertificationList.push(...candidateCertification);
                }

                if (candidate.dataPrivacyStatement) {
                    dataPrivacyStatementResponses.push({
                        Primary_Id: lastInsertedId,
                        Form_Id: candidate.formId || 16,
                        User_Ip: context.User_Ip || null,
                        Accepted_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                    })
                }

                if(candidate.candidateExperience &&  candidate.candidateExperience.length){

                    candidateExperienceList = [];
                    const candidateExperienceDetails = candidate.candidateExperience
                    .map(({companyName, companyLocation, designation, startDate, endDate, duration, years, months}) => {

                        const expStartDate = moment(startDate), expEndDate = moment(endDate);
                        const expYears = expEndDate.diff(expStartDate, 'years');
                        const expMonths = expEndDate.diff(expStartDate.clone().add(expYears, 'years'), 'months');
                        const expDays = expEndDate.diff(expStartDate.clone().add(expYears, 'years').add(expMonths, 'months'), 'days');
                        
                        return {
                            Candidate_Id: lastInsertedId,
                            Prev_Company_Name: companyName,
                            Prev_Company_Location: companyLocation,
                            Designation: designation,
                            Start_Date: startDate,
                            End_Date: endDate,
                            Duration: duration ? duration : `${expYears ? expYears + ' Years ' : ''}${expMonths ? expMonths + ' Months ' : ''}${expDays ? expDays + ' Days' : ''}`.trim(),
                            Years: years || expYears,
                            Months: months || expMonths
                        }
                    });
                    candidateExperienceList.push(...candidateExperienceDetails);

                   const [insertResult]  = await organizationDbConnection('candidate_experience')
                    .insert(candidateExperienceList).transacting(trx);

                    let candidateReferenceDetails = (candidate.candidateExperience || []).map((exp, index) => {
                        const validRefs = Array.isArray(exp.referenceDetails)
                            ? exp.referenceDetails.filter(ref =>
                                ref.Reference_Name && ref.Reference_Email && ref.Reference_Number)
                            : [];
                    
                        return validRefs.map(ref => ({
                            Experience_Id: (insertResult + index),
                            Reference_Name: ref.Reference_Name,
                            Reference_Email: ref.Reference_Email,
                            Reference_Number: ref.Reference_Number
                        }));
                    });
                    
                    candidateReferenceDetails = candidateReferenceDetails.flat();

                    if (candidateReferenceDetails.length) {
                        await organizationDbConnection('candidate_experience_reference')
                            .insert(candidateReferenceDetails)
                            .transacting(trx);
                    }
                    candidateExperienceList = [];

                } 
                
                if (candidate.candidateWorkPermit && candidate.candidateWorkPermit.length > 0) {
                    for (var i in candidate.candidateWorkPermit) {
                        if(candidate.candidateWorkPermit[i]){
                            workAuthorizationList.push({
                                Candidate_Id : lastInsertedId,
                                Work_Permits: candidate.candidateWorkPermit[i],
                                Other_Work_Permit: candidate.candidateWorkPermit[i] === 3 ? candidate.candidateOtherWorkPermit : null
                            });
                        }
                    }
                }


                if (candidate.candidateDependent && candidate.candidateDependent.length > 0) {
                    const candidateDependentDetails = candidate.candidateDependent
                    .filter((dep) => !!dep).map((dep, i) => ({
                        Candidate_Id: lastInsertedId,
                        Dependent_First_Name: dep,
                        Dependent_Last_Name: '',
                        Gender_Id: i === 0 ? 1 : 0,
                        Gender: i === 0 ? 'Male' : 'Female',
                        Relationship: i === 0 ? 'Father' : 'Mother',
                        Dependent_DOB: null
                    }));
                    candidateDependentList.push(...candidateDependentDetails);
                }

                if (candidate.customFieldInputs && candidate.customFieldInputs.Field_Value) {
                    candidateCustomFieldList.push({
                        Primary_Id: lastInsertedId,
                        Form_Id:  formId.jobCandidate,
                        Field_Value: JSON.stringify(candidate.customFieldInputs.Field_Value),
                        Updated_By: context.Employee_Id,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                    });
                }

            }));

            // Step 1: Retrieve dynamic fields data table name
            let dynamicFieldsDataTable = await organizationDbConnection('custom_field_tables').transacting(trx)
            .where('Form_Id', formId.jobCandidate).first();

            dynamicFieldsDataTable = dynamicFieldsDataTable ? dynamicFieldsDataTable.Table_Name : null;

            await Promise.all([
                candidateRecruitmentList.length ?  organizationDbConnection(ehrTables.candidateRecruitmentInfo).insert(candidateRecruitmentList).transacting(trx) : null,
                candidateLanguageList.length ? organizationDbConnection('candidate_language').insert(candidateLanguageList).transacting(trx) : null,
                candidateContactList.length ? organizationDbConnection('candidate_contact_details').insert(candidateContactList).transacting(trx) : null,
                candidatePassportList.length ? organizationDbConnection('candidate_passport').insert(candidatePassportList).transacting(trx) : null,
                candidateEducationList.length ? organizationDbConnection('candidate_education').insert(candidateEducationList).transacting(trx) : null,
                candidatePreferredLocationList.length ? organizationDbConnection('candidate_prefered_job_location').insert(candidatePreferredLocationList).transacting(trx) : null,
                candidateReferenceList.length ? organizationDbConnection('candidate_reference').insert(candidateReferenceList).transacting(trx) : null,
                candidateCertificationList.length ? organizationDbConnection('candidate_certifications').insert(candidateCertificationList).transacting(trx) : null,
                dataPrivacyStatementResponses.length ? organizationDbConnection('data_privacy_statement_responses').insert(dataPrivacyStatementResponses).transacting(trx) : null,
                workAuthorizationList.length ? organizationDbConnection('candidate_work_permit').insert(workAuthorizationList).transacting(trx) : null,
                candidateDependentList.length ? organizationDbConnection('candidate_dependent').insert(candidateDependentList).transacting(trx) : null,
                candidateCustomFieldList.length && dynamicFieldsDataTable ? organizationDbConnection(dynamicFieldsDataTable).insert(candidateCustomFieldList).transacting(trx) : null,
            ]);

            candidateRecruitmentList = [], candidateLanguageList = [], candidateContactList = [], candidatePassportList = [], candidateEducationList = [], 
            candidatePreferredLocationList = [], candidateReferenceList = [], candidateCertificationList = [], dataPrivacyStatementResponses = [], 
            candidateExperienceList=[], workAuthorizationList = [], candidateDependentList = [], candidateCustomFieldList = [];
        
            const groupedCandidates = args.candidateList.reduce((acc, candidate) => {
                // Create a unique key based on firstName, middleName, lastName, email, and mobileNo
                const key = `${candidate.firstName}|${candidate.middleName}|${candidate.lastName}|${candidate.emailId}|${candidate.mobileNo}|${candidate.dob}`;
                if (!acc[key]) {
                    acc[key] = { 
                        firstName: candidate.firstName,
                        middleName: candidate.middleName,
                        lastName: candidate.lastName,
                        emailId: candidate.emailId,
                        mobileNo: candidate.mobileNo,
                        dob: candidate.dob
                    };
                }
                return acc;
            }, {});

            const candidateEmailList = [...new Set(Object.values(groupedCandidates).map(c => c.emailId))];

            const candidateDetailsList = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
            .transacting(trx).distinct('CPI.Candidate_Id', 'CPI.Personal_Email', 'CRI.Blacklisted', 'CRI.Blacklisted_On', 'CRI.Blacklisted_By', 'CRI.Blacklisted_Reason_Id', 
                'CRI.Blacklisted_Comments', 'CRI.Blacklisted_Attachment_File_Name', 'CRI.Portal_Access_Enabeld')
            .join(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
            .whereIn('CPI.Personal_Email', candidateEmailList);

            const candidateCountDetails = await organizationDbConnection(`${ehrTables.candidatePersonalInfo} as CPI`)
            .transacting(trx)
            .join(ehrTables.candidateContactDetails + ' as CCD', 'CCD.Candidate_Id', 'CPI.Candidate_Id')
            .select(
                organizationDbConnection.raw('GROUP_CONCAT(CPI.Candidate_Id) as Candidate_Ids'),
                organizationDbConnection.raw('COUNT(*) as Duplicate_Counts')
            )
            .whereIn('CPI.Candidate_Id', candidateDetailsList.map(c => c.Candidate_Id))
            .groupBy('CPI.Emp_First_Name', 'CPI.Emp_Middle_Name', 'CPI.Emp_Last_Name', 'CPI.Personal_Email', 'CPI.DOB', 'CCD.Mobile_No');

            const duplicateUpdates = candidateCountDetails.map(candidate => 
                organizationDbConnection(ehrTables.candidatePersonalInfo).transacting(trx)
                .update({
                    Is_Duplicate: candidate.Duplicate_Counts ? 1 : null,
                    Duplicate_Count: candidate.Duplicate_Counts
                })
                .whereIn('Candidate_Id', candidate.Candidate_Ids.split(','))
            );

            const candidatePortalAccessDetails = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
            .distinct('CPI.Candidate_Id', 'CPI.Personal_Email', 'CRI.Portal_Access_Enabeld')
            .join(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
            .whereIn('CPI.Personal_Email', candidateEmailList);

            let organizationPortalAccess = await organizationDbConnection('recruitment_settings').transacting(trx)
            .select('Candidate_Portal_Login_Access').first();

            organizationPortalAccess = organizationPortalAccess?.Candidate_Portal_Login_Access.toLowerCase() === 'yes' ? true : false;

            const recruitmentUpdates = candidateEmailList.map(async email => {
                const candidateDetails = candidateDetailsList.filter(c => c.Personal_Email === email);
                const portalAccessDetails = candidatePortalAccessDetails.filter(c => c.Personal_Email === email);
                if (!candidateDetails.length) return null;
                
                const isPortalAccessEnabled = !portalAccessDetails.some(c => c?.Portal_Access_Enabeld?.toLowerCase() === "no");
                const blacklistedDetail = candidateDetails.filter(c => c.Blacklisted && c.Blacklisted.toLowerCase() === "yes")[0];

                return organizationDbConnection(ehrTables.candidateRecruitmentInfo).transacting(trx)
                .update({
                    Portal_Access_Enabeld: organizationPortalAccess && isPortalAccessEnabled ? 'Yes' : 'No',
                    Portal_Access_Passcode: null,
                    Passcode_Expire_Time: null,
                    Blacklisted: blacklistedDetail ? blacklistedDetail.Blacklisted : 'No',
                    Blacklisted_On: blacklistedDetail ? blacklistedDetail.Blacklisted_On : null,
                    Blacklisted_By: blacklistedDetail ? blacklistedDetail.Blacklisted_By : null,
                    Blacklisted_Reason_Id: blacklistedDetail ? blacklistedDetail.Blacklisted_Reason_Id : null,
                    Blacklisted_Comments: blacklistedDetail ? blacklistedDetail.Blacklisted_Comments : null,
                    Blacklisted_Attachment_File_Name: blacklistedDetail ? blacklistedDetail.Blacklisted_Attachment_File_Name : null
                }).whereIn('Candidate_Id', candidateDetails.map(c => c.Candidate_Id));
            })

            await Promise.all([...duplicateUpdates, ...recruitmentUpdates]);
            
        });

        organizationDbConnection ? organizationDbConnection.destroy() : null;
        return {errorCode:'', message: 'Candidate bulk uploaded successfully.'};

    }catch(error){

        console.error('Error in uploadBulkCandidateDetails function main catch block.', error);
        let errResult = commonLib.func.getError(error, 'EI00209');
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if(error && error === 'IVE0000'){
            throw new UserInputError(errResult.message, { validationError: validationErrorList });
        }
        throw new ApolloError(errResult.message, errResult.code);
    }

}

function jobCandidatesValidation(args) {
    const validationErrors = {};

    const validations = [
        { name: 'firstName', rule: 'empFirstName' },
        { name: 'lastName', rule: 'empLastName' },
        { name: 'emailId', rule: 'email' },
        { name: 'mobileNo', rule: 'phone' }
    ];

    if (args.dob) {
        if (new Date(args.dob) > new Date()) {
            validationErrors['dob -' + args.rowNo] = 'Date of birth should be 15 years before.';
        }
    }

    if (args.nationality) validations.push({ name: 'nationality', rule: 'nationality' });
    if (args.apartmentName) validations.push({ name: 'apartmentName', rule: 'apartmentName' });
    if (args.street) validations.push({ name: 'street', rule: 'street' });
    if (args.city) validations.push({ name: 'city', rule: 'city' });
    if (args.state) validations.push({ name: 'state', rule: 'state' });
    if (args.pincode) validations.push({ name: 'pincode', rule: 'pinCode' });
    if (args.verifierName) validations.push({ name: 'verifierName', rule: 'empLastName' });
    if (args.verifierEmailId) validations.push({ name: 'verifierEmailId', rule: 'email' });
    if (args.verifierPhoneNo) validations.push({ name: 'verifierPhoneNo', rule: 'phone' });
    if (args.currentEmployer) validations.push({ name: 'currentEmployer', rule: 'currentEmployer' });

    if (args.currentCTC || args.expectedCTC) {
        if (!args.currency) validationErrors['currency -' + args.rowNo] = 'Please select currency';
    }

    validations.forEach(({ name, rule }) => {
        const validation = validateWithRulesAndReturnMessages(args[name], rule, name);
        if (validation !== 'Validation not found' && validation !== true) {
            validationErrors[name + ' - ' + args.rowNo] = validation;
        }
    });

   
    if (args.candidateEducation && args.candidateEducation.length > 0 ) {
        validateEduDetails(args, validationErrors);
    }

    if (args.candidateExperience && args.candidateExperience.length > 0) {
        validateExpDetails(args, validationErrors)
    }

    if (args.candidateCertification && args.candidateCertification.length > 0) {
        validateCerDetails(args, validationErrors)
    }

    return validationErrors;
}

function validateEduDetails (args, validationErrors){ 
    

    return args.candidateEducation.some((education, index) => {
        const errors = [];
        if (!education.educationType) errors.push('educationType-' +(index + 1) + ': Education Type is required.');
        if (education.speacialisation && !validateWithRulesAndReturnMessages(education.speacialisation, 'specialisation')) errors.push('speacialisation-'+ (index + 1) + ': Specialisation is invalid.');
        if (education.institute && !validateWithRulesAndReturnMessages(education.institute, 'instituteName')) errors.push('institute-'+ (index + 1) + ': Institute name is invalid.');
        if (args.dob && education.yearOfStart && new Date(args.dob).getFullYear() > parseInt(education.yearOfStart, 10)) errors.push('yearOfStart-' + (index + 1) + ': Year of Start cannot be before Date of Birth.');
        else if (education.yearOfStart && education.yearOfPassing && parseInt(education.yearOfStart, 10) > parseInt(education.yearOfPassing, 10)) errors.push('yearOfStart-' + (index + 1) + ': Year of Start cannot be greater than Year of Passing.');
        if (education.percentage !== undefined && (education.percentage < 0 || education.percentage > 100)) errors.push('percentage-' + (index + 1) + ': Percentage must be between 0 and 100.');
        if (errors.length > 0) {
            errors.forEach(error => validationErrors['Education-'+args.rowNo +':'+ error.split(':')[0]] = error.split(':')[1].trim());
            return true;
        }
        return false;
    });
}



function validateExpDetails (args, validationErrors) { 
    return args.candidateExperience.some(({ companyName, designation, startDate, endDate }, index) => {
        const errors = [];
        if (companyName && !validateWithRulesAndReturnMessages(companyName, 'companyName')) errors.push('companyName-'+ (index + 1) + ': CompanyName is invalid.');
        if (designation && !validateWithRulesAndReturnMessages(designation, 'designation')) errors.push('designation-'+ (index + 1) + ': Designation is invalid.');
        if (startDate) {
            if (new Date(args.dob) > new Date(startDate)) errors.push('startDate-'+ (index + 1) + ': StartDate is less than Date of Birth.');
            if (endDate && new Date(endDate) < new Date(startDate)) errors.push('endDate-'+ (index + 1) + ': EndDate is less than StartDate.');
        }
        if (errors.length > 0) {
            errors.forEach(error => validationErrors[`Experience-${args.rowNo}:${error.split(':')[0]}`] = error.split(':')[1].trim());
            return true;
        }
        return false;
    });
}


function validateCerDetails(args, validationErrors) {
    return args.candidateCertification.some( ({certificationName, receivedDate, certificateReceivedFrom}, index) => {
        const errors = [];
        if(certificationName && !validateWithRulesAndReturnMessages(certificationName, 'certificationName'))  errors.push('certificationName-'+ (index + 1) + ': Certification Name is invalid.');
        if(certificateReceivedFrom && !validateWithRulesAndReturnMessages(certificateReceivedFrom, 'receivedFrom'))  errors.push('receivedFrom-'+ (index + 1) + ': ReceivedFrom is invalid.');
        const receivedDateValidation = receivedDate ? (new Date(receivedDate) / 1000) > (Date.now() / 1000) : false;
        if (receivedDateValidation) errors.push('receivedDate-'+ (index + 1) + ': ReceivedDate is greater than current date.');
        if (errors.length > 0) {
            errors.forEach(error => validationErrors[`Caertificate-${args.rowNo}:${error.split(':')[0]}`] = error.split(':')[1].trim());
            return true;
        }
        return false;
    })
}
