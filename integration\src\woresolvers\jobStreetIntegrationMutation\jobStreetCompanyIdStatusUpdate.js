const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const { checkDeletionEligibility } = require('../../common/commonFunction');

module.exports.jobStreetCompanyIdStatusUpdate = async (parent, args, context) => {
  let validationError = {};
  const { Employee_Id: loginEmployeeId, User_Ip: userIp } = context;
  let organizationDbConnection;
  
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);
    
    let checkRightsForForm = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection, 
      loginEmployeeId, 
      null, 
      '', 
      "UI", 
      false, 
      args.formId
    );
    
    if (Object.keys(checkRightsForForm).length > 0 && 
        (checkRightsForForm.Role_Add === 1 || checkRightsForForm.Role_Update === 1) && 
        checkRightsForForm.Employee_Role.toLowerCase() === 'admin') {
      await checkDeletionEligibility(organizationDbConnection, args.seekHirerId);
      await organizationDbConnection(ehrTables.seekHirerList)
        .update({
          Status: args.status
        })
        .where('Hirer_List_Id', args.seekHirerId);
      
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Hirer list details for hired-id ${args.seekHirerId} updated`
      });

      return {
        errorCode: '',
        message: 'The hirer list details have been successfully updated.'
      };
      
    } else {
      if (Object.keys(checkRightsForForm).length === 0 || (checkRightsForForm.Role_Update !== 1)) {
        console.log('No rights to update the recruitment integration Status');
        throw '_DB0111';
      } else {
        throw '_DB0109';
      }
    }
    
  } catch (error) {
    console.error('Error in jobStreetCompanyIdStatusUpdate function main catch block.', error);
    const errResult = commonLib.func.getError(error, 'EI00217');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};
