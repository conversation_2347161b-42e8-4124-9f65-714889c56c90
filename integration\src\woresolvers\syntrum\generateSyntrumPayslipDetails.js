const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const { generateSyntrumPayslip } = require('./generateSyntrumPayslip');

module.exports.generateSyntrumPayslipDetails = async (parent, args, context) => {
    let organizationDbConnection;
    try {
        const accessFormId = args.formId || formId.myPayslip;
        const { Employee_Id: employeeId, connection } = context;

        organizationDbConnection = knex(connection.OrganizationDb);

        // STEP 1: Check employee access rights
        const hasAccess = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, employeeId, null, 'Role_View', 'Back-end', null, accessFormId
        );
        if (!hasAccess) throw '_DB0100';

        const response = await generateSyntrumPayslip(organizationDbConnection, args, context, 'ui');

        return response;

    } catch (e) {
        console.error('Error in generateSyntrumPayslipDetails:', e);
        const errResult = commonLib.func.getError(e, 'SYN0019');
        throw new ApolloError(errResult.message, errResult.code, { actualError: e?.message ? e.message : null });
    } finally {
        if (organizationDbConnection) await organizationDbConnection.destroy();
    }
};