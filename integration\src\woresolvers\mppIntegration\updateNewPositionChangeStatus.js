// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
// Require Apollo Server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');
const moment = require('moment-timezone');

module.exports.updateNewPositionChangeStatus = async (parent, args, context, info) => {
    console.log("Inside updateNewPositionChangeStatus function.");
    let organizationDbConnection;
    let validationError = {};

    try {
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check employee access rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, employeeId, '', '', 'UI', false, formId.toStatusApproval
        );

        if (Object.entries(checkRights).length > 0 && checkRights.Role_Update === 1) {

            if (!args.status) {
                validationError['ERR-001'] = "Status is required";
            } else if (args.comments && (args.comments.length < 3 || args.comments.length > 500)) {
                validationError['ERR-002'] = "Comments should be minimum 3 characters to maximum 500 characters";
            }

            if (Object.keys(validationError).length > 0) {
                throw 'IVE0000';
            }

            let changeStatus = {};

            if (args.status.toLowerCase() === 'to in review') {

                // Start transaction
                return await organizationDbConnection.transaction(async (trx) => {
                    let result = await trx(ehrTables.mppPositionRequest)
                        .select('Status')
                        .where('Position_Request_Id', args.positionRequestId)
                        .andWhere('Status', 'Approved')
                        .first();

                    if (!result) {
                        throw 'EI00190';
                    }

                    changeStatus = {
                        TO_In_Review_Comment: args.comments,
                        Status: args.status,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                        Updated_By: employeeId
                    };

                    await trx(ehrTables.mppPositionRequest)
                        .update(changeStatus)
                        .where('Position_Request_Id', args.positionRequestId);
                    return { errorCode: "", message: "MPP new position change status updated successfully." };
                });

            } else if (args.status.toLowerCase() === 'to changes approved' || args.status.toLowerCase() === 'to changes rejected') {

                // Start transaction
                return await organizationDbConnection.transaction(async (trx) => {
                    let result = await trx(ehrTables.mppPositionRequest)
                        .select('Status')
                        .where('Position_Request_Id', args.positionRequestId)
                        .andWhere('Status', 'TO In Review')
                        .first();

                    if (!result) {
                        throw args.status.toLowerCase() === 'to changes approved' ? 'EI00191' : 'EI00192';
                    }

                    changeStatus = {
                        Status: args.status,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                        Updated_By: employeeId
                    };

                    if (args.status.toLowerCase() === 'to changes approved') {
                        changeStatus['TO_Approval_Comment'] = args.comments;
                    } else {
                        changeStatus['TO_Rejection_Comment'] = args.comments;
                    }

                    let [positionRequest, orgStructureIds, workingCondition, educationDescription] = 
                    await  Promise.all([
                        organizationDbConnection(ehrTables.mppPositionRequest).transacting(trx)
                        .update(changeStatus).where('Position_Request_Id', args.positionRequestId),

                        organizationDbConnection(ehrTables.SFWPOrganizationStructure + ' as SFWP').transacting(trx)
                        .select('SFWP.Organization_Structure_Id', 'MPR.Internal_Operating_Network', 'MPR.External_Operating_Network', 
                            'MPR.License_Certificate', 'MPR.License_Certificate_Details', 'MPR.Comments')
                        .leftJoin(ehrTables.mppPositionRequest + ' as MPR', 'MPR.Organization_Structure_Id', 'SFWP.Organization_Structure_Id')
                        .where('MPR.Position_Request_Id', args.positionRequestId),
                        //.whereNot('SFWP.Status', 'Active'),

                        organizationDbConnection(ehrTables.mppWorkingConditions).transacting(trx)
                        .select(organizationDbConnection.raw("CONCAT_WS('-', Working_Area, Time_Spent) as Work_Condition"))
                        .where('Position_Request_Id', args.positionRequestId),

                        organizationDbConnection(ehrTables.mppEducationRequirementsDescriptions).transacting(trx)
                        .select('Description').where('Position_Request_Id', args.positionRequestId),
                    ]);

                    workingCondition = workingCondition ? workingCondition.map(item => item.Work_Condition).join(",") : '';
                    educationDescription = educationDescription ? educationDescription.map(item => item.Description).join(",") : '';

                    await trx(ehrTables.SFWPOrganizationStructure)
                        .update({ Status: 'TO Changes Approved' })
                        .whereIn('Organization_Structure_Id', orgStructureIds.map(row => row.Organization_Structure_Id));
                      
                    await trx(ehrTables.sfwpJobDescriptionMaster)
                    .update({ 
                        Job_Description: orgStructureIds[0]?.Comments || null,
                        Education: educationDescription ? educationDescription : null,
                        Working_Conditions: workingCondition ? workingCondition : null,
                        Working_Relationship_Internal: orgStructureIds[0]?.Internal_Operating_Network ? orgStructureIds[0]?.Internal_Operating_Network : null,
                        Working_Relationship_External: orgStructureIds[0]?.External_Operating_Network ? orgStructureIds[0]?.External_Operating_Network : null,
                    })
                    .where('Organization_Structure_Id', orgStructureIds[0]?.Organization_Structure_Id || 0);
                    

                    if (organizationDbConnection) organizationDbConnection.destroy();
                    return { errorCode: "", message: "MPP new position change status updated successfully." };
                });

            } else {
                throw 'EI00193'; // No Status
            }

        } else {
            throw '_DB0102';
        }
    } catch (err) {
        // Destroy DB connection
        console.error('Error in updateNewPositionChangeStatus function main catch block.', err);
        if (organizationDbConnection) organizationDbConnection.destroy();
        
        let errResult = commonLib.func.getError(err, '_UH0001');
        if (err === 'IVE0000') {
            console.error('Validation error in updateNewPositionChangeStatus function - ', validationError);
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}