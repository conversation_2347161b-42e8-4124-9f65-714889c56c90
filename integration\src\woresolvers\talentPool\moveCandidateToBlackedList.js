// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formId} = require('../../../common/appConstants');
const { validatCommonRuleInput, getDuplicateRecords } = require('../../common/commonFunction');
const { ehrTables } = require('../../../common/tablealias');
//Require moment
const moment = require('moment-timezone');

module.exports.moveCandidateToBlackedList = async (parent, args, context, info) => {
    console.log('Inside moveCandidateToBlackedList function');
    // Get the currently logged in employee's id
    const loginEmployeeId = context.Employee_Id;
    let organizationDbConnection, validationError = {};
    try {
        // Establish a connection to the database
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check if the user has the access rights to update the candidate
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, 
            loginEmployeeId, 
            null, 
            '', 
            'UI', 
            false, 
            formId.jobCandidate
        );
        if (!checkRights || checkRights.Role_Update === 0) {
            // Throw an error if the user does not have the rights
            throw '_DB0102';
        }

        // Validate inputs
        let fieldValidations= {}
        // Validate the archive comment
        if (args.blackedComment) 
            fieldValidations.blackedComment = 'IVE0499';

        // Validate the archive reason
        if (!args.blackedReasonId) 
            validationError['IVE0001'] = 'Blacklisted reason is required';
        
        // Validate other fields
        validationError = await validatCommonRuleInput(args, fieldValidations);
        // If there are any errors, throw an error
        if (Object.keys(validationError).length > 0) {
            throw 'IVE0000';
        }

        // Get the candidate details
        const candidate = await organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
            .select('CPI.Candidate_Id', 'CPI.Emp_First_Name', 'CPI.Emp_Last_Name', 'CPI.Emp_Middle_Name', 'CPI.DOB as dob',  
                'CPI.Personal_Email as emailId', 'CRI.Blacklisted', 'ST.Status', 'CCD.Mobile_No as mobileNo')
            .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
            .leftJoin(ehrTables.atsStatusTable + ' as ST', 'ST.Id', 'CRI.Candidate_Status')
            .leftJoin(ehrTables.candidateContactDetails + ' as CCD', 'CCD.Candidate_Id', 'CPI.Candidate_Id')
            .where('CPI.Candidate_Id', args.candidateId).first();

        // If the candidate is not found, throw an error
        if (!candidate) {
            throw 'EI00224';  //Sorry! An error occurred while processing the candidate details. Please try again.
        }

        // If the candidate is already blocked, throw an error
        if (candidate?.Blacklisted?.toLowerCase() === 'yes') {
            throw  "EI00225" ; 
        }

        // If the candidate is already onboarded, throw an error
        if (candidate?.Status?.toLowerCase() === "onboarded") {
            throw "TAP0115" ;  //Apologies! The candidate is currently in an onboarded status, so archiving the data is not permitted.
        }

        // Concatenate the candidate's first, middle and last name
        const concatenatedcurrentDetails = [
            candidate?.Emp_First_Name?.trim(), 
            candidate?.Emp_Middle_Name?.trim(), 
            candidate?.Emp_Last_Name?.trim()
        ].filter(Boolean).join(' ');

        // Start a transaction
        return await organizationDbConnection.transaction(async (trx) => {

            // Get all duplicate candidates
            const duplicateCandidate = await getDuplicateRecords(organizationDbConnection, candidate, concatenatedcurrentDetails, trx, true);

            // Get the list of candidate ids to be blocked
            let candidateIds = [args.candidateId];
            if(duplicateCandidate && duplicateCandidate.length > 0) {
                candidateIds = duplicateCandidate.map(item =>  item.Candidate_Id)
            }

            // Update the candidate recruitment info table to block the candidate
            await organizationDbConnection(ehrTables.candidateRecruitmentInfo).transacting(trx)
            .update({
                Blacklisted: 'Yes',
                Blacklisted_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                Blacklisted_By: loginEmployeeId,
                Blacklisted_Reason_Id: args.blackedReasonId,
                Blacklisted_Comments: args.blackedComment,
                Blacklisted_Attachment_File_Name: args.blackedAttachment && args.blackedAttachment.length ? JSON.stringify(args.blackedAttachment) : null,
                Portal_Access_Passcode: null,
                Passcode_Expire_Time: null,
            })
            .whereIn('Candidate_Id', candidateIds)


            const systemLogParam = candidateIds.map(candidateId=> {
                return {
                    userIp: context.User_Ip,
                    employeeId: loginEmployeeId,
                    changedData: args,
                    organizationDbConnection: trx,
                    formId: formId.jobCandidate,
                    action: `Moved Blacklist`,
                    isEmployeeTimeZone: 0,
                    uniqueId: candidateId,
                    message: `The candidate has been moved to the blacklist.`
                }
            });

             await commonLib.func.createMultiSystemLogActivities(systemLogParam);

        }).then(async () => {

            // Return the success response
            return { errorCode: "", message: "The candidate have been blacklisted successfully" };
        })

    } catch (error) {
        console.log('Error in moveCandidateToBlackedList function main catch block:', error);
        const errResult = commonLib.func.getError(error, 'EI00226');
        // Handle different error cases
        if (error === 'IVE0000') 
            throw new UserInputError(errResult.message, { validationError });
       
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
};

