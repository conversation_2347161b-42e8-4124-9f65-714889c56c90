const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require table names
const { ehrTables } = require('../../../common/tablealias');
//Require constants
const { formId,syntrumValues } = require('../../../common/appConstants')
//Require moment
const moment = require('moment');

//Function to get the leave details
async function getEmployeeLeaveDetails(organizationDbConnection,orgCode,syntrumInputs){
    let { args,action } = syntrumInputs;
    let { uniqueIds,elementHeader, uniqueIdDetails } = args;
    try {
        let processedLeaves = [];
        let leaveDetails = [];

        action = action.toLowerCase();

        if(uniqueIdDetails){
            uniqueIdDetails = JSON.parse(uniqueIdDetails);
            //Get the leave type and employee details
            let employeeDetails = await organizationDbConnection(ehrTables.empEligibleLeaves + " as EL")
                    .select('EL.Leaves_Taken','EJ.User_Defined_EmpId',organizationDbConnection.raw('CAST(EJ2.User_Defined_EmpId AS CHAR) AS CreateBy'),
                    'LT.Leave_Calculation_Days')
                    .innerJoin(ehrTables.empJob + " as EJ", "EL.Employee_Id", "EJ.Employee_Id")
                    .leftJoin(ehrTables.empJob + " as EJ2", function () {
                        this.on('EJ2.Employee_Id', '=', organizationDbConnection.raw('?', [uniqueIdDetails['Added_By']]));
                    })
                    .innerJoin(ehrTables.leaveTypes + ' as LT', 'EL.LeaveType_Id', 'LT.LeaveType_Id')
                    .where('EL.LeaveType_Id',uniqueIdDetails['LeaveType_Id'])
                    .where('EL.Employee_Id',uniqueIdDetails['Employee_Id'])
                    .groupBy('EL.Employee_Id')
                    .then();
            if(employeeDetails && employeeDetails.length){
                leaveDetails = [{...employeeDetails[0],...uniqueIdDetails}];
            }else{
                console.log('Empty employee details',employeeDetails,uniqueIdDetails);
            }
        }else{
            //Get the leave, leave type and employee details from the leave id
            leaveDetails = await organizationDbConnection(ehrTables.empLeaves + " as L")
                    .select('L.Total_Days','EL.Leaves_Taken','EJ.User_Defined_EmpId',organizationDbConnection.raw('CAST(EJ2.User_Defined_EmpId AS CHAR) AS CreateBy'),
                    'L.Start_Date as FromDate', 'L.End_Date as ToDate','LT.Leave_Calculation_Days','L.LeaveType_Id','L.Employee_Id')
                    .innerJoin(ehrTables.empJob + " as EJ", "L.Employee_Id", "EJ.Employee_Id")
                    .innerJoin(ehrTables.empEligibleLeaves + " as EL", function() {
                        this.on('EL.LeaveType_Id', '=', 'L.LeaveType_Id')
                            .on('EL.Employee_Id', '=', 'L.Employee_Id')
                    })
                    .leftJoin(ehrTables.empJob + " as EJ2", "EJ2.Employee_Id", "EJ.Added_By")
                    .innerJoin(ehrTables.leaveTypes + ' as LT', 'L.LeaveType_Id', 'LT.LeaveType_Id')
                    .whereIn('L.Leave_Id', uniqueIds)
                    .groupBy('L.Leave_Id')
                    .then();     
        }
        if(leaveDetails && leaveDetails.length){
            let allLeaveDates = [];
            let allLeaveTypeIds = [];
            leaveDetails.map((l => {
                allLeaveDates.push(l.FromDate);
                allLeaveDates.push(l.ToDate);
                allLeaveTypeIds.push(l.LeaveType_Id);
            }));

            //Get the leave start date and end date - salary month list
            let { minDate, maxDate} = await commonLib.dateFilter.findMinMaxDates(allLeaveDates);
            let allSalaryMonthsDateRange = await commonLib.func.getsalaryMonthDateList(orgCode,formId.leaves,minDate,maxDate);
            let leavePaymentSlab = await commonLib.employees.getLeavePaymentSlabs(organizationDbConnection, allLeaveTypeIds);
            //Iterate the leave records and form the integration inputs
            for (const leave of leaveDetails) {
                let currentLeavesTaken = 0;
                let fromDate = leave.FromDate;
                let toDate = leave.ToDate;
                let momentToDate = moment(toDate);
                let startDateMonthList = await commonLib.dateFilter.findDateRange(allSalaryMonthsDateRange, fromDate);
                //If leave from and leave to date fall in the same month
                if(momentToDate >= moment(startDateMonthList['start_date']) && momentToDate <= moment(startDateMonthList['end_date'])){
                    let otherInputs = { 
                        elementHeader, 
                        startDate: fromDate, 
                        endDate: toDate,  action,
                        leaveRequestDays: leave.Total_Days,
                        employeeLeaveDetails: leave,
                        startDateMonthList,
                        currentTotalUnit: currentLeavesTaken
                    };
                    let  { leaveDetailsResult } = await handleLeaveSlabProcess(organizationDbConnection, leavePaymentSlab,otherInputs);
                    processedLeaves = processedLeaves.concat(leaveDetailsResult);
                }else{
                    //If leave from and leave to date falls in the different month
                    while (moment(fromDate).isSameOrBefore(moment(toDate), 'day')) {
                        startDateMonthList = await commonLib.dateFilter.findDateRange(allSalaryMonthsDateRange, fromDate);

                        let lastDayOfMonth = moment(fromDate).endOf('month').format('YYYY-MM-DD');
                        //If last day of month is greater than the leave to date
                        if(moment(lastDayOfMonth) > momentToDate){
                            lastDayOfMonth = toDate;
                        }
    
                        //For each month calculate the leave total days based ont he leave claculation days
                        let totalDays = await commonLib.employees.getTotalDaysBasedOnLeaveCalculationDays(organizationDbConnection,leave.Employee_Id,leave,fromDate,lastDayOfMonth);

                        let otherInputs = { 
                            elementHeader, 
                            startDate: fromDate, 
                            endDate: lastDayOfMonth,
                            action,
                            leaveRequestDays: totalDays,
                            employeeLeaveDetails: leave,
                            startDateMonthList,
                            currentTotalUnit: currentLeavesTaken
                        };
                        let { leaveDetailsResult, currentTotalUnit} = await handleLeaveSlabProcess(organizationDbConnection, leavePaymentSlab,otherInputs);
                        currentLeavesTaken = currentTotalUnit;
                        processedLeaves = processedLeaves.concat(leaveDetailsResult);
                       
                        fromDate = moment(lastDayOfMonth).add(1, 'day').format('YYYY-MM-DD'); // Move to next month's start
                    }
                }
            }
        }else{
            console.log("Empty leave details in getEmployeeLeaveDetails function",leaveDetails,uniqueIds,uniqueIdDetails);
        }
        console.log("Leave to be processed.",processedLeaves)
        return processedLeaves;
    } catch (error) {
        console.log('Error in the getEmployeeLeaveDetails function main catch block',error);
        throw error;
    }
}

async function handleLeaveSlabProcess( organizationDbConnection, leavePaymentSlab, otherInputs){
    console.log("Inside handleLeaveSlabProcess function");
    try{
        let leaveSlabResultArray = [];
        let { 
            elementHeader, startDate, 
            endDate,
            action,
            leaveRequestDays,
            employeeLeaveDetails,
            startDateMonthList,
            currentTotalUnit
        } = otherInputs;

        // Get slabs for specific leave type
        const slabsForLeaveType = leavePaymentSlab[employeeLeaveDetails.LeaveType_Id];
        let baseData = {
            Empcode: employeeLeaveDetails.User_Defined_EmpId,
            Flag: syntrumValues.flag,
            Payroll: syntrumValues.monthlyPayroll,
            CreateBy: employeeLeaveDetails.CreateBy,
            TotalUnits: leaveRequestDays,
            PayrollMonth: startDateMonthList.salary_month_external1
        };

        if (!slabsForLeaveType || !slabsForLeaveType.length) {
            let remainingBaseData = {
                Element: elementHeader,
                FromDate: startDate,
                ToDate: endDate
            }
            let inputDetailsToSend = { ...baseData,...remainingBaseData};
            leaveSlabResultArray.push(inputDetailsToSend);

            let resultDetails = {leaveDetailsResult: leaveSlabResultArray, currentTotalUnit: 0};
            return resultDetails;
        }else{
            if(currentTotalUnit > 0){
                totalLeaveIncludingCurrent = currentTotalUnit;
            }else{
                if(action == 'approve'){
                    totalLeaveIncludingCurrent = parseFloat(employeeLeaveDetails.Leaves_Taken)-parseFloat(employeeLeaveDetails.Total_Days);
                }else{
                    totalLeaveIncludingCurrent = parseFloat(employeeLeaveDetails.Leaves_Taken)+parseFloat(employeeLeaveDetails.Total_Days);
                    totalLeaveIncludingCurrent += 1;//Incrementing one day to match with the slab
                }
            }
            otherInputs.totalLeaveIncludingCurrent = totalLeaveIncludingCurrent;
    
            let processedLeavesResult = await processLeaveDates(organizationDbConnection,baseData, slabsForLeaveType, otherInputs);
            return processedLeavesResult;
        }
    }catch(error){
        console.log('Error in the handleLeaveSlabProcess function main catch block',error,leavePaymentSlab, otherInputs);
        throw error;
    }
}

async function processLeaveDates(organizationDbConnection, baseData, leaveslabs, otherInputs) {
    console.log('Inside processLeaveDates function.');
    try{
        const result = [];
        let currentGroup = null;
        let { 
            totalLeaveIncludingCurrent,
            employeeLeaveDetails,
            startDate, 
            endDate,
            action
        } = otherInputs;
        let currentLeaveCount = totalLeaveIncludingCurrent;

        //During cancellation, approved record edit, the from date is assigned in the reverse order. So it is set before the loop
    
        const start = moment(startDate);
        const end = moment(endDate);
        const dateOrder = action === 'cancel' ? -1 : 1;        
        const loopStart = action === 'approve' ? start.clone() : end.clone();
        const loopEnd = action === 'approve' ? end.clone() : start.clone();
    
        console.log(`Action: ${action} | Start: ${start.format('YYYY-MM-DD')} | End: ${end.format('YYYY-MM-DD')} | Initial leave count: ${currentLeaveCount}`); 
    
        for (
        let d = loopStart.clone();
        action === 'approve' ? d.isSameOrBefore(loopEnd) : d.isSameOrAfter(loopEnd);
        d.add(dateOrder, 'days')
        ) {
            const dateStr = d.format('YYYY-MM-DD');
        
            // Adjust leave count based on action
            currentLeaveCount = action === 'approve' ? currentLeaveCount + 1 : currentLeaveCount - 1;

            // Get slab and element
            const slab = leaveslabs.find(slab => 
                parseFloat(currentLeaveCount) >= parseFloat(slab.minDays) && 
                parseFloat(currentLeaveCount) <= parseFloat(slab.maxDays)
            );
            const slabCode = slab?.leaveTypeCode || '';

            if(slabCode){
                const element = action === 'approve' ? `${slabCode}_DAYS` : `${slabCode}_REV_DAYS`;
                console.log(`Processing ${dateStr} | Count: ${currentLeaveCount} | Element: ${element}`);

                // If it's the first group or the element has changed, push and start new group
                if (!currentGroup || currentGroup.Element !== element) {
                    // If element has changed, push and start new group
                    if (currentGroup){
                        let getTotalDays = await commonLib.employees.getTotalDaysBasedOnLeaveCalculationDays(organizationDbConnection,employeeLeaveDetails.Employee_Id,employeeLeaveDetails,currentGroup['FromDate'],currentGroup['ToDate']);
                        currentGroup.TotalUnits = getTotalDays;
                        
                        result.push(currentGroup);
                    }
                                        
                    currentGroup = {
                    ...baseData,
                    FromDate: dateStr,
                    ToDate: dateStr,
                    Element: element
                    };
                }else{
                    //If current group exist and element not changed, update the date based on the action
                    if(action === 'approve'){
                        currentGroup.ToDate = dateStr;
                    }else{
                        currentGroup.FromDate = dateStr;
                    }
                }
            }else{
                console.log('Leave slab not matched',dateStr, leaveslabs,currentLeaveCount);
            }
        }
    
        if (currentGroup) {
            //Though the total unit is known, for the given date range, the total days will differ. So calculate it
            let getTotalDays = await commonLib.employees.getTotalDaysBasedOnLeaveCalculationDays(organizationDbConnection,employeeLeaveDetails.Employee_Id,employeeLeaveDetails,currentGroup['FromDate'],currentGroup['ToDate']);
            currentGroup.TotalUnits = getTotalDays;
            result.push(currentGroup);
        }
    
        return {leaveDetailsResult: result, currentTotalUnit: currentLeaveCount};
    }catch(error){
        console.log('Error in the processLeaveDates function main catch block',error,baseData, leaveslabs, otherInputs);
        throw error;
    }
  }

//Function to get the full and final settlement details
async function handleFinalSettlementDataRequest(organizationDbConnection,args){
    let { uniqueIds,payrollDetails,elementHeader } = args;
    try {
        payrollDetails = JSON.parse(payrollDetails);
        const processedSettlementRecords = [];

        const moment = require('moment');

        let settlementType = [];
        let elementHeaderLC = elementHeader.toLowerCase();
        if(elementHeaderLC == 'encash_days'){
            settlementType= ['Leave Encashment','Paid Leave Deduction'];
        }
        if(settlementType){
            let allsettlementDetails = await organizationDbConnection(ehrTables.finalSettlementEarningsDeductions + " as F")
                        .select('F.Multiply_By','EJ.User_Defined_EmpId','EJ2.User_Defined_EmpId as CreateBy','F.Settlement_Type')
                        .innerJoin(ehrTables.employeeFullAndFinalSettlement + " as EF", "F.Employee_Id", "EF.Employee_Id")
                        .innerJoin(ehrTables.empJob + " as EJ", "F.Employee_Id", "EJ.Employee_Id")                    
                        .leftJoin(ehrTables.empJob + " as EJ2", "EJ2.Employee_Id", "EF.Added_By")
                        .whereIn('F.Employee_Id', uniqueIds)
                        .whereIn('F.Settlement_Type', settlementType)
                        .then();
            if(allsettlementDetails && Object.keys(allsettlementDetails).length){
                const monthYearExternalFormat = moment(payrollDetails.endDate).format('YYYY-MM');//2025-01
                
                //Iterate the settlement records and for each settlement type
                for (const finalSettlementDetails of allsettlementDetails) {
                    let totalUnits;
                    if(finalSettlementDetails.Settlement_Type == 'Paid Leave Deduction'){
                        totalUnits = '-'+finalSettlementDetails.Multiply_By; 
                    }else{
                        totalUnits = finalSettlementDetails.Multiply_By;
                    }
                    processedSettlementRecords.push({
                        Empcode: finalSettlementDetails.User_Defined_EmpId,
                        Element: elementHeader,
                        Flag: syntrumValues.flag,
                        Payroll: syntrumValues.monthlyPayroll,
                        FromDate: payrollDetails.startDate,
                        ToDate: payrollDetails.endDate,
                        CreateBy: finalSettlementDetails.CreateBy,
                        TotalUnits: finalSettlementDetails.Multiply_By,
                        PayrollMonth: monthYearExternalFormat
                    });
                }
            }
            return processedSettlementRecords;
        }else{
            console.log('Error in the handleFinalSettlementDataRequest else block',settlementType,elementHeader);
            throw 'SYN0103';
        }
    } catch (error) {
        console.log('Error in the handleFinalSettlementDataRequest function main catch block',error);
        throw error;
    }
}

module.exports = {
    getEmployeeLeaveDetails,
    handleFinalSettlementDataRequest
}