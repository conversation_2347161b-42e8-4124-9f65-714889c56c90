const commonFunction = require('../../common/initiateStepFunction');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const {asyncPAGTAPIPushFunction} = require('../../stepFunction/asyncPAGTAPIPushFunction');
//List the work schedule details in the work schedule form
module.exports.sfaIntegrationEmployeeDetails = async (parent, args, context, info) => {
    console.log('Inside sfaIntegrationEmployeeDetails function started');

    try {
    
        args.Employee_Id = context.Employee_Id;
        args.orgCode = context.Org_Code;
        args.stepFunction = 0;

        let triggerStepFunction
        if(args.integrationType && args.integrationType.toLowerCase() === 'pagt nexus hrms'){
            triggerStepFunction = await commonFunction.triggerStepFunction(process.env.asyncPAGTAPIPushFunction, 'asyncPAGTAPIPushFunction', null, args);
        } else if(args.integrationType && args.integrationType.toLowerCase() === 'sunfish'){
            triggerStepFunction = await commonFunction.triggerStepFunction(process.env.asyncSunFishAPIPushFunction, 'asyncSunFishAPIPushFunction', args);    
        }
        console.log("Triggered function stepFunction response => ", triggerStepFunction)   
       
        return {errorCode:'', message: 'External api initiated successfully.'};

    } catch(e){
        console.error('Error in sfaIntegrationEmployeeDetails function main catch block.', e);
        let errResult = commonLib.func.getError(e, '_UH0001');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
