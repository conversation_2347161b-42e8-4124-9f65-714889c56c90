const { ApolloServer, gql } = require('apollo-server-lambda');
const { resolvers } = require('./woexternalauthresolver');
const path = require("path");
const fs = require('fs');
const typeDefs = gql(fs.readFileSync(path.resolve()+'/src/'+'roschema.graphql', 'utf8'));
// require common hrapp-corelib functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
module.exports.graphql = (event, context, callback) => {
    console.log('Inside external auth function');
    context.callbackWaitsForEmptyEventLoop = false; //to send the response immediately when callback executes

    // Create object for ApolloServer
    const server = new ApolloServer({
        typeDefs,
        resolvers,
        context: async ({ event }) => {
            let contextData = await commonLib.func.getContextDataWithoutEmployeeId(event, 1, 'ro') 
            contextData.irukka_app_id = event.headers.irukka_app_id;
            contextData.irukka_secret_key = event.headers.irukka_secret_key;
            //return header to resolver function
            return {...contextData};
        }
    });
    const handler = server.createHandler({
        cors: {
            method: 'POST',
            allowHeaders: '*'
        }
    });
    
    function callbackFilter(error, output) {
        output.headers['Access-Control-Allow-Origin'] = '*';
        output.headers['Access-Control-Allow-Credentials'] = true;
        callback(error, output);
    }
    return handler(event, context, callbackFilter);
};
