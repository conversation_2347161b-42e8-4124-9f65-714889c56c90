//const getIrukkaStatus = require('./roresolvers/camuIntegration/getIrukkaStatus');
const updateIrukkaJobPostStatus = require('./updateIrukkaJobPostStatus');
const addIrukkaCandidateDetails = require('./addIrukkaCandidateDetails');
// Define resolver
const resolvers = {
    Mutation: Object.assign({},
        updateIrukkaJobPostStatus,
        addIrukkaCandidateDetails
    )
}
exports.resolvers = resolvers;