// Require necessary libraries and modules
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib; // Common library for accessing shared functions
const knex = require('knex'); // Database query builder
const { ApolloError } = require('apollo-server-lambda'); // To handle errors in Apollo Server
const { ehrTables } = require('../../../common/tablealias'); // Alias for database table names

module.exports.moveArchiveToCandidate = async (parent, args, context, info) => {
    console.log("Inside moveArchiveToCandidate function.");
    // Establish a database connection for the organization
    let organizationDbConnection = knex(context.connection.OrganizationDb);
    try {
        const loginEmployeeId = context.Employee_Id; // Extract employee ID from context

        // Validate input arguments
        if (!args || !args.candidateId) {
            throw 'IVE0000'; // Throw an error if candidateId is missing
        }

        // Check if the employee has the rights to update
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId
        );

        // If the employee has update rights
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
            // Fetch the candidate data from the database
            const candidateData = await organizationDbConnection(ehrTables.candidateRecruitmentInfo + ' as CRI')
                .select('CRI.Archived', 'ASS.Stage', organizationDbConnection.raw("CONCAT_WS(' ', CPI.Emp_First_Name, CPI.Emp_Last_Name) as candidateName"))
                .innerJoin(ehrTables.candidatePersonalInfo + ' as CPI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                .leftJoin(ehrTables.atsStatusTable + ' as ATS', 'ATS.Id', 'CRI.Candidate_Status')
                .leftJoin(ehrTables.atsStatusStage + ' as ASS', 'ASS.Stage_Id', 'ATS.Stage_Id')
                .where('CPI.Candidate_Id', args.candidateId)
                .first();

            // If no candidate data is found, throw an error for invalid candidate ID
            if (!candidateData) {
                throw 'EI00224'; //Sorry! An error occurred while processing the candidate details. Please try again.
            }

            // If the candidate is already archived is no, throw an error
            if (candidateData && candidateData.Archived.toLowerCase() === 'no') {
                throw 'EI00212'; // Sorry! The candidate archive status has already been transferred and cannot be altered. Please contact the system administrator.
            }

            await organizationDbConnection.transaction(async (trx) => {
            // Update the candidate's Archived to no (not archived)
                const [recruitmentInfo, notification] = await Promise.all([
                    organizationDbConnection(ehrTables.candidateRecruitmentInfo).transacting(trx)
                    .update({ Archived: 'No', Archived_On: null, Archived_By: null }).where('Candidate_Id', args.candidateId),

                    organizationDbConnection(ehrTables.candidateArchiveNotification).transacting(trx)
                    .del().where('Candidate_Id', args.candidateId).where('Status', 'Open'),

                    candidateData?.Stage?.toLowerCase() === "archived" ? null : 
                    organizationDbConnection(ehrTables.candidatePersonalInfo).transacting(trx)
                    .update({ Archive_Reason_Id: null, Archive_Comment: null}).where('Candidate_Id', args.candidateId),
                ]) 

                // If the update fails, throw an error
                if (!recruitmentInfo) {
                    throw 'EI00213'; // Oops! An error occurred while updating the candidate details. Please try again later.
                }
            })


            // Log the activity
           await commonLib.func.createSystemLogActivities({
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                changedData: args,
                organizationDbConnection: organizationDbConnection,
                formId: args.formId,
                action: 'Rollback',
                isEmployeeTimeZone: 0,
                uniqueId: args.candidateId,
                message: `The candidate has been rollback from the Archive to the Job Candidate.`
            });

            // Return a success message
            return { errorCode: '', message: 'This candidate has been successfully transferred from the archive to job candidate.' };
        } else {
            throw '_DB0102'; // Error for lack of edit access rights
        }
    } catch (e) {
        console.error('Error while moveArchiveToCandidate main catch block.', e);
        // Get a user-friendly error message
        const errResult = commonLib.func.getError(e, 'EI00213');  //Oops! An error occurred while updating the candidate details. Please try again later.
        // Throw an ApolloError with the error message
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Destroy the database connection to free up resources
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
}


