//get common functions
const{getConnection,getOpenStatusDataFromMasterTable,updateInMasterTable}=require("./commonFunctions");
//get tablealias
const{appManagerTables,ehrTables}=require("../../common/tablealias")
// Organization database connection
const knex = require('knex');
//Require moment
const moment = require('moment-timezone');

//variable declaration
let masterTable=appManagerTables.resignationManager;
let inputParams;
let currentDate;
let appmanagerDbConnection;
let organizationDbConnection;
module.exports.insertResignationEmployee  = async(event,context) =>{
    try{
        console.log('Inside insertResignationEmployee function');
        currentDate=moment.utc().format("YYYY-MM-DD");
        // get input data
        let inputStatus=event.status;
        if(inputStatus && (inputStatus.toLowerCase()==='open' || inputStatus.toLowerCase()==='failed'))
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.
            Incase of input status as 'Open' proceed to the camu exit api call process*/
            console.log('Event triggered to process remaining records so move to step3');
            let response={
                nextStep:'Step3',
                input:{'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            let databaseConnection=await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                let openInstances= await getOpenStatusDataFromMasterTable(appmanagerDbConnection,masterTable,"Employee_Insert_Status");
                if(openInstances)
                {  
                    if(openInstances.length>0)
                    {
                        for(let i=0;i<openInstances.length;i++)
                        {
                            let orgCode=openInstances[i]['Org_Code'];
                            let connection=await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region,orgCode);
                            organizationDbConnection = knex(connection.OrganizationDb);
                            let inactiveEmployees=await getInactiveEmployeess(organizationDbConnection);
                            if(inactiveEmployees)
                            {   
                                if(inactiveEmployees.length>0)
                                {
                                 
                                    for(let j=0;j<inactiveEmployees.length;j++)
                                    {
                                        inactiveEmployees[j]['Status']='Open';
                                        inactiveEmployees[j]['Data_Processing_Date']=currentDate;     
                                    }
                                    let insertEmployeesResponse=await insertEmployees(organizationDbConnection,inactiveEmployees);
                                    if(insertEmployeesResponse)
                                    {   
                                        inputParams={
                                            Employee_Insert_Status:"Success"
                                        }
                                        await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);  
                                    }
                                    else{
                                        inputParams={
                                            Employee_Insert_Status:"Failed"
                                        }
                                        await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                                    }
                                }
                                else{
                                    inputParams={
                                        Employee_Insert_Status:"Success"
                                    }
                                    await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);   
                                }
                            }
                            else{
                                console.log("Error Occurred while geting inactive Employees");
                                inputParams={
                                    Employee_Insert_Status:"Failed"
                                }
                                await updateInMasterTable(appmanagerDbConnection,inputParams,masterTable,orgCode);
                            }
                            organizationDbConnection?organizationDbConnection.destroy():null;   
                        }
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response={
                            nextStep:'Step3',
                            input:{'status':inputStatus},
                            message:'Event triggered to process next step.'          
                        }
                        return response;
                    }
                    else{
                        console.log("No Open instances found.");
                        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                        let response={
                            nextStep:'Step3',
                            input:{'status':inputStatus},
                            message:'Event triggered to process next step.'          
                        }
                        return response;
                    }
                   
                }
                else{
                    console.log("Error Occured while calling  getOpenOrgCodeFromMasterTable.");
                    appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error Occured while calling getOpenOrgCodeFromMasterTable.'
                    };
                    return response;
                }
            }
            else{
                console.log('Error while creating app manager database connection in step1');
                let response ={
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Occured creating app manager database connection.'
                };
                return response;
            }
        }
    }
    catch(e){
        console.log("Error in insertResignationEmployee function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        organizationDbConnection?organizationDbConnection.destroy():null; 
        let response ={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Error Occured in insertResignationEmployee.'
        };
        return response;
    }
}



//function to getInactive employees
async function getInactiveEmployeess(organizationDbConnection)
{
    try{
        let subQuery=organizationDbConnection(ehrTables.camuEmployeeResignationManager).select('Employee_Id');
        return(
            organizationDbConnection(ehrTables.empJob)
            .select('Employee_Id')
            .whereNotNull("Camu_Id")
            .andWhere("Emp_Status","InActive")
            .andWhere("Employee_Id","not in",subQuery)
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log("Error in getInactiveEmployeess function  .catch block.",e);
                return false;
            })              
        )
    }
    catch(e)
    {
        console.log("Error in getInactiveEmployeess function main catch block.",e);
        return false;
    }
}

//function to insert inactive employees in camuEmployeeResignationManager table
async function insertEmployees(organizationDbConnection,insertData)
{
    try{
        return(
            organizationDbConnection(ehrTables.camuEmployeeResignationManager)
            .insert(insertData)
            .then(data=>{
                return true;
            })
            .catch(e=>{
                console.log("Error in insertEmployees function .catch block.",e);
                return false;
            })
        )
    }
    catch(e)
    {
        console.log("Error in insertEmployees function main catch block.",e);
        return false;
    }
}

//function to purge employees except with failed status from camuEmployeeResignationManager table
async function purgeEmployees(organizationDbConnection)
{
    try{
        organizationDbConnection(ehrTables.camuEmployeeResignationManager)
        .where("Status",'!=','Failed')
        .del()
        .then(data=>{
            return true;
        })
        .catch(e=>{
            console.log("Error in purgeEmployees function .catch block.",e);
            return false;
        })
    }
    catch(e)
    {
        console.log("Error in purgeEmployees function main catch block.",e);
        return false;
    }
}

