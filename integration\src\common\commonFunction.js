//Require table alias
const { ehrTables, appManagerTables } = require('../../common/tablealias');
//Require aws-sdk
const AWS = require('aws-sdk');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require moment
const moment = require('moment-timezone');
//Require axios
const axios = require('axios');
const { validateWithRules } = require('@cksiva09/validationlib/src/validator');
const nodemailer = require('nodemailer')
const { s3DefaultValues, awsSesTemplates, defaultURL, s3FileUpload } = require('../../common/appConstants');
const ExcelJS = require('exceljs');
const fs = require('fs');
const os = require('os');
const path = require('path');
const s3 = new AWS.S3();
async function getCredentials(dbSecretName) {
  try {
    //Create client for secrets manager
    let client = new AWS.SecretsManager({
      region: process.env.region
    });

    //Get secrets from aws secrets manager
    let secretKeys = await client.getSecretValue({ SecretId: dbSecretName }).promise();
    secretKeys = JSON.parse(secretKeys.SecretString);

    return (secretKeys);
  } catch (getCredentialsCatchError) {
    console.log('Error in getCredentials() main catch block.', getCredentialsCatchError);
    return (getCredentialsCatchError);
  }
}

async function getOrgDetails(organizationDbConnection, orgCode) {
  try {
    return (
      organizationDbConnection(ehrTables.orgDetails)
        .select('Institution_Code as instituteCode', 'Camu_Base_Url as camuBaseUrl')
        .where('Org_Code', '=', orgCode)
        .then(data => {
          if (data.length > 0) {
            return data[0];
          }
          else {
            return {};
          }
        })
        .catch(e => {
          console.log("Error occured while getting org details", e);
          return { error: e };
        })
    )
  }
  catch (e) {
    console.log("Error Occured while getting Institution Code main catch block", e);
    return { error: e };
  }
}
async function checkJobStreetRecordExistJobPostId(organizationDbConnection, Job_Post_Id, status = ["Closed"]) {
  try {
    let records = await organizationDbConnection(ehrTables.jobStreetJobProfile + " as JP")
      .select('JP.Job_Street_Id')
      .leftJoin('job_street_job_openings as JO', 'JO.Job_Street_Id', 'JP.Job_Street_Id')
      .where('JP.Job_Post_Id', Job_Post_Id)
      .whereIn('JP.Applied_Status', status);

    if (records && records.length) return records;
    else return [];
  }
  catch (err) {
    console.log("error in checkexisting record function", err);
    throw err;
  }
}

async function camuConversion(input) {
  try {
    let camuConversionFields = {
      'Male': 'M',
      'Female': 'F',
      'Mr': 'MR',
      'Miss': 'MS',
      'Mrs': 'MRS',
      'Dr': 'DR',
      'O-': 'ONEG',
      'O+': 'OPOS',
      'B-': 'BNEG',
      'B+': 'BPOS',
      'A+': 'APOS',
      'A-': 'ANEG',
      'AB+': 'ABPOS',
      'AB-': 'ABNEG',
      'A1-': 'A1NEG',
      'A1+': 'A1POS',
      'A1B-': 'A1BNEG',
      'A1B+': 'A1BPOS',
      'A2-': 'A2NEG',
      'A2+': 'A2POS',
      'A2B-': 'A2BNEG',
      'A2B+': 'A2BPOS',
      'B1+': 'B1POS',
      '2': 'M',
      '1': 'S',
    }
    if (input['gender'] && camuConversionFields[input['gender']]) {
      input['gender'] = camuConversionFields[input['gender']];
    }
    if (input['bloodGroup'] && camuConversionFields[input['bloodGroup']]) {
      input['bloodGroup'] = camuConversionFields[input['bloodGroup']];
    }
    if (input['maritalStatus'] && camuConversionFields[input['maritalStatus']]) {
      input['maritalStatus'] = camuConversionFields[input['maritalStatus']];
    }
    if (input['title'] && camuConversionFields[input['title']]) {
      input['title'] = camuConversionFields[input['title']];
    }
    if (input['DOB']) {
      let dateOfBirth = moment(input['DOB']);
      dateOfBirth = dateOfBirth.format('DD-MMM-YYYY')
      input['DOB'] = dateOfBirth;
    }
    if (input['DOJ']) {
      let dateOfJoin = moment(input['DOJ']);
      dateOfJoin = dateOfJoin.format('DD-MMM-YYYY');
      input['DOJ'] = dateOfJoin;
    }

    return input;
  }
  catch (e) {
    console.log("Error Occurred while converting input to camu field", e);
    return { error: e };
  }
}
async function updateCamuIdInEmpJob(organizationDbConnection, employeeId, camuId) {
  console.log("Inside updateCamuIdInEmpJob function");
  try {
    return (
      organizationDbConnection(ehrTables.empJob)
        .where('Employee_Id', '=', employeeId)
        .update('Camu_Id', camuId)
        .then(data => {
          return true;
        })
        .catch(e => {
          console.log("error occurred while updating camuId", e);
          return false;
        })
    )
  }
  catch (e) {
    console.log("Error Occured while updating camu id in emp job main catch block", e);
    return false;
  }
}

async function updateEmployeeInfoTimestampLog(organizationDbConnection, employeeId, action, status, failureReason = NULL) {
  try {
    console.log('inside updateEmployeeInfoTimestampLog() function', employeeId, action, status, failureReason);
    return (
      organizationDbConnection(ehrTables.employeeInfoTimestampLog)
        .where('Employee_Id', '=', employeeId)
        .where('Action', action)
        .update({ 'Camu_Push_Status': status, 'Failure_Reason': failureReason, Log_Timestamp: moment().utc().format('YYYY-MM-DD HH:mm:ss') })
        .then(data => {
          return true;
        })
        .catch(e => {
          console.log("error occurred while updating EmployeeInfoTimestampLog", e);
          return false;
        })
    )
  }
  catch (e) {
    console.log("Error Occured while updating status in EmployeeInfoTimestampLog main catch block", e);
    return false;
  }
}

async function callCreateStaffApi(data, camuSyncCreateStaffEndPoint, camuToken) {
  console.log("Inside  callCreateStaffApi() function")
  try {
    let response = { "camuId": "", "error": "" };
    if (data['email'] && data['email'].length > 0 && data['staffID'] && data['staffID'].length > 0) {
      const apiHeaders = {
        'Content-Type': 'application/json',
        'api-key': camuToken
      }
      const config = {
        method: 'post',
        url: camuSyncCreateStaffEndPoint,
        maxBodyLength: Infinity,
        data: data,
        headers: apiHeaders
      };
      return (
        axios.request(config)
          .then(response => {
            if (response && response.data && response.data.id) {
              let camuId = response.data.id;
              response.camuId = camuId;
              return response;
            } else {
              response.error = "Technical issue";
              return response;
            }
          })
          .catch(e => {
            console.log("error occured while calling callCreateStaffApi", e);
            response.error = (e.response && e.response.data && e.response.data.errorMessage) ? e.response.data.errorMessage : "Technical issue";
            // return "";
            return response;
          })
      )
    }
    else {
      console.log("error occured while calling callCreateStaffApi because email or staff id is not there");
      // return "";
      response.error = "Email address or staff ID is not present";
      return response;
    }

  }
  catch (e) {
    console.log("error occured while calling callCreateStaffApi main catch block", e);
    return "";
  }
}
function validateIrukkaCandidateDetails(args, fieldValidations) {
  let validationError = {};

  for (const field in fieldValidations) {
    let fieldName = field; // By default, use the current field name
    if (field.includes('_')) {
      const parts = field.split('_');
      fieldName = parts[1]; // Use the name after the underscore
    }

    if (args[field]) {
      const validation = validateWithRules(args[field], fieldName);
      if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {
        validationError[fieldValidations[field]] = validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
      }
    }
  }
  return validationError;
}
async function validateJobStreetCommonRuleInput(args, fieldValidations, jobOpening = 0) {
  try {
    if (args.jobStreetId && (!args.profileId || !args.profileId.length) && !jobOpening) {
      console.log("error profileid not provided for update")
      throw 'EI00162'
    }
    let validationError = {};
    for (const field in fieldValidations) {
      let fieldName = field; // By default, use the current field name
      if (args.hasOwnProperty(field)) {
        const validation = validateWithRules(args[field], fieldName);
        if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {

          validationError[fieldValidations[field]] = validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
        }
      }
    }
    return validationError;
  }
  catch (err) {
    console.log('Error in the validateCommonRuleInput function in the main catch block.', err);
    throw err;
  }
}

async function validatCommonRuleInput(args, fieldValidations) {
  try {
    let validationError = {};
    for (const field in fieldValidations) {
      let fieldName = field; // By default, use the current field name
      if (args.hasOwnProperty(field)) {
        const validation = validateWithRules(args[field], fieldName);
        if (validation !== 'Validation not found' && (!validation.validation || !validation.length)) {

          validationError[fieldValidations[field]] = validation.length ? commonLib.func.getError('', fieldValidations[field]).message : commonLib.func.getError('', fieldValidations[field]).message1;
        }
      }
    }
    return validationError;
  }
  catch (err) {
    console.log('Error in the validateCommonRuleInput function in the main catch block.', err);
    throw err;
  }
}

async function checkJobStreetRecordExist(organizationDbConnection, jobStreetId, status = ["Pending"]) {
  try {
    let records = await organizationDbConnection(ehrTables.jobStreetJobProfile + " as JP")
      .select('JP.Applied_Status', 'JO.Document_Id', 'JP.Profile_Id')
      .leftJoin('job_street_job_openings as JO', 'JO.Job_Street_Id', 'JP.Job_Street_Id')
      .where('JP.Job_Street_Id', jobStreetId)
      .whereIn('JP.Applied_Status', status);

    if (records && records.length) return records;
    else return [];
  }
  catch (err) {
    console.log("error in checkexisting record function", err);
    throw err;
  }
}
async function checkJobOpeningRecordExist(organizationDbConnection, jobStreetId, status = ["Pending"]) {
  try {
    let records = await organizationDbConnection(ehrTables.jobStreetJobOpenings + " as JP")
      .select('JP.Applied_Status', 'JP.Document_Id')
      .where('JP.Job_Street_Id', jobStreetId)
      .whereIn('JP.Applied_Status', status);

    if (records && records.length) return records;
    else return [];
  }
  catch (err) {
    console.log("error in checkJobOpeningRecordExist function", err);
    throw err;
  }
}
function mapErrorToCustomCodeSeek(error) {
  const errorMapping = {
    "FORBIDDEN": "CH0001",
    "UNAUTHENTICATED": "CH0002",
    "BAD_USER_INPUT": "CH0003",
    "INTERNAL_SERVER_ERROR": "CH0004"
  };

  const errorCode = error.errors[0].extensions.code;
  return errorMapping[errorCode] || "CH0005"; // Default to CH0005 for unmapped errors
}
async function jobPostDetailss(organizationDbConnection, jobId) {
  try {
    let records = await organizationDbConnection(ehrTables.jobPost)
      .select('Job_Post_Id', 'Min_Payment_Frequency as minimumAmount', 'Max_Payment_Frequency as maximumAmount',
        'Closing_Date', 'Job_Description',  'currency.Currency_Code as currency',
        organizationDbConnection.raw(`
          CASE 
            WHEN pay_type.Job_Street_Pay_Type = 'Hourly' THEN 'Hourly'
            WHEN pay_type.Job_Street_Pay_Type = 'Annual Commission' THEN 'SalariedPlusCommission' 
            ELSE 'Salaried' 
          END AS 'basiscode',
          CASE 
            WHEN pay_type.Job_Street_Pay_Type = 'Hourly' THEN 'Hour'
            WHEN pay_type.Job_Street_Pay_Type = 'Monthly' THEN 'Month'
            ELSE 'Year'
          END AS intervalcode
        `)
      )
      .leftJoin('currency', 'job_post.Currency', 'currency.Currency_Id')
      .leftJoin('pay_type', 'pay_type.Pay_Type_Name', 'job_post.Pay_Type')
      .where('Job_Post_Id', jobId);
    if (records && records.length) return records;
    else return [];
  } catch (err) {
    console.log("error in jobPostDetailss catch block", err)
    throw err;
  }
}
async function getHirerId(organizationDbConnection, integrationType) {
  try {
    let recruitmentDetails;
    await organizationDbConnection(ehrTables.recruitmentIntegration)
      .select('Hirer_ID', 'Integration_Type')
      .where('Integration_Type', integrationType)
      .then((data) => {
        recruitmentDetails = data;
      })

    return recruitmentDetails || [];
  } catch (error) {
    console.error('Error in getHirerId try-catch block', error);
    return []; // Return empty array in case of error
  }
}


async function deleteLinkedInJob(args) {
  try {
    console.log("Inside deleteLinkedInJob function()")
    const AWS = require('aws-sdk')
    const s3 = new AWS.S3({ region: process.env.region });
    if (args.action.toLowerCase() === 'delete-data') {
      // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
      var isFileExists = await s3.headObject({ Bucket: process.env.recruitBucketName, Key: args.fileName }).promise();
      if (isFileExists) {
        await s3.deleteObject({ Bucket: process.env.recruitBucketName, Key: args.fileName }).promise();
      }
    }

  } catch (err) {
    console.error("Error : while s3UploadAndRetrieve catch block => ", err)
  }
}
async function getUserData() {
  try {
    const AWS = require('aws-sdk')

    // Create client for secrets manager
    let client = new AWS.SecretsManager({
      region: process.env.region
    })
    // Get secrets from aws secrets manager
    let secretKeys = await client
      .getSecretValue({ SecretId: process.env.dbSecretName })
      .promise()
    secretKeys = JSON.parse(secretKeys.SecretString)
    let { SeekClientID, SeekClientSecret } = secretKeys;
    if (!SeekClientID || !SeekClientSecret) {
      throw 'EI00163';
    }
    return {
      SeekClientID,
      SeekClientSecret
    }

  } catch (err) {
    console.log('Error in getUserData function main catch block')
    throw err
  }
}
async function getWebHookSecret() {
  try {
    const AWS = require('aws-sdk')

    // Create client for secrets manager
    let client = new AWS.SecretsManager({
      region: process.env.region
    })
    // Get secrets from aws secrets manager
    let secretKeys = await client
      .getSecretValue({ SecretId: process.env.dbSecretName })
      .promise()
    secretKeys = JSON.parse(secretKeys.SecretString);
    let { seekwebhooksecret } = secretKeys;
    if (!seekwebhooksecret) {
      throw 'EI00163';
    }
    return seekwebhooksecret;

  } catch (err) {
    console.log('Error in getWebHookSecret function main catch block')
    throw err
  }
}
async function getJobStreetAccessToken(SeekClientID, SeekClientSecret) {
  const url = process.env.jobStreetAuthTokenAPI;
  const requestBody = {
    audience: 'https://graphql.seek.com',
    client_id: SeekClientID,
    client_secret: SeekClientSecret,
    grant_type: 'client_credentials'
  }

  try {
    const apiHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'YourPartnerService/1.2.3'
    }
    const config = {
      method: 'post',
      url: url,
      maxBodyLength: Infinity,
      data: requestBody,
      headers: apiHeaders
    };
    const response = await axios
      .request(config)
      .catch((err) => {
        console.log('Error in getAuthTokenJobStreet catch block', err)
        if (err.response.status === 401) {
          console.log(
            'unathorized error please check client id and credential are correct',
            err
          )
          throw 'EI00141'
        } else {
          throw err
        }
      })
    if (!response || !response.data || !response.data.access_token) {
      throw 'EI00142'
    }

    return response.data;
  } catch (error) {
    console.error('Error in getAuthToken function catch block', error)
    return null
  }
}

async function getDecryptedString(decryptionInputs) {
  try {
    var AWS = require("aws-sdk");
    // Create lambda object to invoke lambda
    const lambda = new AWS.Lambda({
      region: process.env.region
    });
    var payload = { body: JSON.stringify(decryptionInputs) };
    const invokeParams = {
      FunctionName: "decrypt-keys",
      InvocationType: "RequestResponse",
      Payload: JSON.stringify(payload)
    };
    // Use await to wait for the Lambda invocation response
    const result = await lambda.invoke(invokeParams).promise();

    let decryptionResult = JSON.parse(result.Payload);

    let decryptionResponse = JSON.parse(decryptionResult.body);

    if (decryptionResponse.errorCode) {
      throw decryptionResponse.errorCode;
    } else {
      return decryptionResponse.decryptedString;
    }
  } catch (decryptionCatchError) {
    console.log("Error in getDecryptedString function main catch block", decryptionCatchError);
    throw decryptionCatchError;
  }
}
async function fetchAndUploadFile(url, fileName, bucket, partnerToken) {
  try {
    // Create object for s3 bucket
    const s3 = new AWS.S3({ region: process.env.region })
    let contentType = 'application/pdf'
    const config = {
      method: 'get',
      url: url,
      maxBodyLength: Infinity,
      responseType: 'arraybuffer',
      headers: {
        Authorization: `Bearer ${partnerToken}`,
        'Content-Type': contentType,
        'User-Agent': 'YourPartnerService/1.2.3'
      } // Fetch the data as an ArrayBuffer
    }
    const response = await axios.request(config)
    const contentTypes = response.headers['content-type']
    console.log(response.headers['content-type'], "resumeData..", response.headers)
    let contentDisposition = response.headers['content-disposition']
    let extension = getFileExtension(contentTypes, contentDisposition)
    const base64 = Buffer.from(response.data, 'binary').toString('base64')
    const base64Data = new Buffer.from(
      base64.replace(/^data:image\/\w+;base64,/, ''),
      'base64'
    )
    let s3Params = {
      Body: base64Data,
      Bucket: bucket,
      Key: fileName + extension,
      ContentEncoding: s3DefaultValues.contentEncoding,
      ContentType: contentTypes
    }
    let value = await s3.upload(s3Params).promise()
    return extension;
  } catch (error) {
    console.error('Error fetching or uploading file:', error)
  }
}

async function uploadFileToS3Bucket(fileName, resumeData, bucket) {
  try {
    // Create object for s3 bucket
    const s3 = new AWS.S3({ region: process.env.region });
    let extension = getFileExtension(resumeData.contentType);
    const fileContent = Buffer.from(resumeData.data, 'base64');
    let s3Params = {
      Body: fileContent,
      Bucket: bucket,
      Key: fileName + extension,
      ContentEncoding: s3DefaultValues.contentEncoding,
      ContentType: resumeData.contentType
    }

    let value = await s3.upload(s3Params).promise()
    return extension;
  } catch (error) {
    console.error('Error fetching or uploading file:', error)
  }
}

/**
 * Deletes a file from S3 bucket
 * @param {string} fileName - file name to be deleted
 * @param {string} bucket - S3 bucket name
 * @returns {Promise} - Promise with the result of the operation
 */
async function deleteFileFromS3Bucket(fileName, bucket) {
  try {
    const s3 = new AWS.S3({ region: process.env.region });
    let s3Params = {
      Bucket: bucket,
      Key: fileName
    }
    await s3.deleteObject(s3Params).promise()
  } catch (error) {
    console.error('Error in deleteFileFromS3Bucket', error)
    throw error
  }
}

function getFileExtension(contentType, contentDisposition = null) {
  console.log(contentType, "contentType---")
  switch (contentType) {
    case 'application/msword':
      return '.doc' // Microsoft Word 97-2004
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return '.docx' // Microsoft Word (OpenXML)
    case 'application/rtf':
      return '.rtf' // Rich Text Format (RTF)
    case 'application/pdf':
      return '.pdf' // Portable Document Format (PDF)
    case 'text/plain':
    case 'text/plain; charset=utf-8':
      return '.txt' // Plain text
    case 'application/octet-stream':
      if (contentDisposition && contentDisposition.includes('filename=')) {
        const filename = contentDisposition.split('filename=')[1].replace(/"/g, '');
        const extension = filename.split('.').pop();
        console.log(extension, "extension---"); // Log the extracted extension
        return '.' + extension; // Return the extension from the filename
      }
      console.log('Filename not found in Content-Disposition header');
      return '';
    default:
      return '' // Return an empty string or a default value if the Content-Type is not recognized
  }
}
async function formBodyForEmail(CcAddresses, organizationDbConnection, trx, orgCode, jobPostDetails, jobPostId, jobPostLink) {
  try {
    let destinations = [];
    let logoPath;
    let orgDetails = await commonLib.func.getOrgDetails(orgCode, organizationDbConnection)
    let notificationSetting = await getNotificationSetting(organizationDbConnection, trx);
    logoPath = notificationSetting === 'No' ? '' : (orgDetails.logoPath || '');
    let sendToMail = jobPostDetails.Emp_Email;
    destinations.push({
      Destination: {
        ToAddresses: [sendToMail],
        BccAddresses: CcAddresses
      }
    });
    let notificationParams = {
      "Source": process.env.emailFrom,
      "Template": "JobPostsJobStreetClosed",
      "Destination": {
        ToAddresses: [sendToMail],
        BccAddresses: CcAddresses
      },
      "TemplateData": JSON.stringify({
        orgLogo: logoPath ? logoPath : "",
        name: jobPostDetails.Job_Post_Owner_Name,
        jobPostTitle: jobPostDetails.Job_Post_Name,
        jobCode: 'JOB' + jobPostId,
        mailContent: '',
        hrappSupportEmail: "",
        initiatedBy: "",
        typeOfAction: "",
        actionDoneBy: "",
        redirectUrl: jobPostLink ? jobPostLink : '',
      })
    }

    return notificationParams;
  }
  catch (err) {
    throw err;
  }
}
async function getRecruiterEmailAddress(organizationDbConnection, jobPostId, trx) {
  try {
    let recruiterEmail = await organizationDbConnection(
      ehrTables.jobPostRecruiters + ' as jpr'
    )
      .pluck('ej.Emp_Email')
      .innerJoin(
        ehrTables.empJob + ' as ej',
        'jpr.Recruiter_Id',
        'ej.Employee_Id'
      )
      .where('jpr.Job_Post_Id', jobPostId)
      .whereNotNull('ej.Emp_Email')
      .whereNot('ej.Emp_Email', '')
      .transacting(trx);
    return recruiterEmail
  } catch (err) {
    console.log('Error in getRecruiterEmailAddress function .catch block', err)
    throw err
  }
}
async function getNotificationSetting(organizationDbConnection, trx) {
  try {
    let addLogoValue = await organizationDbConnection('email_notification_setting').select('Add_Logo').transacting(trx);

    if (addLogoValue && addLogoValue.length && addLogoValue[0].Add_Logo) {
      return addLogoValue[0].Add_Logo;
    } else {
      return 'Yes';
    }
  } catch (err) {
    throw err
  }
}
async function getWorkflowProcessInstanceId(organizationDbConnection, uniqueId) {
  try {
    return await organizationDbConnection(ehrTables.mppPositionRequest)
      .select('Process_Instance_Id')
      .where('Position_Request_Id', uniqueId);
  } catch (err) {
    console.log('Error in getWorkflowProcessInstanceId', err);
    throw err;
  }
}
async function getWorkflowProcessInstanceData(organizationDbConnection, workflowInstanceId, trx) {
  try {
    return await organizationDbConnection('ta_process_instance').select('instance_data').where('process_instance_id', workflowInstanceId).transacting(trx);
  } catch (err) {
    console.log('Error in getWorkflowProcessInstanceData', err);
    throw err;
  }
}
function calculateStartMonth(endMonth, startYear) {
  let forecastStartMonth;
  let forecastEndYear = startYear;

  if (endMonth === 12) {
    // Scenario 1: End month is December, start month is January of the same year
    forecastStartMonth = 1;
  } else {
    // Scenario 2: End month is any month other than December
    forecastStartMonth = endMonth + 1;
    forecastEndYear = startYear + 1;
  }

  return { forecastStartMonth, forecastEndYear };
}
async function initaiteWorkflow(eventId, instanceData, orgCode, formId, organizationDbConnection, loginEmployeeId, trx) {
  try {
    instanceData.formId = formId
    instanceData.Employee_Id = loginEmployeeId
    instanceData.Added_By = loginEmployeeId
    instanceData.Added_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const config = {
      method: 'post',
      url: `https://${process.env.customDomainName}/workflowEngine/workflow/initiate`,
      data: { event_id: eventId, instance_data: instanceData },
      headers: {
        'Content-Type': 'application/json',
        org_code: orgCode,
        employee_id: loginEmployeeId,
        db_prefix: process.env.dbPrefix,
      },
    };
    const response = await axios.request(config);
    if (response.status === 200 && response.data?.workflowProcessInstanceId) {
      await updateWorkflowProcessInstanceId(organizationDbConnection, instanceData.positionRequestId, response.data.workflowProcessInstanceId, trx);
    } else {
      console.log('Error in the initaiteWorkflow endpoint.', response);
      throw 'ATS0009';
    }
  } catch (mainError) {
    console.log('Error in initaiteWorkflow', mainError);
    throw 'ATS0009';
  }
}
async function updateWorkflowProcessInstanceId(organizationDbConnection, uniqueId, workflowProcessInstanceId, trx) {
  try {
    return await organizationDbConnection(ehrTables.mppPositionRequest)
      .where('Position_Request_Id', uniqueId)
      .transacting(trx)
      .update({ Process_Instance_Id: workflowProcessInstanceId });
  } catch (err) {
    console.log('Error in updateWorkflowProcessInstanceId', err);
    throw err;
  }
}

async function checkDeletionEligibility(organizationDbConnection, seekHirerId) {
  try {
    // Fetch the Hirer_ID based on the given Hirer_List_Id
    const hirerRecord = await organizationDbConnection(ehrTables.seekHirerList)
      .select('Hirer_ID')
      .where('Hirer_List_Id', seekHirerId)
      .first();

    if (!hirerRecord) {
      throw 'EI00218';
    }
    // Check if there are any job profiles with active statuses that prevent deletion
    const jobProfileRecord = await organizationDbConnection(ehrTables.jobStreetJobProfile + ' as JP')
      .select('JP.Applied_Status')
      .where('JP.Hirer_ID', hirerRecord.Hirer_ID)
      .whereIn('JP.Applied_Status', ['Pending', 'Applied', 'Update Progress'])
      .first();

    if (jobProfileRecord) {
      console.log('Deletion restricted due to existing job posts with active statuses.');
      throw 'EI00215';
    }

    return true;
  }
  catch (err) {
    throw err
  }
};

async function approvedPositionValidationSkip(organizationDbConnection) {
  try {
    const recruitmentSettings = await organizationDbConnection('recruitment_request_settings')
      .select('Warm_Bodies_Include_Notice_Period', 'Allow_Hire_Without_Approved_Vacancy').first();

    return recruitmentSettings;
  } catch (err) {
    console.log('Error in approvedPositionValidationSkip', err);
    throw err;
  }
}


async function updateCandidateDetails(organizationDbConnection, args, candidateCount, loginEmployeeId, trx) {
  try {
    let candidateData = {
      Archive_Reason_Id: args.archiveReasonId,
      Archive_Comment: args.archiveComment,
      Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
      Updated_By: loginEmployeeId
    }

    if (candidateCount) {
      candidateData.Is_Duplicate = 1;
      candidateData.Duplicate_Count = candidateCount + 1;
    }

    // Update the candidate's personal information
    await organizationDbConnection(ehrTables.candidatePersonalInfo)
      .update(candidateData).where('Candidate_Id', args.candidateId)
      .transacting(trx);

    const archiveStatus = args?.archiveStatus?.toLowerCase();
    if (archiveStatus !== 'no') {
      await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
        .update({
          Archived: 'Yes',
          Archived_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
          Archived_By: loginEmployeeId
        })
        .where('Candidate_Id', args.candidateId)
        .transacting(trx);
    }

    if (args.notificationTime && args.mailContent) {
      await organizationDbConnection(ehrTables.candidateArchiveNotification).transacting(trx)
        .insert({
          Candidate_Id: args.candidateId,
          Notification_Time: args.notificationTime,
          Email_Content: args.mailContent,
          Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
          Added_By: loginEmployeeId
        })
    }

    // Return true after successful updates
    return true;

  } catch (err) {
    console.error('Error in updateCurrentCandidateDetails:', err);
    throw err;
  }
}

async function updateCandidateInterviewStatus(organizationDbConnection, args, trx) {
  try {
    const interviewDetails = await organizationDbConnection(ehrTables.interviewCandidates + ' as IC')
      .select('IC.Interview_Id', 'I.PanelMember_Calendar_Event_Id', 'I.Calendar_Type', 'I.Candidate_Calendar_Event_Id')
      .innerJoin(ehrTables.interviewRounds + ' as IR', 'IR.Interview_Id', 'IC.Interview_Id')
      .innerJoin(ehrTables.interviews + ' as I', 'I.Interview_Id', 'IR.Interview_Id')
      .where('IC.Candidate_Id', args.candidateId)
      .where('IR.Round_Status', 'Scheduled')
      .andWhere('IR.Round_Start_Date_Time', '>=', moment().utc().format('YYYY-MM-DD HH:mm:ss'))
      .transacting(trx);

    if (!interviewDetails || interviewDetails.length === 0) {
      return true;
    }
    let interviewIds = interviewDetails.map(interview => interview.Interview_Id)

    await Promise.all([
      organizationDbConnection('interview_candidates').transacting(trx)
        .update({ Round_Status: 'Cancelled' })
        .whereIn('Interview_Id', interviewIds),

      organizationDbConnection('interview_rounds').transacting(trx)
        .update({ Round_Status: 'Cancelled' })
        .whereIn('Interview_Id', interviewIds),
    ]);

    const cancelCalendarEvent = interviewDetails.filter(interview => interview.Calendar_Type)
      .map(interview => cancelMicrosoftCalendarEvent(args, interview));

    if (cancelCalendarEvent && cancelCalendarEvent.length > 0) {
      await Promise.all(cancelCalendarEvent);
    }

    return true;  // Return success when all updates are done

  } catch (err) {
    console.error('Error in updateCandidateInterviewStatus: ', err);
    throw err;  // Rethrow error to handle it in the caller function
  }
}

async function validateDuplicateCandidate(organizationDbConnection, args, employeeId, trx) {
  console.log("Inside validateDuplicateCandidate function")
  try {
    const currentDetails = [args?.firstName?.trim(), args?.middleName?.trim(), args?.lastName?.trim()];

    // Concatenate the details, removing any empty values and joining with spaces
    const concatenatedcurrentDetails = currentDetails.filter(Boolean).join(' ');

    let candidate = await getDuplicateRecords(organizationDbConnection, args, concatenatedcurrentDetails, trx, false)
    if (candidate && candidate.length > 0) {
      const candidateIds = candidate.map(item => item.Candidate_Id);
      let duplicateCount = candidateIds.length;
      let candidateBlacked = candidate.filter(item => item.Blacklisted && item.Blacklisted.toLowerCase() === 'yes')[0] || null;
      const data = await updateCandidateRecord(organizationDbConnection, employeeId, duplicateCount, candidateIds, trx);
      if (data) {
        return { candidateDuplicateCount: duplicateCount, candidateBlackedList: candidateBlacked };
      }
      return { candidateDuplicateCount: 0, candidateBlackedList: candidateBlacked };
    }
    return { candidateDuplicateCount: 0, candidateBlackedList: null };
  } catch (err) {
    console.error('Error in validateDuplicateCandidate main catch() block ', err)
    throw err;
  }
}

async function getDuplicateRecords(organizationDbConnection, args, concatenatedcurrentDetails, trx, validateOnboardedStatus) {
  console.log("Inside getDuplicateRecords function")
  try {
    let candidate = await organizationDbConnection('candidate_personal_info')
      .select('candidate_personal_info.Candidate_Id', 'candidate_personal_info.Emp_First_Name', 'candidate_personal_info.Emp_Last_Name',
        'candidate_personal_info.Personal_Email', 'candidate_personal_info.DOB', 'candidate_contact_details.Mobile_No',
        'candidate_personal_info.Added_On', 'candidate_personal_info.Updated_On', 'candidate_recruitment_info.Blacklisted',
        'candidate_recruitment_info.Blacklisted_Reason_Id', 'candidate_recruitment_info.Blacklisted_Comments', 'candidate_recruitment_info.Blacklisted_Attachment_File_Name')
      .leftJoin('candidate_contact_details', 'candidate_contact_details.Candidate_Id', 'candidate_personal_info.Candidate_Id')
      .leftJoin('candidate_recruitment_info', 'candidate_recruitment_info.Candidate_Id', 'candidate_personal_info.Candidate_Id')
      .whereRaw(`TRIM(CONCAT_WS(' ',
                  CASE 
                      WHEN candidate_personal_info.Emp_First_Name IS NOT NULL AND candidate_personal_info.Emp_First_Name != '' 
                      THEN LOWER(TRIM(candidate_personal_info.Emp_First_Name)) ELSE NULL END,
                  CASE 
                      WHEN candidate_personal_info.Emp_Middle_Name IS NOT NULL AND candidate_personal_info.Emp_Middle_Name != '' 
                      THEN LOWER(TRIM(candidate_personal_info.Emp_Middle_Name)) ELSE NULL END,
                  CASE 
                      WHEN candidate_personal_info.Emp_Last_Name IS NOT NULL AND candidate_personal_info.Emp_Last_Name != '' 
                      THEN LOWER(TRIM(candidate_personal_info.Emp_Last_Name)) ELSE NULL END
              )) = ?`, [concatenatedcurrentDetails.toLowerCase().trim()])
      .where('candidate_personal_info.Personal_Email', args.emailId)
      .where('candidate_personal_info.DOB', args.dob)
      .where('candidate_contact_details.Mobile_No', args.mobileNo)
      .transacting(trx);

    return candidate;
  } catch (err) {
    console.error('Error in getDuplicateRecords main catch() block ', err)
    return false;
  }
}

async function updateCandidateRecord(organizationDbConnection, employeeId, duplicateCount, candidateIds, trx) {
  console.log("Inside updateCandidateRecord function")
  try {
    const data = await organizationDbConnection('candidate_personal_info')
      .update({
        Is_Duplicate: 1,
        Duplicate_Count: duplicateCount + 1,
        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
        Updated_By: employeeId
      })
      .whereIn('Candidate_Id', candidateIds)
      .transacting(trx);
    return data;
  } catch (err) {
    console.error('Error in updateCandidateRecord main catch() block ', err)
    return false;
  }
}

async function cancelMicrosoftCalendarEvent(args, interviewDetails) {
  try {

    if (!args.microsoft_access_token) {
      throw 'MIS0103'; //Sorry! The microsoft calendar could not be processed because the token is missing. Please contact your platform administrator for assistance..
    }

    if (!interviewDetails.PanelMember_Calendar_Event_Id || !interviewDetails.Candidate_Calendar_Event_Id) {
      throw 'MIS0105'; //Sorry! The microsoft calendar event you are trying to delete requires a valid unique ID. Please contact your platform administrator for assistance.
    }

    const createConfig = (eventId) => ({
      method: 'delete',
      maxBodyLength: Infinity,
      url: `${defaultURL.microsoftAPIUrl}/me/events/${eventId}`,
      headers: {
        'Authorization': `Bearer ${args.microsoft_access_token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    const [panelMemberResponse, candidateResponse] = await Promise.all([
      axios.request(createConfig(interviewDetails.PanelMember_Calendar_Event_Id)),
      axios.request(createConfig(interviewDetails.Candidate_Calendar_Event_Id))
    ]);

    console.log("Microsoft calendar delete event responses => ", panelMemberResponse.data, candidateResponse.data);

  } catch (error) {
    console.error("Error in deleteMicrosoftCalendarEvent function", error?.response?.data || error);
    if (error?.response?.data?.error)
      throw commonLib.func.microsoftAPIErrorCode(error.response.data.error.code);
    throw error;
  }
}
async function getEmployeeEmailsByGroup(groupId, organizationDbConnection) {
  try {
    const emails = await organizationDbConnection(
      ehrTables.cusEmpGroupEmployees + ' as CEGE'
    )
      .pluck('EJ.Emp_Email')
      .innerJoin(
        ehrTables.cusEmpGroup + ' as CEG',
        'CEGE.Group_Id',
        'CEG.Group_Id'
      )
      .leftJoin(
        ehrTables.empJob + ' as EJ',
        'CEGE.Employee_Id',
        'EJ.Employee_Id'
      )
      .whereIn('CEGE.Type', ['Default', 'AdditionalInclusion'])
      .where('CEG.Group_Id', groupId)
      .andWhere(function () {
        this.whereNotNull('EJ.Emp_Email').andWhere('EJ.Emp_Email', '!=', '')
      })
      .where('EJ.Emp_Status', 'Active')

    return emails
  } catch (error) {
    console.error('Error retrieving employee emails for group:', groupId, error)
    throw error
  }
}
async function approvedAndForecastpositiondetails(
  data,
  orgCode,
  organizationDbConnection,
  context,
  orgDetails
) {
  try {
    let companyName = ''
    companyName = orgDetails.orgName
    let replyTo = orgDetails.hrAdminEmailAddress
    // Get company logo
    let reportLogoS3Path = null
    let result = await getSenderNameAndLogo(organizationDbConnection)
    let Add_Logo = result?.Add_Logo
    if (
      Add_Logo?.toLowerCase() === 'no' ||
      (context &&
        (context.partnerid?.toLowerCase() === 'entomo' ||
          context.Partnerid?.toLowerCase() === 'entomo'))
    ) {
      // Handle entomo case
    } else {
      reportLogoS3Path = orgDetails.Report_LogoPath
        ? process.env.domainName +
          '_upload/' +
          orgCode +
          '_tmp/logos/' +
          orgDetails.Report_LogoPath
        : ''
    }
    let logoHtml = ''
    if (reportLogoS3Path) {
      logoHtml = `<div style="text-align: center; margin-bottom: 20px;">
              <img src="${reportLogoS3Path}" alt="${companyName}" style="max-height: 80px; max-width: 200px;">
          </div>`
    }

    // Create the main travel details summary section
    const approvedAndForecastpositiondetails = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
          <p style="font-size: 14px; margin-bottom: 20px;">
              We are pleased to inform you that a new <strong>Approved & Forecasted Position</strong> has been requested and is ready for hiring action. Please find the details below:
          </p>
  
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;font-size: 14px;">
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; width: 40%; font-weight: bold;">Position Code:</td>
                  <td style="padding: 10px 0;">
                    ${
                      data?.positionCode
                        ? `${data.positionTitle} - ${data.positionCode}`
                        : 'N/A'
                    }
                  </td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Department:</td>
                  <td style="padding: 10px 0;">${
                    data.departmentName || 'N/A'
                  }</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Division:</td>
                  <td style="padding: 10px 0;">${
                    data.divisionName || 'N/A'
                  }</td>
              </tr>
              <tr style="border-bottom: 1px solid #e2e2e2;">
                  <td style="padding: 10px 0; font-weight: bold;">Group:</td>
                  <td style="padding: 10px 0;">${data.groupName || 'N/A'}</td>
              </tr>
              <tr>
                  <td style="padding: 10px 0; font-weight: bold;">Company:</td>
                  <td style="padding: 10px 0;">${companyName || 'N/A'}</td>
              </tr>
          </table>
  
          <p style="margin-bottom: 20px;font-size: 14px">
              You are requested to <strong>initiate the hiring process</strong> for the above position at the earliest. Kindly ensure the position is posted on relevant platforms, and coordinate with the respective department head to proceed with the recruitment plan.
          </p>
  
          <p style="font-size: 14px;">
              If you have any questions or require further details, feel free to reach out.
          </p>
  
          <p style="margin-top: 20px;font-size: 14px;">
              Best regards,<br>
              ${data.Added_By_Name}
          </p>
      </div>
      `
    let finalEmailBody = `
     <div style="background-color: #f5f5f5; padding: 20px; font-family: Arial, sans-serif;font-size: 14px">
         <div style="max-width: 800px; margin: 0 auto; background-color: #FFFFFF; border-radius: 5px; overflow: hidden; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
             <div style="padding: 20px;">
                 ${logoHtml}
                 ${approvedAndForecastpositiondetails}
             </div>
         </div>
     </div>`
    return {
      replyTo,
      finalEmailBody
    }
  } catch (e) {
    console.log('Error in approvedAndForecastpositiondetails main block', e)
    return ''
  }
}
async function sendApprovedPositionEmail(
  organizationDbConnection,
  email_body,
  email_subject,
  replyTo,
  instanceData
) {
  try {
    // Retrieve email details from the database
    let emailData = await getEmployeeEmailsByGroup(
      instanceData.customGroupId,
      organizationDbConnection
    )
    if (!emailData || emailData.length === 0) {
      return
    }
    let originalSenderName = null
    let result = await getSenderNameAndLogo(organizationDbConnection)
    originalSenderName = result?.senderName
    let source = originalSenderName
      ? `${originalSenderName} <${process.env.emailFrom}>`
      : process.env.emailFrom
    let region = process.env.sesRegion
    const ses = new AWS.SES({ region })
    // Nodemailer transport with SES
    const transporter = nodemailer.createTransport({ SES: ses })

    // Email options
    const mailOptions = {
      from: source ?? process.env.emailFrom,
      subject: email_subject,
      html: email_body,
      to: emailData,
      cc: instanceData.Added_By_Email || '',
      replyTo: replyTo || ''
    }


    // Send email
    const info = await transporter.sendMail(mailOptions)
    return info ? 'Invited' : 'Pending'
  } catch (error) {
    console.error('Error in sendApprovedPositionEmail:', error)
    throw error
  }
}
async function getSenderNameAndLogo(organizationDbConnection) {
  try {
    let data = await organizationDbConnection('email_notification_setting')
      .select('Sender_Name as senderName', 'Add_Logo')
      .first()

    if (data) {
      return data
    }
    return false
  } catch (err) {
    console.log('Error in getSenderName function.', err)
    return false
  }
}
async function getEmployeeEmail(employeeId, organizationDbConnection) {
  const emails = await organizationDbConnection(ehrTables.empJob + ' as EJ')
    .select(
      'EJ.Emp_Email',
      organizationDbConnection.raw(`
        CONCAT_WS(" ", EP_U.Emp_First_Name, EP_U.Emp_Middle_Name, EP_U.Emp_Last_Name) as Added_By_Name
      `)
    )
    .where('EJ.Employee_Id', employeeId)
    .andWhereNot('EJ.Emp_Email', '')
    .andWhereNot('EJ.Emp_Email', null)
    .leftJoin(
      `${ehrTables.empPersonalInfo} as EP_U`,
      'EP_U.Employee_Id',
      'EJ.Employee_Id'
    )

  return emails
}

async function generateAndUploadReport({ 
  orgCode,
  reportData,
  reportType,
  fileName = null,
  New_File_Content = null,
  contentType = null,
}) {
  const bucketName = process.env.offlineReportBucket;
  const reportFileName = fileName || getDefaultFileName(reportType);
  const tempDir = os.tmpdir();
  const tempFilePath = path.join(tempDir, reportFileName);

  try {
    if (!fs.existsSync(tempDir)) fs.mkdirSync(tempDir, { recursive: true });

    const workbook = new ExcelJS.Workbook();

    // Handle both single sheet and multi-sheet data
    // Check if it's multi-sheet data (array of sheet objects with sheet names as keys)
    if (Array.isArray(reportData) && reportData.length > 0 &&
        typeof reportData[0] === 'object' &&
        !Array.isArray(reportData[0]) &&
        Object.keys(reportData[0]).length === 1 &&
        Array.isArray(Object.values(reportData[0])[0])) {
      // Multi-sheet data (array of sheet objects)
      reportData.forEach((sheetObj, index) => {
        const sheetName = Object.keys(sheetObj)[0] || `Sheet${index + 1}`;
        const sheetData = sheetObj[sheetName];

        if (Array.isArray(sheetData)) {
          const worksheet = workbook.addWorksheet(sheetName);
          if (sheetData.length > 0) {
            worksheet.columns = Object.keys(sheetData[0]).map(key => ({ header: key, key }));
            worksheet.addRows(sheetData);

            // Style header row with light blue fill
            worksheet.getRow(1).eachCell(cell => {
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FF92CDDC' }
              };
              cell.font = { bold: true };
            });
          }
        }
      });
    } else if (Array.isArray(reportData) && reportData.length > 0 && typeof reportData[0] === 'object') {
      // Single sheet data (array of objects
      const worksheet = workbook.addWorksheet('ReportData');
      worksheet.columns = Object.keys(reportData[0]).map(key => ({ header: key, key }));
      worksheet.addRows(reportData);

      // Style header row with light blue fill
      worksheet.getRow(1).eachCell(cell => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF92CDDC' }
        };
        cell.font = { bold: true };
      });
    } else {
      // Fallback for empty or invalid data
      const worksheet = workbook.addWorksheet('ReportData');
    }

    await workbook.xlsx.writeFile(tempFilePath);

    const s3Path = `${orgCode}/${reportType}/${moment().format('YYYYMMDD')}/${reportFileName}`;
    let uploadResponse;

    if (New_File_Content && contentType) {
      const buffer = Buffer.from(New_File_Content, 'base64');
      const uploadParams = {
        Bucket: bucketName,
        Key: s3Path,
        Body: buffer,
        ContentType: contentType,
        ContentEncoding: 'base64',
        ServerSideEncryption:s3FileUpload.defaultEncryption,
      };
      uploadResponse = await s3.upload(uploadParams).promise();
    } else {
      const fileContent = fs.readFileSync(tempFilePath);
      const uploadParams = {
        Bucket: bucketName,
        Key: s3Path,
        Body: fileContent,
        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ServerSideEncryption:s3FileUpload.defaultEncryption,
      };
      uploadResponse = await s3.upload(uploadParams).promise();
    }

    fs.unlinkSync(tempFilePath);

    let s3PresignedUrl = s3.getSignedUrl("getObject", {
        Bucket: bucketName,
        Key: s3Path,
        Expires: 60000
    });

    return {
      success: true,
      s3Url: s3PresignedUrl,
      s3Path
    };
  } catch (error) {
    if (fs.existsSync(tempFilePath)) fs.unlinkSync(tempFilePath);
    throw error;
  }
}

function getDefaultFileName(reportType) {
  const fileNames = {
    'APPROVED_FORECAST': 'Approved & Forecasted Positions List.xlsx',
    'NEW_POSITION': 'New Position & Additional Headcount List.xlsx',
    'ORG_STRUCTURE': 'Table of Organization.xlsx',
    'HIRING_FORECAST': 'Hiring Forecast.xlsx'
  };
  return fileNames[reportType] || 'Report.xlsx';
}


async function getAPIIntegrationCredentials(organizationDbConnection, integrationType) {
  try {
      const apiIntegrationCredentials = await organizationDbConnection(ehrTables.externalApiCredentials)
      .select('Integration_Type', 'Credential1', 'Credential2', 'Api_Url as baseURL', 'Sync_Type')
      .where('Integration_Type', integrationType).whereNotNull('Credential1').whereNotNull('Api_Url').first();
      if (apiIntegrationCredentials) {
          return apiIntegrationCredentials;
      }
      return false;
   
  } catch (error) {
      console.error('Error occured in validateAPIIntegrationActive() main catch block ', error);
      return false;
  }

}

async function decryptAPICredentials(integrationCredentials, isError){
  let credentialsArray = [];
  try {
      let credentialsToDecrypt = [integrationCredentials.Credential1];
      if(integrationCredentials.Credential2){
        credentialsToDecrypt.push(integrationCredentials.Credential2)
      }
      for(let textToDecrypt of credentialsToDecrypt){
          let decryptionInputs ={ 
              isBase64SecretKey: 1,
              isEncryptedInputEncoded: 0,
              textToDecrypt: textToDecrypt ? textToDecrypt : '',
              decryptionAlgorithm: process.env.decryptionAlgorithm,//algorithm used for decryption
              decryptionSecretKeyName: process.env.decryptionSecretKeyName,
              allowEmptySecretKey: 1
          };
          let decryptedText = await getDecryptedString(decryptionInputs);
          
          credentialsArray.push(decryptedText);
      }
      return credentialsArray;
  } catch (error) {
      console.error('Error occured in decryptSyntrumCredentials main catch block ', error);
      return isError ? error : false;
  }
}



module.exports = {
  getCredentials,
  getOrgDetails,
  camuConversion,
  updateCamuIdInEmpJob,
  updateEmployeeInfoTimestampLog,
  callCreateStaffApi,
  validateIrukkaCandidateDetails,
  validateJobStreetCommonRuleInput,
  checkJobStreetRecordExist,
  jobPostDetailss,
  getHirerId,
  mapErrorToCustomCodeSeek,
  checkJobOpeningRecordExist,
  deleteLinkedInJob,
  getUserData,
  getJobStreetAccessToken,
  getDecryptedString,
  checkJobStreetRecordExistJobPostId,
  fetchAndUploadFile,
  uploadFileToS3Bucket,
  deleteFileFromS3Bucket,
  getFileExtension,
  getWebHookSecret,
  formBodyForEmail,
  getRecruiterEmailAddress,
  validatCommonRuleInput,
  getWorkflowProcessInstanceId,
  initaiteWorkflow,
  updateWorkflowProcessInstanceId,
  getWorkflowProcessInstanceData,
  calculateStartMonth,
  checkDeletionEligibility,
  approvedPositionValidationSkip,
  updateCandidateInterviewStatus,
  updateCandidateDetails,
  validateDuplicateCandidate,
  getDuplicateRecords,
  approvedAndForecastpositiondetails,
  sendApprovedPositionEmail,
  getEmployeeEmail,
  generateAndUploadReport,
  getDefaultFileName,
  getAPIIntegrationCredentials,
  decryptAPICredentials
};