// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');

module.exports.moveToJobPost = async (parent, args, context, info) => {
    let organizationDbConnection;
    console.log("Inside moveToJobPost function.");
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        let loginEmployeeId = context.Employee_Id;
        if(!args.jobPostId || !args.status || !args.candidateId){
            throw 'IVE0000'
        }
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId);
        if (Object.keys(checkRights).length > 0 && checkRights.Role_Update === 1) {
        
            await organizationDbConnection.transaction(async (trx) => {

                let [candidate, jobPost] = await Promise.all([
                    organizationDbConnection(ehrTables.candidatePersonalInfo+ ' as CPI').select('CPI.Talent_Pool_Id', 'CRI.Archived', organizationDbConnection.raw("CONCAT(CPI.Emp_First_Name, ' ', COALESCE(CPI.Emp_Middle_Name, ''), ' ', CPI.Emp_Last_Name) as candidateName"))
                    .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                    .where('CPI.Candidate_Id', args.candidateId).first(),

                    organizationDbConnection(ehrTables.jobPost+' as JP').select('JP.Job_Post_Name', 'ST.Status')
                    .leftJoin(ehrTables.atsStatusTable + ' as ST', 'ST.Id', 'JP.Status')
                    .where('Job_Post_Id', args.jobPostId).first()
                ])

                if (!candidate  || !jobPost || candidate?.Archived.toLowerCase() === 'yes' || 
                !candidate?.Talent_Pool_Id || 
                (jobPost.Status && jobPost.Status.toLowerCase() === 'closed') || (args.talentPoolId && candidate.Talent_Pool_Id !== args.talentPoolId)) {
                    throw !candidate ? 'EI00224' : //Sorry! An error occurred while processing the candidate details. Please try again.
                          candidate?.Archived.toLowerCase() === 'yes' ? 'TAP0114' : //Apologies! The candidate details have already been archived in a different user session.
                          (!candidate?.Talent_Pool_Id || candidate.Talent_Pool_Id !== args.talentPoolId) ? 'TAP0109' : //Sorry, an error occurred while transferring the candidate from the talent pool to the job post
                          'TAP0111'; //The selected job post has already been closed and cannot be chosen.
                }

                await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
                .transacting(trx).update({
                    Job_Post_Id: args.jobPostId,
                    Candidate_Status: args.status,
                }).where('Candidate_Id', args.candidateId);

                let result = await organizationDbConnection(ehrTables.candidatePersonalInfo)
                .transacting(trx).update({ Talent_Pool_Id: null }).where('Candidate_Id', args.candidateId);

                if(!result) throw 'TAP0110';

                
                
                await commonLib.func.createSystemLogActivities({
                    userIp: context.User_Ip,
                    employeeId: loginEmployeeId,
                    changedData: args,
                    organizationDbConnection: trx,
                    formId: args.formId,
                    action: 'Moved Pool',
                    isEmployeeTimeZone: 0,
                    uniqueId: args.candidateId,
                    message: `The candidate has been reassigned to the job post titled as ${jobPost.Job_Post_Name}.`
                });


                let systemLogParam = {
                    userIp: context.User_Ip,
                    employeeId: loginEmployeeId,
                    changedData: args,
                    organizationDbConnection,
                    message: `${candidate.candidateName} - ${args.candidateId} has been transferred from the talent pool to the job post ${jobPost.Job_Post_Name} - ${args.jobPostId}.`
                };
        
                await commonLib.func.createSystemLogActivities(systemLogParam);
            })  

            return { errorCode: '', message: 'The candidate has been transferred from the talent pool to the job post successfully' };

        }else{
            throw '_DB0102'; //This employee do not have edit access rights
        }
    }
    catch (e) {
        console.error('Error while moveToJobPost main catch block.', e);
        let errResult = commonLib.func.getError(e, 'TAP0109'); //Sorry, an error occurred while processing the request to moving talent pool to candidate.
        throw new ApolloError(errResult.message, errResult.code);
    }finally {
        if (organizationDbConnection) {
            organizationDbConnection.destroy();
        }
    }
}
