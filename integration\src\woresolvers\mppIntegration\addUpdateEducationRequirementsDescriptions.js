const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');

module.exports.addUpdateEducationRequirementsDescriptions = async (parent, args, context) => {
  let validationError = {};
  const { Employee_Id: loginEmployeeId, User_Ip: userIp } = context;
  let organizationDbConnection;

  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId.newPosition
    );

    // Check if the employee has the required access rights
    if (Object.keys(checkRights).length <= 0 || 
        (args.input && checkRights.Role_Update === 0) || 
        (!args.input && checkRights.Role_Add === 0)) {
      throw !args.input ? '_DB0101' : '_DB0102';
    }

    const {
      action,
      positionRequestId,
      input,
    } = args;
    if (!input || input.length === 0) {
      throw 'EI00179';
    }
    // Start a transaction
    return await organizationDbConnection.transaction(async (trx) => {
      const educationRequirementData = input.map(desc => ({
        Mpp_Education_Requirements_Descriptions_Id: 0,
        Position_Request_Id: desc.positionRequestId || null,
        Description: desc.educationDescription || null,
        Mpp_Education_Requirements_Id: desc.educationRequirementId || null,
      }));
      if (action === 'update') {
        let idsToDelete = input
        .filter((desc) => desc.mppEducationRequirementsDescriptionsId !== 0)
        .map((desc) => desc.mppEducationRequirementsDescriptionsId);
        await trx(ehrTables.mppEducationRequirementsDescriptions)
          .whereIn('Mpp_Education_Requirements_Descriptions_Id', idsToDelete)
          .del();
      }
      const result = await trx(ehrTables.mppEducationRequirementsDescriptions)
        .insert(educationRequirementData);
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection: trx,
        message: `Education requirements added successfully`,
      });
      return {
        errorCode: '',
        message: `Education requirements added successfully`
      };
    });
  } catch (error) {
    console.error('Error in addUpdateEducationRequirementsDescriptions function main catch block.', error);
    if (error === 'IVE0000') {
      console.log('Validation error in addUpdateEducationRequirementsDescriptions function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      const errResult = commonLib.func.getError(error, 'EI00186');
      throw new ApolloError(errResult.message, errResult.code);
    }
  } finally {
    // Clean up the database connection
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};