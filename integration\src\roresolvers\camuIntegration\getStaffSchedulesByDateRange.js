//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
//Require tables
const { ehrTables } = require('./../../../common/tablealias')
//Require axios
const axios = require('axios');
//Require moment
const moment = require('moment');

//List the lecture schedules details from camu.
module.exports.getStaffSchedulesByDateRange = async (parent, args, context, info) => {
    let appmanagerDbConnection;
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log("Inside getStaffSchedulesByDateRange () function.")
        validationError = await commonLib.func.validateInputDateAndGetDesiredDateFormat(args.fromDate, args.toDate, {}, 1);
        if (!args.staffId) {
            validationError['IVE0257'] = commonLib.func.getError('', 'IVE0257').message;
        }
        if (Object.keys(validationError['validationError']).length === 0) {

            let input = {
                "staffId": args.staffId,
                "fromDt": validationError['receivedDate'],
                "toDt": validationError['expiryDate']
            };

            let orgCode = context.Org_Code;
            // get the appmanager database connections
            appmanagerDbConnection = knex(context.connection.AppManagerDb);
            organizationDbConnection = knex(context.connection.OrganizationDb);

            let employeeIdResult = await organizationDbConnection(ehrTables.empJob)
                .pluck('Employee_Id')
                .where('User_Defined_EmpId', args.staffId)
                .then();
            let employeeId = employeeIdResult[0];
            let camuDetails = await commonLib.employees.getCamuClientToken(appmanagerDbConnection, orgCode, organizationDbConnection, [employeeId]);
            if (camuDetails.fieldForceEnabled == 1) {
                camuToken = camuDetails['empCamuDetails'][employeeId][0]['Client_Access_Token'];
                camuBaseUrl = camuDetails['empCamuDetails'][employeeId][0]['Camu_Base_Url'];
            } else {
                camuToken = camuDetails['empCamuDetails']['Client_Access_Token'];
                camuBaseUrl = camuDetails['empCamuDetails']['Camu_Base_Url'];
            }
            appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
            if (!camuBaseUrl) {
                throw ('EI00118')
            }
            let getStaffSchedulesByDateRangeUrl = camuBaseUrl + 'staff/external/get-staff-schedule-by-staffid-and-date-range';
            if (!camuToken) {
                throw ("EI00103")
            }
            // console.log("input:",input," getStaffSchedulesByDateRangeUrl",getStaffSchedulesByDateRangeUrl," camuToken",camuToken);
            let lectureSchedules = await callGetStaffSchedulesByDateRangeApi(input, getStaffSchedulesByDateRangeUrl, camuToken);
            if (lectureSchedules || lectureSchedules.length > 0) {
                let groupAccordingToEventDate = {};
                let finalLectureSchedules = [];
                for (let i = 0; i < lectureSchedules.length; i++) {
                    let keyChangedValue = {
                        'id': lectureSchedules[i]["_id"],
                        'academicYear': lectureSchedules[i]['acyrNm'],
                        'courseName': lectureSchedules[i]['crsNm'],
                        'day': lectureSchedules[i]['Day'],
                        'departmentName': lectureSchedules[i]['dptNm'],
                        'eventDate': lectureSchedules[i]['eventDate'],
                        'fromTime': lectureSchedules[i]['FrTime'],
                        'locationName': lectureSchedules[i]['locNm'],
                        'programName': lectureSchedules[i]['prNm'],
                        'sectionName': lectureSchedules[i]['secNm'],
                        'semester': lectureSchedules[i]['semNm'],
                        'slotDuration': lectureSchedules[i]['SltDur'],
                        'staffID': lectureSchedules[i]['stfId'],
                        'staffName': lectureSchedules[i]['stfNm'],
                        'subject': lectureSchedules[i]['subjNm'],
                        'toTime': lectureSchedules[i]['ToTime'],
                        'type': lectureSchedules[i]['type']
                    }
                    if (groupAccordingToEventDate[lectureSchedules[i]['eventDate']]) {
                        groupAccordingToEventDate[lectureSchedules[i]['eventDate']].push(keyChangedValue)
                    }
                    else {
                        groupAccordingToEventDate[lectureSchedules[i]['eventDate']] = [keyChangedValue]
                    }
                }
                for (let key in groupAccordingToEventDate) {
                    let finalLectureSchedulesJson = { eventDate: key, schedules: groupAccordingToEventDate[key] };
                    finalLectureSchedules.push(finalLectureSchedulesJson);
                }

                //Get the processed schedules
                let processedSchedules = await processScheduledData(organizationDbConnection, args, employeeId, finalLectureSchedules)

                return { errorCode: '', message: 'Staff schedules retrieved successfully.', allSchedules: finalLectureSchedules, processedSchedules };
            }
            else {
                console.log('Error in the getStaffSchedulesByDateRange () function, lectureSchedules', lectureSchedules, input, getStaffSchedulesByDateRangeUrl, camuToken)
                throw ('EI00105')
            }
        }
        else {
            console.log('Validation error in getStaffSchedulesByDateRange () function.', validationError);
            throw ('IVE0000')
        }
    }
    catch (e) {
        console.log('Error in getStaffSchedulesByDateRange () main catch block.', e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy() : null;
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        if (e == 'IVE0000') {
            let errResult = commonLib.func.getError(e, 'IVE0000');
            // return response
            throw new UserInputError(errResult.message, { validationError: validationError });
        }
        errResult = commonLib.func.getError(e, 'EI00111');
        // return error response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

/**
 * Processes scheduled data for an employee within a specified date range.
 *
 * @param {Object} organizationDbConnection - The database connection object for the organization.
 * @param {Object} args - The arguments object containing the date range and duration.
 * @param {string} args.fromDate - The start date of the range in 'YYYY-MM-DD' format.
 * @param {string} args.toDate - The end date of the range in 'YYYY-MM-DD' format.
 * @param {string} args.duration - The duration of the schedule (e.g., "0.5" for half day, "0.25" for quarter day).
 * @param {string} employeeId - The ID of the employee.
 * @param {Array} schedules - The array of schedule objects.
 * @param {string} schedules[].eventDate - The date of the schedule event in 'YYYY-MM-DD' format.
 * @param {Array} schedules[].schedules - The array of individual schedules for the event date.
 * @param {string} schedules[].schedules[].fromTime - The start time of the schedule in 'HH:mm' format.
 * @param {string} schedules[].schedules[].toTime - The end time of the schedule in 'HH:mm' format.
 * @returns {Promise<Array>} A promise that resolves to an array of processed schedules.
 * @throws Will throw an error if there is an issue processing the scheduled data.
 */
async function processScheduledData(organizationDbConnection, args, employeeId, schedules) {
    try {
        console.log("Inside processScheduledData function");
        let startDate = moment(args.fromDate);
        let endDate = moment(args.toDate);
        let processedSchedules = [];

        for (let start = startDate; start <= endDate; start = start.add(1, 'days')) {
            let eventDate = start.format('YYYY-MM-DD');
            let eventSchedules = schedules.filter(schedule => schedule.eventDate === eventDate);

            let isScheduled = eventSchedules.length > 0;
            let isScheduledPartially = false;

            if (isScheduled && (args.duration === "0.5" || args.duration === "0.25")) {
                let workSchedule = await getEmployeeWorkSchedule(organizationDbConnection, args, employeeId);
                if (workSchedule) {
                    //Set eventSchedules to first element of array as half day and quarter day schedules are not for multiple days
                    eventSchedules = eventSchedules[0]
                    let eventDate = eventSchedules.eventDate;
                    for (let schedule of eventSchedules.schedules) {
                        const fromTime = moment(`${eventDate} ${schedule.fromTime}`, "YYYY-MM-DD HH:mm");
                        const toTime = moment(`${eventDate} ${schedule.toTime}`, "YYYY-MM-DD HH:mm");

                        let timeOffPeriod = commonLib.shiftAndTimeManagement.generateTimeOffHours(workSchedule, args.leavePeriod);
                        if (timeOffPeriod) {
                            timeOffPeriod.startDateTime = moment(timeOffPeriod.startDateTime, "YYYY-MM-DD HH:mm");
                            timeOffPeriod.endDateTime = moment(timeOffPeriod.endDateTime, "YYYY-MM-DD HH:mm");
                        }

                        if (timeOffPeriod?.startDateTime && timeOffPeriod?.endDateTime) {
                            if (fromTime.isSameOrAfter(timeOffPeriod.startDateTime) && toTime.isSameOrBefore(timeOffPeriod.endDateTime)) {
                                isScheduledPartially = true;
                                break;
                            }
                        }
                    };
                }
            }

            processedSchedules.push({
                eventDate: eventDate,
                isScheduled: isScheduled,
                isScheduledPartially: isScheduledPartially,
                colorCode: isScheduled ? '#FFB7B2' : '#B5EAD7'
            });
        }
        return processedSchedules;
    }
    catch (e) {
        console.log("Error occurred while processing scheduled data", e);
        throw e
    }
}

/**
 * Retrieves the work schedule for a specific employee within a given date range.
 *
 * @param {Object} organizationDbConnection - The database connection object for the organization.
 * @param {Object} args - The arguments object containing the date range.
 * @param {string} args.fromDate - The start date of the range.
 * @param {string} args.toDate - The end date of the range.
 * @param {string} employeeId - The ID of the employee whose work schedule is to be retrieved.
 * @returns {Promise<Object|null>} - A promise that resolves to the employee's work schedule details or null if not found.
 * @throws Will throw an error if the employee is not found or if there is an issue retrieving the work schedule.
 */
async function getEmployeeWorkSchedule(organizationDbConnection, args, employeeId) {
    try {
        const [workScheduleDetails, employeeDetails, rosterSettings] = await Promise.all([
            commonLib.shiftAndTimeManagement.getAllWorkScheduleDetails(organizationDbConnection, args.fromDate, args.toDate),
            commonLib.employees.getActiveEmployeesByDateRange(organizationDbConnection, [employeeId], args.fromDate, args.toDate),
            commonLib.shiftAndTimeManagement.getRosterManagementSettings(organizationDbConnection),
        ]);

        if (!employeeDetails?.length) throw 'Employee not found';
        const employee = employeeDetails[0];

        // Fetch shift details only if the employee follows a 'shift roster' schedule
        let shiftDetails = null;
        if (employee?.Work_Schedule?.toLowerCase() === 'shift roster') {
            shiftDetails = await commonLib.shiftAndTimeManagement.getAllEmployeeShiftDetails(
                organizationDbConnection, [employee], args.fromDate, args.toDate
            );
            shiftDetails = shiftDetails?.[employeeId];
        }

        const workSchedule = workScheduleDetails?.[employee.WorkSchedule_Id] || null;

        return employee?.Work_Schedule?.toLowerCase() === 'shift roster'
            ? await commonLib.shiftAndTimeManagement.getWorkScheduleDetailsByDateForShiftRosterEmployees(args.fromDate, shiftDetails, workScheduleDetails, rosterSettings)
            : workSchedule?.[args.fromDate] || null;

    } catch (e) {
        console.error("Error in getEmployeeWorkSchedule:", e);
        throw e;
    }
}


async function callGetStaffSchedulesByDateRangeApi(data, getStaffSchedulesByDateRangeUrl, camuToken) {
    try {

        const config = {
            method: 'post',
            url: getStaffSchedulesByDateRangeUrl,
            maxBodyLength: Infinity,
            data: data,
            headers: {
                'Content-Type': 'application/json',
                'api-key': camuToken
            }
        };
        return (
            axios.request(config)
                .then(response => {
                    let data = response.data ? response.data : [];
                    return data
                })
                .catch(e => {
                    console.log("Error occurred while calling getStaffSchedulesByDateRange function .catch block", e);
                    if (e && e.response && e.response.data) {
                        if (e.response.data.errorMessage == 'SCHEDULE_NOT_FOUND' || e.response.data.errorMessage == 'TIM_DEF_NT_FND') {
                            throw ('EI00105')
                        }
                        else if (e.response.data.errorMessage == 'STAFF_ID_MISSING') {
                            throw ('EI00106')
                        }
                        else if (e.response.data.errorMessage == 'STAFF_NOT_FOUND') {
                            throw ('EI00107')
                        }
                        else if (e.response.data.errorMessage == 'DATE_FIELDS_MISSING') {
                            throw ('EI00108')
                        }
                        else if (e.response.data.errorMessage == 'WORNG_DATE_RANGE') {
                            throw ('EI00109')
                        }
                        else if (e.response.data.errorMessage == 'INVALID_DATE_FORMAT') {
                            throw ('EI00110')
                        }
                        else {
                            throw ('EI00111')
                        }
                    }
                })
        )
    }
    catch (e) {
        console.log("Error occurred while calling getStaffSchedulesByDateRange function main catch block", e);
        throw ('EI00111')
    }
}
