//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const {UserInputError, ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const {formId} = require('../../../common/appConstants');
const {checkJobStreetRecordExist, mapErrorToCustomCodeSeek}=require('../../common/commonFunction');
const {closeJobProfile,deletepositionProfile}=require('../../queries/jobStreetQueries')
const moment=require('moment');
//Require axios
const axios = require('axios');

module.exports.closeJobStreetJob = async (parent, args, context, info) => {
    let organizationDbConnection;
    let errResult;
    const loginEmployee_Id = context.Employee_Id;
    const formIds = formId.jobpost;
    organizationDbConnection = knex(context.connection.OrganizationDb);
    let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployee_Id, '', '', 'UI', false, formIds);
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Update === 1) && checkRights.Is_Recruiter.toLowerCase() === 'yes') {
            const {
                jobStreetId,
            } = args;
            let existingRecord= await checkJobStreetRecordExist(organizationDbConnection,jobStreetId,["Applied"]);
            if(!existingRecord || existingRecord.length<=0){
              throw 'EI00121';
            }
        let jobOpeningData = {
            "Applied_Status": "Closed"
        };
        let jobProfileData={
            "Applied_Status": "Closed"}
            const profileId=existingRecord[0].Profile_Id;
            const variables ={
                "input": {
                  "positionProfile": {
                    "profileId":profileId
                  }
                }
              }
            const ACCESS_TOKEN = 
            context.jobstreet_access_token
            const GRAPHQL_ENDPOINT = process.env.jobStreetTokenAPI;
            let query =closeJobProfile
            try {
                const response = await axios.post(
                    GRAPHQL_ENDPOINT,
                    { query, variables },
                    { headers: { Authorization: `Bearer ${ACCESS_TOKEN}` } }
                );
                if (
                    response.data &&
                    response.data.errors &&
                    response.data.errors.length
                  ) {
                    let errorCode = await mapErrorToCustomCodeSeek(response.data)
                    if (errorCode === 'CH0001') {
                      console.log('unathentcated request check the tokens')
                    } else if (errorCode === 'CH0003') {
                      console.log('check the validations bad_input error')
                    }
                    console.log(response.data.errors,"error form seek api")
        
                    throw 'EI00157'
                }
                if (
                    response && response.data.data &&
                    response.data.data.closePostedPositionProfile &&
                    response.data.data.closePostedPositionProfile.positionProfile
                      .profileId &&
                      response.data.data.closePostedPositionProfile.positionProfile
                      .profileId.value
                  ) {
                    return(
                        await organizationDbConnection.transaction(async (trx) => {
                                jobOpeningData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                jobOpeningData.Updated_By = loginEmployee_Id;
                                jobProfileData.Updated_On = moment().utc().format('YYYY-MM-DD HH:mm:ss');
                                jobProfileData.Updated_By = loginEmployee_Id;
                            
                        await organizationDbConnection(ehrTables.jobStreetJobOpenings)
                                .transacting(trx)
                                .where('Job_Street_Id',jobStreetId)
                                .update(jobOpeningData).catch((error)=>{
                                    console.log('Error in closeJobStreetJob .catch() block', error);
                                    throw error;
                                })
                            await organizationDbConnection(ehrTables.jobStreetJobProfile)
                            .transacting(trx)
                            .where('Job_Street_Id',jobStreetId)
                            .update(jobProfileData).catch((error)=>{
                                console.log('Error in closeJobStreetJob .catch() block', error);
                                throw error;
                            });
                            let systemLogParam = {
                                userIp: context.User_Ip,
                                employeeId: loginEmployee_Id,
                                organizationDbConnection: organizationDbConnection,
                                message: "Job posted closed sucessfuly",
                              };
                              await commonLib.func.createSystemLogActivities(systemLogParam);
                              organizationDbConnection ? organizationDbConnection.destroy() : null;
                            return { errorCode: "", message: `Job profile closed sucessfuly`};
                
                        }));
                  }
                else{
                  console.log("response not found from  close job post seek")
                    throw 'EI00158'
                }
            } catch (error) {
              console.log(error,"error in closeJobstreetJob function")
              if (error && error.length && error.response  &&error.response.data) {
                  let errorCode = await mapErrorToCustomCodeSeek(error.response.data)
                  if (errorCode === 'CH0001') {
                    console.log(
                      'unathentcated request check the tokens in catch block'
                    )
                  } else if (errorCode === 'CH0003') {
                    console.log(
                      'check the validations bad_input error in catch block'
                    )
                  }
      
                  throw 'EI00159'
                } else {
                  throw 'EI00160'
                }
            }
    }else{
      console.log(checkRights,"checkRights")
      if(Object.keys(checkRights).length <0 || checkRights.Role_Update !== 1){
        console.log("The employee does not have delete access.");
        throw '_DB0102';
      }
      else{
        throw '_DB0115'
      }
    }
    } catch (e) {
        console.log('Error in closeJobStreetJob  function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        errResult = commonLib.func.getError(e, 'EI00160');
        throw new ApolloError(errResult.message, errResult.code);
        
    }
}

