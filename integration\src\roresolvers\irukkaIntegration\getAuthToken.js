//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
let moment = require('moment');
const axios = require('axios');
//Require table alias
const { ehrTables } = require('../../../common/tablealias');
const {formName} = require('../../../common/appConstants');

//function to get Auth tokens
module.exports.getAuthToken = async (event, args, context, info) => {
    console.log('Inside getAuthToken function');
    const irukkaAppId = context.irukka_app_id;
    const irukkaSecretKey = context.irukka_secret_key;
    try {
        if (irukkaAppId && irukkaSecretKey) {
            try {
                let tokens =  await getUserData(irukkaAppId, irukkaSecretKey);
                return { errorCode: "", message: "<PERSON><PERSON>ka getAuthToken created successfully.", getData: tokens};
                
            } catch (error) {
                throw error;
                
            }
        } 
        else{
            console.log("Either IrukkaAppId or IrukkaSecretKey is missing");
            throw 'SET0112';
            
        }
    } catch (mainCatchError) {
        console.log('Error in getAuthToken function main block', mainCatchError);
        let errResult = commonLib.func.getError(mainCatchError, 'SET0009');
        // return response
        throw new ApolloError(errResult.message, errResult.code);
    }
}

async function getUserData(irukkaAppId, irukkaSecretKey) {
    try {
        let userData = {};
        // Require aws-sdk
        const AWS = require('aws-sdk');
        // Create client for secrets manager
        let client = new AWS.SecretsManager({
            region: process.env.region
        });
        // Get credentials from secrets manager and do signin
        // Get secrets from aws secrets manager
        let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
        // parse keys from secrets manager
        secretKeys = JSON.parse(secretKeys.SecretString);
    
        // get the common credentials from secrets manager
        let userName = secretKeys.irukkafbusername;
        let password = secretKeys.irukkafbpwd;
        let irukkaToHrappPublicKey = secretKeys.irukkatohrapp;
        const irukkaSecretKeys = JSON.parse(secretKeys.irukka);
        // Parse the JSON string into a JavaScript object
        const irukkaSecretKeyObject = JSON.parse(irukkaSecretKeys);
        const appId = irukkaSecretKeyObject.authApiKey;
        const secretKey = irukkaSecretKeyObject['authSecretKey'];
        //irukkaToHrappPublicKey = irukkaToHrappPublicKey.split("\\");

        if(userName && password){
            // call fire-base rest API to signin into application.
            if(appId == irukkaAppId && secretKey == irukkaSecretKey){
                let token = await axios.post(process.env.signInAPI + process.env.firebaseApiKey, {
                    "email": userName,
                    "password": password,
                    "returnSecureToken": true
                    })
                        .then(function (successResponse) {
                            userData = successResponse;
                            let tokens = {};
                            tokens["authToken"] = userData.data.idToken;
                            tokens["refreshToken"] = userData.data.refreshToken;
                            return tokens;
                        })
                        .catch(function (stealthModeSigninInsideCatchError) {
                            console.log("Error in stealthModeSignin() function axios post API block", stealthModeSigninInsideCatchError);
                            throw stealthModeSigninInsideCatchError; 
                        });
                    return token;
            }
            else{
                console.log("Either IrukkaAppId or the IrukkaSecret Key is not matching");
                throw  'SET0113';
            }

        }
    }
    catch(err){
        console.log("Here in the main .catch block");
        throw err;
    }
}

