{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "dbSecretName": "prod/hrapp/pgaccess", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::************:role/LambdaMicroservice", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:************:function:ATS-prod-firebaseauthorizer", "customDomainName": "api.hrapp.co", "firebaseApiKey": "AIzaSyAupi9_2ATYi05M7hfgO3pZFqF1dNGK7tk", "logoBucket": "s3.logos.hrapp.co", "emailFrom": "<EMAIL>", "sesTemplatesRegion": "us-west-2", "documentsBucket": "s3.taxdocs.hrapp.co", "offlineReportBucket": "offlinereports.hrapp.co", "webAddress": ".co", "emailTo": "<EMAIL>", "sesRegion": "us-west-2", "camuCreateStaffEndPoint": "external/staff", "camuExitStaffEndPoint": "external/staff/exit", "dailyCamuResignationArn": "arn:aws:states:ap-south-1:************:stateMachine:prod-integrationCamuResignation", "irukkaCloseJobUrl": "https://aju0i48d0f.execute-api.ap-south-1.amazonaws.com/uat/partner/api/v1/partnerIntegration/closeJob/a4ae0c94-0051-4f8f-9122-1e7cbb4bf03e", "irukkaPartnerId": "82b03fe2-2f77-477d-8cfb-5d3e58b094e3", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:************:function:INTEGRATION-prod", "signInAPI": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=", "indeedAuthTokenAPI": "https://apis.indeed.com/oauth/v2/tokens", "indeedPublishJobAPI": "https://apis.indeed.com/graphql", "sunFishesAPI": "https://sf7dev-api.dataon.com/sfapi/", "jobStreetAuthTokenAPI": "https://auth.seek.com/oauth/token", "jobStreetAuthBrowserTokenAPI": "https://graphql.seek.com/auth/token", "jobStreetTokenAPI": "https://graphql.seek.com/graphql", "pagtAPIURL": "https://testentomoapi.punongbayan-araullo.com/api", "asyncSunFishAPIPushFunction": "arn:aws:states:ap-south-1:************:stateMachine:prod-asyncSunFishAPIPushFunction", "asyncPAGTAPIPushFunction": "arn:aws:states:ap-south-1:************:stateMachine:prod-asyncPAGTAPIPushFunction", "asyncJobStreetWebHookFunction": "arn:aws:states:ap-south-1:************:stateMachine:prod-asyncJobStreetWebHookFunction", "asyncJobStreetCloseWebHookFunction": "arn:aws:states:ap-south-1:************:stateMachine:prod-asyncJobStreetCloseWebHookFunction", "recruitBucketName": "recruit.hrapp.co", "atsNameForIndeed": "Flowtrack"}