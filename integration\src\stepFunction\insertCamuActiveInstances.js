//get common functions
const{getConnection,insertInMasterTable}=require("./commonFunctions");
//get tablealias
const{appManagerTables}=require("../../common/tablealias")
// Organization database connection
const knex = require('knex');
//Require moment
const moment = require('moment-timezone');

// Function to get the active subscribed user to call camu exit api.
module.exports.insertCamuActiveInstances  = async(event,context) =>{
    try{
        console.log('Inside insertCamuActiveInsances function');
        // get input data
        let inputStatus=event.status;
        let masterTable=appManagerTables.resignationManager;
        let inputParams;
        let currentDate=moment.utc().format("YYYY-MM-DD");
        if(inputStatus && (inputStatus.toLowerCase()==='open' || inputStatus.toLowerCase()==='failed'))
        {
            /** We limit the number of execution at a particular time so event will be triggered for executing remaining records.
            Incase of input status as 'Open' proceed to the camu exit api call process*/
            console.log('Event triggered to process remaining records so move to step2');
            let response={
                nextStep:'Step2',
                input:{'status':inputStatus},
                message:'Event triggered to process next set of instances.'          
            }
            return response;
        }
        else{
            let databaseConnection=await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
            // check whether data exist or not
            if(Object.keys(databaseConnection).length){
                // form app manager database connection
                let appmanagerDbConnection=knex(databaseConnection.AppManagerDb);
                //get camu active instances
                let camuActiveInsances=await getCamuActiveInsances(appmanagerDbConnection);
                //remove inactive instances from master table
                await removeInstancesFromAppManager(appmanagerDbConnection,masterTable);
                let inputData=[];
                for(let i=0;i<camuActiveInsances.length;i++)
                {
                    inputParams={
                        Org_Code: camuActiveInsances[i]['Org_Code'],
                        Complete_Status: "Open",
                        Employee_Insert_Status:"Open",
                        Data_Processing_Date: currentDate
                    }   
                    inputData.push(inputParams);
                }
                let insertStatus=await insertInMasterTable(appmanagerDbConnection,inputData,masterTable);
                appmanagerDbConnection?appmanagerDbConnection.destroy():null;
                if(insertStatus)
                {
                    let response={
                        nextStep:'Step2',
                        input:{'status':inputStatus},
                        message:'Event triggered to process next set of instances.'          
                    }
                    return response;  
                }
                else
                {
                    let response ={
                        nextStep:'End',
                        input:{'status':inputStatus},
                        message:'Error Occured while Updating the table.'
                    };
                    return response;
                }
              
            }
            else{
                console.log('Error while creating app manager database connection in step1');
                appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
                let response ={
                    nextStep:'End',
                    input:{'status':inputStatus},
                    message:'Error Occured while Updating the table.'
                };
                return response;
            }
        }
    }
    catch(e)
    {
        console.log("Error in insertCamuActiveInsances function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response ={
            nextStep:'End',
            input:{'status':inputStatus},
            message:'Error Occured while Updating the table.'
        };
        return response;
    }
}

//function to get active instances having subscription to camu
async function getCamuActiveInsances(appmanagerDbConnection)
{
    try{
        return(
            appmanagerDbConnection(appManagerTables.orgRateChoice)
            .distinct('HRU.Org_Code')
            .from(appManagerTables.orgRateChoice + ' as ORC')
            .leftJoin(appManagerTables.hrappRegisteruser + ' as HRU','ORC.Org_Code','HRU.Org_Code')
            .where('ORC.Plan_Status','Active')
            .andWhere('HRU.Partner_Integration','camu')
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log("Error in getCamuActiveInsances function .catch block.",e);
                return '';
            })
        )
    }
    catch(e)
    {
        console.log("Error in getCamuActiveInsances function main catch block.",e);
        return '';
    }
}

// function to remove user from master table where  master orgcode not exist in orgCodeList
async function removeInstancesFromAppManager(appmanagerDbConnection,masterTable){
    try{
        console.log('Inside  removeInstancesFromAppManager function.');
        return(
            appmanagerDbConnection(masterTable)
            .del()
            .then(deleteUser =>{
                console.log('Instances deleted from app manager table',deleteUser);
                return true;
            })
            .catch(catchError=>{
                console.log('Error in removeInstancesFromAppManager function .catch block.', catchError);
                return false;
            })
        );
    }
    catch(error){
        console.log('Error in removeInstancesFromAppManager function main catch block.', error);
        return false;
    }
};
