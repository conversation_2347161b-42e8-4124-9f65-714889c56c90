
// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants')

module.exports.updatePositionJobSummary = async (parent, args, context, info) => {


    console.log("Inside updatePositionJobSummary function.")
    let organizationDbConnection;
    let validationError = {};
    try {

        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, '', '', 'UI', false, args.formId);
    
        if(Object.entries(checkRights).length > 0 && checkRights.Role_Update === 1){

            if(!args.positionCode){
                validationError['ERR-001'] = "Position code is required"
            }else if(!args.jobDescription){
                validationError['ERR-002'] = "Job description required"
            } else if( (args.jobDescription.length < 3 || args.jobDescription.length >4000)){
                validationError['ERR-003'] = "Job description could not have less than 3 characters and more than 4000 chars"
            }
            if(Object.keys(validationError).length > 0){
                throw 'IVE0000';
            }
            return (
                await organizationDbConnection.transaction(async function(trx){
                    await organizationDbConnection(ehrTables.sfwpJobDescriptionMaster).transacting(trx)
                    .update({Job_Description: args.jobDescription}).where('Organization_Structure_Id', args.originalPositionId)

                    
                   
                    await organizationDbConnection(ehrTables.SFWPOrganizationStructure).transacting(trx)
                    .update({Approved_Position: args.approvedPosition, Warm_Bodies: args.warmBodies, 
                        Pos_Code: args.positionCode}
                    ).where('Organization_Structure_Id', args.originalPositionId);

                    organizationDbConnection ? organizationDbConnection.destroy() : null;
                    return { errorCode: "", message: "Position job summary updated successfully."};

                })
            );
           
        }else{
            throw '_DB0102'
        }
    }catch(err){
        //Destroy DB connection
        console.error('Error in updatePositionJobSummary function main catch block.', err);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(err, '_UH0001');
        if (err === 'IVE0000') {
            console.error('Validation error in updatePositionJobSummary function - ', validationError)
            throw new UserInputError(errResult.message, { validationError: validationError });
        }else{
            
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
}