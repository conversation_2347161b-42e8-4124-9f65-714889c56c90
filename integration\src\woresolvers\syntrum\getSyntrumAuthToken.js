//Require constants
const { syntrumValues } = require('../../../common/appConstants');
//Require table names
const { ehrTables } = require('../../../common/tablealias');
//Require common function
const { getDecryptedString } = require('../../common/commonFunction');
const { extractResponse } = require('../../common/syntrumCommonFunctions');

//Function to get the syntrum auth token
async function getSyntrumAuthToken(organizationDbConnection){
    try {
        let integrationCredentials = await getAPIIntegrationCredentials(organizationDbConnection);
        let credentialsArray = await decryptSyntrumCredentials(integrationCredentials);
        let apiUrl = integrationCredentials.Api_Url;
        let authTokenResponse = await callSignInEndpoint(apiUrl,credentialsArray);
        if(authTokenResponse.status){
            return {apiUrl,authToken:authTokenResponse.authToken};
        }
        throw authTokenResponse;
    } catch (error) {
        console.log('Error in getSyntrumAuthToken function main catch block',error);
        throw error;
    }
}

//Function to get the integration credentials for the org code
async function getAPIIntegrationCredentials(organizationDbConnection) {
    try {
        const syntrumApiIntegrationCredentials = await organizationDbConnection(ehrTables.externalApiCredentials)
        .select('Integration_Type', 'Credential1', 'Credential2', 'Api_Url')
        .where('Integration_Type', syntrumValues.integrationType)
        .whereNotNull('Credential1')
        .whereNotNull('Credential2')
        .whereNotNull('Api_Url')
        .first();
        if (syntrumApiIntegrationCredentials) {
            return syntrumApiIntegrationCredentials;
        }
        throw 'SYN0107';
    } catch (error) {
        console.error('Error occured in getAPIIntegrationCredentials main catch block ', error);
        throw error=='SYN0107' ? 'SYN0107' : error;
    }
}

//Function to decrypt the credential1 and credential2
async function decryptSyntrumCredentials(integrationCredentials){
    let credentialsArray = [];
    try {
        let credentialsToDecrypt = [integrationCredentials.Credential1,integrationCredentials.Credential2];
        for(let textToDecrypt of credentialsToDecrypt){
            let decryptionInputs ={ 
                isBase64SecretKey: 1,
                isEncryptedInputEncoded: 0,
                textToDecrypt: textToDecrypt ? textToDecrypt : '',
                decryptionAlgorithm: process.env.decryptionAlgorithm,//algorithm used for decryption
                decryptionSecretKeyName: process.env.decryptionSecretKeyName,
                allowEmptySecretKey: 1
            };
            let decryptedText = await getDecryptedString(decryptionInputs);
            
            credentialsArray.push(decryptedText);
        }
        return credentialsArray;
    } catch (error) {
        console.error('Error occured in decryptSyntrumCredentials main catch block ', error);
        throw error;
    }
}

//Function to call the sign in endpoint to get the auth token
async function callSignInEndpoint(apiUrl,credentialsArray) {
    try {
        const axios = require('axios');

        let url = apiUrl+syntrumValues.signInEndpointName;

        //call API to retrieve the details
        const config = {
            method: 'post', 
            url: url,
            maxBodyLength: Infinity,
            headers: {
                'Content-Type': 'application/json'
            },
            data: {
                "json":
                {
                    userName: credentialsArray[0],
                    password: credentialsArray[1]
                }
            },
            timeout: 60000//1minute
        };

        const { data } = await axios.request(config);
        console.log('Inside callSignInEndpoint success response, ', JSON.stringify(data));
        
        // Extract the token from the response data
        let token = extractResponse(data, 'token');
        // If token is not present, return a failure status with a message
        if(!token){
            return { status: false,  errorCode: 'SYN0101', message: "Authentication failed: Token is missing. Please provide a valid token.", response: extractResponse(data, 'json') };
        }
        // Return success status with the token and a success message
        return { status: true, authToken: token, message: 'Authentication successfully completed', response: null };
    } catch (error) {
        console.error('Error occured in callSignInEndpoint main catch block ', error?.response?.data || error?.response || error,apiUrl);
        
        // Destructure error response details, if available
        const { status, statusText, data } = error.response || {};
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
            return { status: false,  errorCode: 'SYN0101', message: `408 - Request Timeout - The server took longer than 1 minute to respond.`, response: data };
        }
        // Return failure status with error details
        return { status: false,  errorCode: 'SYN0101', message: `${status || ' Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: Authentication is failed`, response: extractResponse(data, 'json') };
    }
}

module.exports={
    getSyntrumAuthToken,
    getAPIIntegrationCredentials
}