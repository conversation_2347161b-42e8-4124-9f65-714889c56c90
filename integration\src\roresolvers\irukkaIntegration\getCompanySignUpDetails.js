// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
// require common constant files
const { formName } = require('../../../common/appConstants');


let organizationDbConnection;
module.exports.getCompanySignUpDetails = async (parent, args, context, info) => {
    try {
        console.log("Inside getCompanySignUpDetails function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // check their rights
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, employeeId, formName.recruitment, '', 'UI');
        if (Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const personalInfoQuery = 
            organizationDbConnection(ehrTables.empPersonalInfo + " as EPI")
               .select('EPI.Employee_Id','EPI.DOB', 'EJ.Emp_Email', organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name,EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as Updated_By_Name"), organizationDbConnection.raw("CONCAT_WS(' ', CD.Mobile_No_Country_Code, CD.Mobile_No) as Mobile_Number"))
               .innerJoin(ehrTables.contactDetails + " as CD", "EPI.Employee_Id", "CD.Employee_Id")
               .innerJoin(ehrTables.empJob + " as EJ", 'EPI.Employee_Id', 'EJ.Employee_Id')
               .where('EPI.Employee_Id', employeeId);
           const organisationDetailsQuery = 
               organizationDbConnection(ehrTables.orgDetails)
               .select('Org_Name', 'Org_Description');
            return (
                    Promise.all([personalInfoQuery, organisationDetailsQuery])
                    .then(([result1, result2]) => {
                      // Processing the results and creating a common response
                      const combinedResponse = {
                        result1: result1,
                        result2: result2
                      };
                      organizationDbConnection ? organizationDbConnection.destroy() : null;
                      return { errorCode: "", message: "Company Signup details retrieved successfully.", getPersonalDetails: combinedResponse.result1, getCompanyDetails:combinedResponse.result2 };
                    })
                    .catch((err) => {
                        console.log('Error in getCompanySignUpDetails. .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'SET0107');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            throw '_DB0100';
        }

    }
    catch (e) {
        //Destroy DB connection
        console.log('Error in getCompanySignUpDetails. function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'SET0005');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
