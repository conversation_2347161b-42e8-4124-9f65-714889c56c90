// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
// require common constant files
const {formId} = require('../../../common/appConstants');
const { validatCommonRuleInput, updateCandidateDetails, updateCandidateInterviewStatus } = require('../../common/commonFunction');
const { ehrTables } = require('../../../common/tablealias');


module.exports.archiveCandidateDetails = async (parent, args, context, info) => {
    console.log('Inside archiveCandidateDetails function');
    let organizationDbConnection, validationError = {};
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // Validate inputs
        let fieldValidations={}
        if (!args.archiveReasonId) {
            throw 'IVE0000';
        }
        
        if (args.archiveComment) {
            fieldValidations.archiveComment = 'IVE0499';
        }

        validationError = await validatCommonRuleInput(args, fieldValidations);
        if (Object.keys(validationError).length > 0) {
            throw 'IVE0000';
        }

        const accessFormId = args.action.toLowerCase() === 'candidate' ?  formId.jobCandidate : 
        args.action.toLowerCase() === 'talentpool' ? formId.talentPoolCandidate : null;

        const loginEmployeeId = context.Employee_Id;
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection, loginEmployeeId, null, '', 'UI', false, accessFormId);

        if (!checkRights || checkRights.Role_Update !== 1) {
            throw '_DB0102';
        }

        const [candidate, archivedReason] = await Promise.all([ 
            organizationDbConnection(ehrTables.candidatePersonalInfo + ' as CPI')
            .select('CPI.Candidate_Id', 'CPI.Talent_Pool_Id', 'CRI.Archived', 'ST.Status', 'TP.Talent_Pool')
            .innerJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
            .leftJoin(ehrTables.atsStatusTable + ' as ST', 'ST.Id', 'CRI.Candidate_Status')
            .leftJoin(ehrTables.talentPool + ' as TP', 'TP.Talent_Pool_Id', 'CPI.Talent_Pool_Id')
            .where('CPI.Candidate_Id', args.candidateId).first(),

            organizationDbConnection('archive_reasons').where('Reason_Id', args.archiveReasonId).first(),
            
        ]);

        if (!candidate || candidate?.Archived?.toLowerCase() === 'yes' || candidate?.Status?.toLowerCase() === "onboarded") {
            throw !candidate ? 'EI00224' :  //Sorry! An error occurred while processing the candidate details. Please try again.
            candidate?.Archived?.toLowerCase() === 'yes' ?  "TAP0114" :  //Apologies! The candidate details have already been archived in a different user session.
            candidate?.Status?.toLowerCase() === "onboarded" ? "TAP0115"  //Apologies! The candidate is currently in an onboarded status, so archiving the data is not permitted.
            : "EI00212" //The candidate archive has already been transferred and cannot be modified.
        }
        if (args.action && (args.action.toLowerCase() === 'candidate' && candidate.Talent_Pool_Id) ||  (args.action.toLowerCase() === 'talentpool' && (!candidate.Talent_Pool_Id || (args.talentPoolId && candidate.Talent_Pool_Id !== args.talentPoolId)))) {
            throw 'TAP0004'; //An error occurred while attempting to archive candidate details. Please contact the platform administrator for assistance.
        } 

        args.microsoft_access_token = context.microsoft_access_token;

        // Transaction block
        return await organizationDbConnection.transaction(async (trx) => {
            await Promise.all([
                updateCandidateDetails(organizationDbConnection, args, null, loginEmployeeId, trx),
                updateCandidateInterviewStatus(organizationDbConnection, args, trx),
            ]);

            return true;
        }).then(async () => {
            // Log system activity
            await commonLib.func.createSystemLogActivities({
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                changedData: args,
                organizationDbConnection: organizationDbConnection,
                formId: accessFormId,
                action: `Moved ${candidate?.Talent_Pool ? 'Pool' : 'Archive'}`,
                isEmployeeTimeZone: 0,
                uniqueId: args.candidateId,
                message: `The candidate has been moved from ${candidate?.Talent_Pool ? candidate.Talent_Pool+' talent pool' : 'candidate' } to archive with the reason ${archivedReason.Reason}. and all upcoming scheduled interviews have been cancelled.`
            });
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Candidate details have been archived successfully." };
        });
    } catch (error) {
        console.log('Error in archiveCandidateDetails function main catch block:', error);

        // Destroy DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        // Handle different error cases
        if (error === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError });
        } else {
            const errResult = commonLib.func.getError(error, 'TAP0004');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};

