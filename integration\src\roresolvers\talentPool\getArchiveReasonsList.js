// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');


module.exports.getArchiveReasonsList = async (parent, args, context, info) => {
    console.log('Inside getArchiveReasonsList function');

    // Store the database connection to the organization database in a variable
    let organizationDbConnection;

    try {
        // Establish a database connection to the organization database
        organizationDbConnection = knex(context.connection.OrganizationDb);
        // Get the employee ID of the user who is accessing this function
        const loginEmployeeId = context.Employee_Id;

        // Check if the user has view access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, args.formId);
        // If the user has view access rights, retrieve the list of archive reasons
        if (checkRights && Object.keys(checkRights).length > 0 && checkRights.Role_View === 1) {
            const archiveReasonList = await organizationDbConnection(ehrTables.archiveReason + ' as AR')
            .select('AR.Reason_Id', 'AR.Reason', 'AR.Stage')
            .modify(queryBuilder => {
                if (args.stageId) {
                    queryBuilder.innerJoin(ehrTables.archiveReasonsMappingStage + ' as ARMS', 'AR.Reason_Id', 'ARMS.Reason_Id') 
                    .where('ARMS.Stage_Id', args.stageId);
                }
            });
            // Return the list of archive reasons
            return {
                errorCode: '',
                message: 'Archive reasons list has been retrieved successfully.',
                archiveReasonList: archiveReasonList
            };

        } else {
            // If the user doesn't have view access rights, throw an error
            console.log('Employee does not have view access rights');
            throw '_DB0100';
        }
    } catch (error) {
        // Catch any errors that occur in the try block
        console.log('Error in getArchiveReasonsList function main catch block.', error);
        // Convert the error to a message and code
        const errResult = commonLib.func.getError(error, 'TAP0001');
        // Throw an ApolloError with the message and code
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        // Destroy the database connection to the organization database
        organizationDbConnection ? organizationDbConnection.destroy() : null;
    }
};

