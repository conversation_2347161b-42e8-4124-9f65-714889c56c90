// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError, ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../../common/tablealias');
const { formId } = require('../../../common/appConstants');
const { validatCommonRuleInput, updateCandidateInterviewStatus, updateCandidateDetails, validateDuplicateCandidate } = require('../../common/commonFunction');
const moment = require('moment');
const axios = require('axios');

module.exports.addCandidateToTalentPool = async (parent, args, context, info) => {
    console.log('Inside addCandidateToTalentPool function');
    let validationError = {};
    let organizationDbConnection;
    try {
        organizationDbConnection = knex(context.connection.OrganizationDb);

        let fieldValidations = {}
        // Validate inputs
        if (args.archiveComment) {
            fieldValidations.archiveComment = 'IVE0499'
        }
        if (!args.archiveReasonId) {
            validationError['IVE0000'] = 'Archive reason is required';
            throw 'IVE0000';
        }
        validationError = await validatCommonRuleInput(args, fieldValidations)
        if (Object.keys(validationError).length > 0) throw 'IVE0000';

        // Check access rights
        const loginEmployeeId = context.Employee_Id;
        let checkRights = await commonLib.func.checkEmployeeAccessRights(organizationDbConnection, loginEmployeeId, null, '', 'UI', false, formId.jobCandidate);

        if (!(checkRights && checkRights.Role_Add === 1)) throw '_DB0101';

        let [talentPool, archiveReason] = await Promise.all([
            organizationDbConnection(ehrTables.talentPool).select('Talent_Pool')
            .where('Talent_Pool_Id', args.talentPoolId).first(),

            organizationDbConnection('archive_reasons').where('Reason_Id', args.archiveReasonId).first(),
        ]);


        // Transaction block
        return await organizationDbConnection.transaction(async (trx) => {

          
            if (!talentPool) {
                throw 'TAP0112'; //Unable to transfer details to the specified talent pool as it has been deleted.
            }

            args.microsoft_access_token = context.microsoft_access_token;

            let candidatePersonalData = await getCurrentCandidateDetails(organizationDbConnection, args, trx, context);
            if (!candidatePersonalData || !Object.keys(candidatePersonalData).length ||
                candidatePersonalData?.Archived?.toLowerCase() === 'yes' ||
                candidatePersonalData?.Status?.toLowerCase() === "onboarded") {

                throw candidatePersonalData?.Archived?.toLowerCase() === 'yes' ? "TAP0114" :   //Apologies! The candidate details have already been archived in a different user session.
                    candidatePersonalData?.Status?.toLowerCase() === "onboarded" ? "TAP0115" : //Apologies! The candidate is currently in an onboarded status, so archiving the data is not permitted.
                        "TAP0108"; //Sorry, an error occurred while processing the request to add candidate to talent pool.
            }

            candidatePersonalData.firstName = candidatePersonalData.First_Name
            candidatePersonalData.middleName = candidatePersonalData.Middle_Name
            candidatePersonalData.lastName = candidatePersonalData.Last_Name
            candidatePersonalData.emailId = candidatePersonalData.Personal_Email
            candidatePersonalData.dob = candidatePersonalData.DOB
            candidatePersonalData.mobileNo = candidatePersonalData.Mobile_No

            let [statusDetail, organizationPortalAccess, duplicateResult] = await Promise.all([
                organizationDbConnection(ehrTables.atsStatusTable).transacting(trx).where('Status', 'Applied').first(),
                organizationDbConnection('recruitment_settings').transacting(trx).select('Candidate_Portal_Login_Access').first(),
                validateDuplicateCandidate(organizationDbConnection, candidatePersonalData, loginEmployeeId, trx)
            ])
            organizationPortalAccess = organizationPortalAccess?.Candidate_Portal_Login_Access === 1 ? true : false;
            

            // Insert into candidatePersonalInfo table
            const [lastInsertedId] = await organizationDbConnection(ehrTables.candidatePersonalInfo)
                .insert({
                    Source_Type: candidatePersonalData.Source_Type,
                    Photo_Path: candidatePersonalData.Photo_Path,
                    Emp_First_Name: candidatePersonalData.First_Name,
                    Emp_Last_Name: candidatePersonalData.Last_Name,
                    Emp_Middle_Name: candidatePersonalData.Middle_Name,
                    Emp_Pref_First_Name: candidatePersonalData.Emp_Pref_First_Name,
                    Gender_Identity_Id: candidatePersonalData.Gender_Identity_Id,
                    Gender_Expression_Id: candidatePersonalData.Gender_Expression_Id,
                    Salutation: candidatePersonalData.Salutation,
                    Personal_Email: candidatePersonalData.Personal_Email,
                    Suffix: candidatePersonalData.Suffix,
                    DOB: candidatePersonalData.DOB,
                    Blood_Group: candidatePersonalData.Blood_Group,
                    Marital_Status: candidatePersonalData.Marital_Status,
                    Nationality: candidatePersonalData.Nationality,
                    Nationality_Id: candidatePersonalData.Nationality_Id,
                    Gender_Orientations: candidatePersonalData.Gender_Orientations,
                    Pronoun: candidatePersonalData.Pronoun,
                    Statutory_Insurance_Number: candidatePersonalData.Statutory_Insurance_Number,
                    PRAN_No: candidatePersonalData.PRAN_No,
                    Added_On: candidatePersonalData.Added_On,
                    Gender_Id: candidatePersonalData.Gender_Id,
                    Gender: candidatePersonalData.Gender,
                    Is_Duplicate: 1,
                    Duplicate_Count: duplicateResult.candidateDuplicateCount ? duplicateResult.candidateDuplicateCount + 1 : 1,
                    Talent_Pool_Id: args.talentPoolId,
                    Candidate_Status:  statusDetail ? statusDetail.Status : 'Applied',
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: loginEmployeeId,
                    Data_Privacy_Statement: candidatePersonalData.Data_Privacy_Statement ? candidatePersonalData.Data_Privacy_Statement : 0
                })
                .transacting(trx);

            // Prepare candidate languages known
            const candidateLangKnown = candidatePersonalData.Lang_Known.map(field => ({
                Candidate_Id: lastInsertedId,
                Lang_Known: field.Lang_Known,
                Lang_Spoken: field.Lang_Spoken,
                Lang_Read_Write: field.Lang_Read_Write,
                Lang_Proficiency: field.Lang_Proficiency
            }));

            //Retrieve Custom Field Input Values
            let customFieldInputValues = await organizationDbConnection(ehrTables.candidateCustomFieldValues)
                .select('Custom_Field_Value')
                .where('Primary_Id', args.candidateId)
                .first();

            if (customFieldInputValues) {
                customFieldInputValues = {
                    Primary_Id: lastInsertedId,
                    Form_Id: formId.jobCandidate,
                    Field_Value: customFieldInputValues.Custom_Field_Value,
                }
            }


            // Insert candidate's known languages and contact details
            const queries = [
                organizationDbConnection('candidate_contact_details')
                    .insert({
                        Candidate_Id: lastInsertedId,
                        Mobile_No: candidatePersonalData.Mobile_No,
                        Mobile_No_Country_Code: candidatePersonalData.Mobile_No_Country_Code,
                        pApartment_Name: candidatePersonalData.pApartment_Name,
                        pStreet_Name: candidatePersonalData.pStreet_Name,
                        pCity: candidatePersonalData.pCity,
                        pState: candidatePersonalData.pState,
                        pCountry: candidatePersonalData.pCountry,
                        pPincode: candidatePersonalData.pPincode,
                        pBarangay: candidatePersonalData.pBarangay,
                        pRegion: candidatePersonalData.pRegion,
                        Fax_No: candidatePersonalData.Fax_No ? candidatePersonalData.Fax_No : null,
                        Emergency_Contact_Name: candidatePersonalData.Emergency_Contact_Name,
                        Emergency_Contact_Relation: candidatePersonalData.Emergency_Contact_Name,
                    })
                    .transacting(trx),
                insertCandidatePreferredLocation(organizationDbConnection, candidatePersonalData, lastInsertedId, trx, context),
                addCandidateWorkPermitDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx),
                addCandidateReferenceDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx),
                addCandidateCareerInfo(organizationDbConnection, candidatePersonalData, lastInsertedId, trx, loginEmployeeId, duplicateResult, organizationPortalAccess, statusDetail),
                insertEducationDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx),
                addCandidateCertificationDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx, context),
                addCandidateDependentDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx, context),
                addCandidateSkillDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx),
                insertCandidateExperienceDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx, context),
                addCandidatePassportDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx),
                updateCandidateInterviewStatus(organizationDbConnection, args, trx), // Update the current candidate interview status
                updateCandidateDetails(organizationDbConnection, args, duplicateResult.candidateDuplicateCount, loginEmployeeId, trx), // Update the current candidate details
                addUpdateCustomFieldValues(customFieldInputValues, context),
                addCandidateDataPrivacyDetails(organizationDbConnection, candidatePersonalData, lastInsertedId, trx, context),
            ];

            // Conditionally add candidate language insertion if candidateLangKnown is not empty
            if (candidateLangKnown && candidateLangKnown.length > 0) {
                queries.push(
                    organizationDbConnection('candidate_language')
                        .insert(candidateLangKnown)
                        .transacting(trx)
                );
            }

            await Promise.all(queries);

            let systemLogs = {
                userIp: context.User_Ip,
                employeeId: loginEmployeeId,
                changedData: args,
                organizationDbConnection: trx,
                formId:  formId.jobCandidate,
                action: 'Moved Pool',
                isEmployeeTimeZone: 0,
                uniqueId: args.candidateId,
                message: `The canidate has been moved to ${talentPool.Talent_Pool} talent pool with the reason ${archiveReason.Reason}. and all upcoming scheduled interviews have been cancelled.`
            }

            await commonLib.func.createSystemLogActivities(systemLogs);
            systemLogs.uniqueId = lastInsertedId;
            systemLogs.message = `The candidate added to the Source stage with ${statusDetail.Status} status via ${talentPool.Talent_Pool} talent pool.`
            await commonLib.func.createSystemLogActivities(systemLogs);



        }).then(async () => {
            organizationDbConnection ? organizationDbConnection.destroy() : null;
            return { errorCode: "", message: "Candidate has been added to talent pool successfully." };
        });

    } catch (error) {
        console.log('Error in addCandidateToTalentPool function main catch block:', error);

        // Clean up DB connection
        organizationDbConnection ? organizationDbConnection.destroy() : null;

        // Handle different error cases
        if (error === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError });
        } else {
            const errResult = commonLib.func.getError(error, 'TAP0006');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
};

async function getCurrentCandidateDetails(organizationDbConnection, args, trx, context) {
    console.log("Inside getCurrentCandidateDetails function");
    const candidateId = args.candidateId;

    try {
        // Fetch basic candidate details

        let [candidate, preferredLocationResult, langKnownResult, dependents, candidateEducation,
            candidateExperience, candidateSkills, candidateCertifications, workPermitsResult, candidatePassport] =
            await Promise.all([

                organizationDbConnection(ehrTables.candidatePersonalInfo + " as CPI")
                    .select(
                        'CPI.Candidate_Id',
                        'CPI.Source_Type',
                        'CPI.Photo_Path',
                        'CPI.Emp_First_Name as First_Name',
                        'CPI.Emp_Last_Name as Last_Name',
                        'CPI.Emp_Middle_Name as Middle_Name',
                        'CPI.Emp_Pref_First_Name',
                        'CPI.Gender_Identity_Id',
                        'CPI.Gender_Expression_Id',
                        'CPI.Salutation',
                        'CPI.Suffix',
                        'CPI.Personal_Email',
                        'CPI.DOB',
                        'CPI.Marital_Status',
                        'CPI.Blood_Group',
                        'CPI.Nationality',
                        'CPI.Nationality_Id',
                        'CPI.Gender_Orientations',
                        'CPI.Pronoun',
                        'CPI.Statutory_Insurance_Number',
                        'CPI.PRAN_No',
                        'CR.*', 'CRI.*',
                        'CCD.Mobile_No', 'CCD.Mobile_No_Country_Code', 'CCD.pApartment_Name', 'CCD.pStreet_Name', 'CCD.pCity', 'CCD.pState', 'CCD.pCountry', 'CCD.pPincode',
                        'CPI.Added_On',
                        'CPI.Gender_Id',
                        'CPI.Gender',
                        'CCD.pBarangay',
                        'CCD.pRegion',
                        'CCD.Fax_No',
                        'CCD.Emergency_Contact_Name',
                        'CCD.Emergency_Contact_Relation',
                        'CPI.Is_Duplicate',
                        'CPI.Duplicate_Count', 'ST.Status', 'CPI.Data_Privacy_Statement'
                    )
                    .leftJoin(ehrTables.candidateContactDetails + ' as CCD', 'CCD.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.candidateRecruitmentInfo + ' as CRI', 'CRI.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.candidateReference + ' as CR', 'CR.Candidate_Id', 'CPI.Candidate_Id')
                    .leftJoin(ehrTables.atsStatusTable + ' as ST', 'ST.Id', 'CRI.Candidate_Status')

                    .where('CPI.Candidate_Id', candidateId)
                    .transacting(trx),

                organizationDbConnection('candidate_prefered_job_location').transacting(trx)
                    .select('*').where('candidate_prefered_job_location.Candidate_Id', candidateId),

                organizationDbConnection('candidate_language').transacting(trx).select('*')
                    .where('candidate_language.Candidate_Id', candidateId),

                organizationDbConnection('candidate_dependent').transacting(trx)
                    .select('*').where('candidate_dependent.Candidate_Id', candidateId),

                organizationDbConnection('candidate_education').transacting(trx)
                    .select('*').where('Candidate_Id', candidateId),

                organizationDbConnection('candidate_experience')
                    .select([
                        'candidate_experience.*',
                        organizationDbConnection.raw(`
                            JSON_ARRAYAGG(
                                JSON_OBJECT(
                                    'Reference_Email', candidate_experience_reference.Reference_Email,
                                    'Reference_Name', candidate_experience_reference.Reference_Name,
                                    'Reference_Number', candidate_experience_reference.Reference_Number
                                )
                            ) AS referenceDetails
                        `)
                    ])
                    .where('candidate_experience.Candidate_Id', candidateId)
                    .leftJoin('candidate_experience_reference', 'candidate_experience_reference.Experience_Id', 'candidate_experience.Experience_Id')
                    .groupBy('candidate_experience.Experience_Id')
                    .transacting(trx),

                organizationDbConnection('candidate_skills').select('*').where('Candidate_Id', candidateId)
                    .transacting(trx),

                organizationDbConnection('candidate_certifications').select('*').where('Candidate_Id', candidateId)
                    .transacting(trx),

                organizationDbConnection('candidate_work_permit').transacting(trx)
                    .select('*').where('Candidate_Id', candidateId),

                organizationDbConnection('candidate_passport').transacting(trx)
                    .select('*').where('Candidate_Id', candidateId)

            ]);


        if (!candidate || candidate.length === 0) return [];

        const candidatesDetails = { ...candidate[0] };

        candidatesDetails['Preferred_Location'] = preferredLocationResult;
        candidatesDetails['Lang_Known'] = langKnownResult;
        candidatesDetails['Candidate_Education'] = candidateEducation;
        candidatesDetails['Candidate_Experience'] = candidateExperience
        candidatesDetails['Candidate_Skills'] = candidateSkills;
        candidatesDetails['Candidate_Dependent'] = dependents;
        candidatesDetails['Work_Permit'] = workPermitsResult;
        candidatesDetails['Candidate_Passport'] = candidatePassport;


        let candidateCertificationData = candidateCertifications.map(cert => cert.Certification_Id).filter(Boolean);

        candidateCertificationData = candidateCertificationData.length ?
            await organizationDbConnection('candidate_certifications_documents')
                .select('*').whereIn('Certification_Id', candidateCertificationData)
                .transacting(trx) : [];

        // Create a lookup map for faster document access by Certification_Id
        const certificationDocsMap = candidateCertificationData.reduce((acc, doc) => {
            acc[doc.Certification_Id] = doc;
            return acc;
        }, {});

        const candidateCertificationsDetails = candidateCertifications.map(cert => {
            const doc = certificationDocsMap[cert.Certification_Id] || {};
            return {
                Certification_Id: cert.Certification_Id,
                Certification_Name: cert.Certification_Name,
                Received_Date: cert.Received_Date,
                Certificate_Received_From: cert.Certificate_Received_From,
                Certificate_File_Name: doc.File_Name || null,
                Ranking: cert?.Ranking,
                File_Hash: doc.File_Hash || null,
                Sub_Type_Id: doc.Sub_Type_Id || null,
                Document_Name: doc.Document_Name || null
            };
        });

        candidatesDetails['Candidate_Certifications'] = candidateCertificationsDetails;

        return candidatesDetails;
    } catch (error) {
        console.error('Error in getCurrentCandidateDetails main catch() block ', error)
        throw error;
    }
}

async function insertCandidatePreferredLocation(organizationDbConnection, args, lastInsertedId, trx, root) {
    try {
        if (args.Preferred_Location && args.Preferred_Location.length > 0) {
            /**candidate's preferred location */
            const candidatePreferredLocation = args.Preferred_Location.map(({ Candidate_Id, ...field }) => ({
                Candidate_Id: lastInsertedId,
                ...field
            }));
            /**insert candidate's preferred location */
            return organizationDbConnection('candidate_prefered_job_location')
                .insert(candidatePreferredLocation)
                .transacting(trx)
                .then(async data => {
                    return true;
                })
                .catch(function (err) {
                    throw err;
                })
        }
        else {
            return true;
        }
    } catch (err) {
        console.log('Error in insertCandidatePreferredLocation main catch() block', err);
        throw err;
    }

}
async function addCandidateWorkPermitDetails(organizationDbConnection, args, lastInsertedId, trx) {
    try {
        // Check if workAuthorization exists
        if (args.Work_Permit && args.Work_Permit.length > 0) {

            let workAuthorization = args.Work_Permit.map(({ Candidate_Id, ...field }) => ({
                Candidate_Id: lastInsertedId,
                ...field
            }));

            /**insert candidate's work permit details */
            return organizationDbConnection('candidate_work_permit')
                .insert(workAuthorization).transacting(trx)
                .then(data => {
                    return true;
                })
                .catch(function (err) {
                    throw err;
                })
        }
        else {
            return true;
        }
    } catch (err) {
        console.log('Error in addCandidateWorkPermitDetails main catch() block', err);
        throw err;
    }

}
async function addCandidateReferenceDetails(organizationDbConnection, args, lastInsertedId, trx) {
    try {
        if (args.Verifier_Name || args.Verifier_Phone_Number || args.Verifier_Email_Id) {
            /**insert candidate's reference details */
            return organizationDbConnection('candidate_reference')
                .insert({
                    Candidate_Id: lastInsertedId,
                    Verifier_Name: args.Verifier_Name,
                    Verifier_Phone_Number: args.Verifier_Phone_Number,
                    Verifier_Email_Id: args.Verifier_Email_Id
                })
                .transacting(trx)
                .then(data => {
                    return true;
                })
                .catch(function (err) {
                    throw err;
                })
        } else {
            return true;
        }
    } catch (err) {
        console.log('Error in addCandidateReferenceDetails main catch() block', err);
        throw err;
    }

}
async function addCandidateCareerInfo(organizationDbConnection, args, lastInsertedId, trx, loginEmployeeId, duplicateResult, organizationPortalAccess, statusDetail) {
    try {
        /**insert candidate's career details */
        return organizationDbConnection(
            'candidate_recruitment_info'
        )
            .insert({
                Candidate_Id: lastInsertedId,
                Job_Post_Id: args.Job_Post_Id || null,
                Current_Employer: args.Current_Employer,
                Notice_Period: args.Notice_Period,
                Current_CTC: args.Current_CTC,
                Expected_CTC: args.Expected_CTC,
                Resume: args.Resume,
                Currency: args.Currency,
                Resume_File_Size: args.Resume_File_Size,
                National_Identification_Number: args.National_Identification_Number,
                Candidate_Status: statusDetail ? statusDetail.Id : 10,
                Total_Experience_In_Years: args.Total_Experience_In_Years,
                Total_Experience_In_Months: args.Total_Experience_In_Months,
                Source: args.Source,
                Skill_Set: args.Skill_Set,
                Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                Added_By: loginEmployeeId,
                Portal_Access_Enabeld: organizationPortalAccess && args?.Portal_Access_Enabeld?.toLowerCase() === 'yes' ? 'Yes' : 'No',
                Blacklisted: duplicateResult?.candidateBlackedList ? 'Yes' : 'No',
                Blacklisted_On: duplicateResult?.candidateBlackedList ? moment().utc().format('YYYY-MM-DD HH:mm:ss') : null,
                Blacklisted_By: duplicateResult?.candidateBlackedList ? loginEmployeeId : null,
                Blacklisted_Reason_Id: duplicateResult?.candidateBlackedList?.Blacklisted_Reason_Id || null,
                Blacklisted_Comments: duplicateResult?.candidateBlackedList?.Blacklisted_Comments || null,
                Blacklisted_Attachment_File_Name: duplicateResult?.candidateBlackedList?.Blacklisted_Attachment_File_Name || null
            })
            .transacting(trx)
            .then(data => {
                return true;
            })
            .catch(function (err) {
                console.log('Error in addCandidateCareerInfo  catch() block', err);
                throw err;
            })
    } catch (err) {
        console.log('Error in addCandidateCareerInfo main catch() block', err);
        throw err;
    }

}
async function insertEducationDetails(organizationDbConnection, args, lastInsertedId, trx) {
    try {
        let candidateEducation = [];
        if (args.Candidate_Education && args.Candidate_Education.length > 0) {
            candidateEducation = args.Candidate_Education.map(({ Education_Id, Candidate_Id, ...field }) => ({
                Candidate_Id: lastInsertedId,
                ...field
            }));
            /**insert candidate's education details */
            return organizationDbConnection('candidate_education')
                .insert(candidateEducation)
                .transacting(trx)
                .then(async data => {
                    return true;
                })
                .catch(function (err) {
                    throw err;
                })
        }
        else {
            return true;
        }
    } catch (err) {
        console.log('Error in insertEducationDetails main catch() block', err);
        throw err;
    }

}
async function addCandidateCertificationDetails(organizationDbConnection, args, lastInsertedId, trx) {
    try {
        if (
            args.Candidate_Certifications &&
            args.Candidate_Certifications
                .length > 0
        ) {
            /**candidate's certification details */
            const candidateCertification = args.Candidate_Certifications.map(
                field => ({
                    Candidate_Id: lastInsertedId,
                    Certification_Name: field.Certification_Name,
                    Ranking: field.Ranking,
                    Received_Date: field.Received_Date,
                    Certificate_Received_From: field.Certificate_Received_From
                })
            );

            /**insert candidate's certificate details */
            return organizationDbConnection(
                'candidate_certifications'
            )
                .insert(candidateCertification)
                .transacting(trx)
                .then(async data => {
                    if (args.Candidate_Certifications && args.Candidate_Certifications.length > 0) {
                        var lastCertificationId = data[0]; /**inserted certificate ID */
                        /**candidate certificate documents */
                        var candidateCertificationName = args.Candidate_Certifications.map(
                            field => ({
                                File_Name: field.Certificate_File_Name,
                                Sub_Type_Id: field.Sub_Type_Id,
                                Document_Name: field.Document_Name
                            })
                        );
                        var records = [];
                        /**form the data that needs to be inserted in the candidate certificates */
                        for (var i in candidateCertificationName) {
                            var record = candidateCertificationName[i];
                            if (record.File_Name && record.Sub_Type_Id && record.Document_Name) {
                                record['Certification_Id'] = lastCertificationId;
                                lastCertificationId = lastCertificationId + 1;
                                records.push(record);
                            }
                        }

                        if (records && records.length > 0) {
                            /**insert candidate's certificate documents details */
                            return (
                                organizationDbConnection(
                                    'candidate_certifications_documents'
                                )
                                    .insert(records)
                                    .transacting(
                                        trx
                                    )
                                    /**return success response */
                                    .then(async (data) => {
                                        return true;
                                    })
                            );
                        } else {
                            return true;
                        }
                    }
                    else {
                        return true;
                    }
                });
        }
        else {
            return true;
        }
    } catch (err) {
        console.log('Error in addCandidateCertificationDetails main catch() block', err);
        throw err;
    }

}
async function addCandidateDependentDetails(organizationDbConnection, args, lastInsertedId, trx, root) {
    try {
        if (args.Candidate_Dependent && args.Candidate_Dependent.length > 0) {
            /**candidate's dependent details */

            const candidateDependents = args.Candidate_Dependent.map(({ Dependent_Id, Candidate_Id, ...field }) => ({
                Candidate_Id: lastInsertedId,
                ...field
            }));
            /**insert candidate's dependent details */
            return organizationDbConnection('candidate_dependent')
                .insert(candidateDependents)
                .transacting(trx)
                .then(async data => {
                    return true;
                })
                .catch(function (err) {
                    throw err;
                })
        }
        else {
            return true;
        }
    } catch (err) {
        console.log('Error in addCandidateDependentDetails main catch() block', err);
        throw err;
    }

}
async function insertCandidateExperienceDetails(organizationDbConnection, args, lastInsertedId, trx, root) {
    try {
        var candidateExperience = args.Candidate_Experience;
        let candidateExperienceDetails = [];
        let candidateReferenceDetails = [];
        if (candidateExperience && candidateExperience.length > 0) {

            candidateExperienceDetails = candidateExperience.map(field => ({
                Candidate_Id: lastInsertedId,
                Designation: field.Designation,
                Duration: field.Duration,
                End_Date: field.End_Date,
                Months: field.Months,
                Prev_Company_Name: field.Prev_Company_Name,
                Prev_Company_Location: field.Prev_Company_Location,
                Start_Date: field.Start_Date,
                Years: field.Years
            }));

            /**insert experience details */
            let candidateExperienceId = await organizationDbConnection('candidate_experience')
                .insert(candidateExperienceDetails)
                .transacting(trx)

            if (candidateExperienceId && candidateExperienceId.length > 0) {

                candidateExperienceId = candidateExperienceId[0];

                let experienceIds = candidateExperienceDetails.map((_, index) => candidateExperienceId + index);

                candidateExperience.forEach((field, index) => {
                    if (field.referenceDetails && field.referenceDetails.length > 0) {
                        field.referenceDetails = JSON.parse(field.referenceDetails);
                        field.referenceDetails.forEach(ref => {
                            if (ref.Reference_Name && ref.Reference_Email && ref.Reference_Number) {
                                candidateReferenceDetails.push({
                                    Experience_Id: experienceIds[index],
                                    Reference_Name: ref.Reference_Name,
                                    Reference_Email: ref.Reference_Email,
                                    Reference_Number: ref.Reference_Number
                                });
                            }
                        });
                    }
                });

                // Insert reference details if there are any
                if (candidateReferenceDetails?.length > 0) {

                    //Check for duplication
                    const duplicates = candidateReferenceDetails.filter((item, index, self) =>
                        self.findIndex(obj => obj.Reference_Email === item.Reference_Email && obj.Reference_Number === item.Reference_Number) !== index
                    );

                    if (duplicates?.length) {
                        throw 'ESS0156'
                    }

                    await organizationDbConnection('candidate_experience_reference')
                        .insert(candidateReferenceDetails)
                        .transacting(trx);
                }
            }
        }
        return true
    } catch (err) {
        console.log('Error in insertCandidateExperienceDetails main catch() block', err);
        throw err;
    }

}
async function addCandidatePassportDetails(organizationDbConnection, args, lastInsertedId, trx) {
    try {
        if (args.Candidate_Passport && args.Candidate_Passport.length > 0) {

            /**candidate skill details */
            let candidatePassport = args.Candidate_Passport.map(({ Candidate_Id, ...field }) => ({
                Candidate_Id: lastInsertedId,
                ...field
            }));
            /**insert candidate's skill details */

            /**insert candidate's passport details */
            return organizationDbConnection('candidate_passport')
                .insert(candidatePassport)
                .transacting(trx)
                .then(data => {
                    return true;
                })
                .catch(function (err) {
                    throw err;
                })
        } else {
            return true;
        }
    } catch (err) {
        console.log('Error in addCandidatePassportDetails main catch() block', err);
        throw err;
    }

}
async function addCandidateSkillDetails(organizationDbConnection, args, lastInsertedId, trx) {
    try {

        if (args.Candidate_Skills && args.Candidate_Skills.length > 0) {
            /**candidate skill details */
            let candidateSkills = args.Candidate_Skills.map(({ Skill_Id, Candidate_Id, ...field }) => ({
                Candidate_Id: lastInsertedId,
                ...field
            }));
            /**insert candidate's skill details */
            return (
                organizationDbConnection('candidate_skills')
                    .insert(candidateSkills)
                    .transacting(trx)
                    .then(async data => {
                        return true;
                    })
                    .catch(function (err) {
                        throw err;
                    })
            )
        }
        else {
            return true;
        }
    } catch (err) {
        console.log('Error in addCandidateSkillDetails main catch() block', err);
        throw err;
    }
}

/**
 * This function is used to add or update the custom field values in the system. 
 * It will take the primary id, form id and the field value as the input and make a call to the external API to add or update the record.
 * @param {Object} customFieldInputs - The object containing the primary id, form id and the field value
 * @param {Object} context - The context object containing the org_code
 * @returns {Boolean} - Returns true if the custom field values are added or updated successfully
 * @throws {String} - Error code if the custom field values are not added or updated successfully
 */
async function addUpdateCustomFieldValues(customFieldInputs, context) {
    try {
        if (customFieldInputs && customFieldInputs.Primary_Id && customFieldInputs.Form_Id && customFieldInputs.Field_Value?.length > 0) {

            let requestBody = {
                variables: {
                    "Primary_Id": customFieldInputs.Primary_Id,
                    "Form_Id": customFieldInputs.Form_Id,
                    "Field_Value": customFieldInputs.Field_Value
                },
                query: `
                    mutation addUpdateCustomFieldValues(
                        $Primary_Id: Int!
                        $Form_Id: Int!
                        $Field_Value: String!
                    ) {
                        addUpdateCustomFieldValues(
                            Primary_Id: $Primary_Id,
                            Form_Id: $Form_Id,
                            Field_Value: $Field_Value
                        ) {
                            errorCode
                            message
                        }
                    }
                `
            };

            const apiHeaders = {
                org_code: context.Org_Code,
            };

            let response = await axios.request({
                method: 'POST',
                url: 'https://' + process.env.customDomainName + '/coreHr/external',
                headers: apiHeaders,
                data: requestBody
            });
            if (response?.data?.data?.addUpdateCustomFieldValues?.errorCode?.length > 0) {
                throw response?.data?.data?.addUpdateCustomFieldValues?.errorCode;
            }
        }
        return true

    } catch (error) {
        console.log('Error in addUpdateCustomFieldValues', error);
        throw error;
    }
}
/**
 * Inserts a record into the data privacy statement responses table for a candidate.
 * 
 * @param {Object} organizationDbConnection - Database connection object for executing queries.
 * @param {Object} args - Object containing details for the data privacy statement.
 * @param {number} lastInsertedId - Identifier of the last inserted candidate record.
 * @param {Object} trx - Transaction object for maintaining atomicity.
 * @param {Object} root - Object containing additional information, such as the user's IP.
 * 
 * @returns {Promise<boolean>} - Returns true if the operation is successful.
 * 
 * @throws Will throw an error if the database operation fails.
 */

async function addCandidateDataPrivacyDetails(organizationDbConnection, args, lastInsertedId, trx, root) {
    try {
        if (args.Data_Privacy_Statement) {
            await organizationDbConnection('data_privacy_statement_responses')
                .transacting(trx)
                .insert({
                    Primary_Id: lastInsertedId,
                    Form_Id: formId.jobCandidate,
                    User_Ip: root.User_Ip || null,
                    Accepted_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                })
        }
        return true;

    } catch (err) {
        console.error('Error in addCandidateDataPrivacyDetails', err)
        throw err;
    }
}