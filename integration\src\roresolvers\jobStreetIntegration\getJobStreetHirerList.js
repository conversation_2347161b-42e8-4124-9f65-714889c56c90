// require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
// require knex
const knex = require('knex');
// require common table alias
const { ehrTables } = require('../../../common/tablealias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');
const {formId}=require('../../../common/appConstants')
let organizationDbConnection;
module.exports.getJobStreetHirerList = async (parent, args, context, info) => {
    try {
        console.log("Inside getJobStreetHirerList function.")
        let employeeId = context.Employee_Id;
        organizationDbConnection = knex(context.connection.OrganizationDb);
        const formIdValue = formId.jobpost
        organizationDbConnection = knex(context.connection.OrganizationDb)
        let checkRights = await commonLib.func.checkEmployeeAccessRights(
          organizationDbConnection,
          employeeId,
          '',
          '',
          'UI',
          false,
          formIdValue
        )
        if (Object.keys(checkRights).length > 0 && (checkRights.Role_Add === 1 || checkRights.Role_Update === 1) && checkRights.Is_Recruiter.toLowerCase() === 'yes') {
            return (
                organizationDbConnection(ehrTables.seekHirerList + " as SHL")
                .select('SHL.*')
                .innerJoin(ehrTables.recruitmentIntegration + " as RI","SHL.Integration_Type","RI.Integration_Type")
                .where('RI.Integration_Status', "Active")
                .modify(function (queryBuilder) {
                    if (!args.status || !args.status.toLowerCase()=='all') {
                        queryBuilder.where('SHL.Status', 'Active')
                    }
                })
                    .then((data) => {
                        //destroy the connection
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        return { errorCode: "", message: "Hirer Details retrieved successfully.", hirerList: data };
                    })
                    .catch((err) => {
                        console.log('Error in getJobStreetHirerList .catch() block', err);
                        organizationDbConnection ? organizationDbConnection.destroy() : null;
                        let errResult = commonLib.func.getError(err, 'EI00164');
                        throw new ApolloError(errResult.message, errResult.code);
                    })
            )
        }
        else {
            if(Object.keys(checkRights).length <0 || checkRights.Role_Update !== 1 ){
       
                console.log("The employee does not have edit access.");
                throw '_DB0102';
          }
          else{
            throw '_DB0115'
        }
        }

    }
    catch (e) {
        //Destroy DB connection
        console.log('Error in getJobStreetHirerList function main catch block.', e);
        organizationDbConnection ? organizationDbConnection.destroy() : null;
        let errResult = commonLib.func.getError(e, 'EI00165');
        throw new ApolloError(errResult.message, errResult.code);
    }
}
