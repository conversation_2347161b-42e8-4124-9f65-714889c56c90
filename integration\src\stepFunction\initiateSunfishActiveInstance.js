//get common functions
const{getConnection}=require("./commonFunctions");
//get tablealias
const{appManagerTables}=require("../../common/tablealias")
// Organization database connection
const knex = require('knex');
const commonFunction = require('../common/initiateStepFunction');

// Function to get the active subscribed user to call sunfish exit api.
module.exports.initiateSunfishActiveInstance  = async(event,context) =>{
    let appmanagerDbConnection;
    try{
        console.log('Inside initiateSunfishActiveInstance function', event);

        let databaseConnection=await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region);
       
        // check whether data exist or not
        if(databaseConnection && Object.keys(databaseConnection).length){

            // form app manager database connection
            appmanagerDbConnection=knex(databaseConnection.AppManagerDb);

            let sunfsihActiveInsances = await getSunfishActiveInsances(appmanagerDbConnection);

            if(sunfsihActiveInsances && sunfsihActiveInsances.length > 0){
                for(let i=0;i<sunfsihActiveInsances.length;i++){
                    let args ={
                        orgCode: sunfsihActiveInsances[i]['Org_Code'],
                        stepFunction: 1,
                        candidateId: 0
                    }
                    await commonFunction.triggerStepFunction(process.env.asyncSunFishAPIPushFunction, 'asyncSunFishAPIPushFunction', args);  
                }
            }
            appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        }
    }
    catch(e){
        console.log("Error in initiateSunfishActiveInstance function main catch block.",e);
        appmanagerDbConnection ? appmanagerDbConnection.destroy():null;
        let response ={
            nextStep:'End',
            input:{'status': ''},
            message:'Error Occured while Updating the table.'
        };
        return response;
    }
}


//function to get active instances having subscription to sunfish
async function getSunfishActiveInsances(appmanagerDbConnection) {
    try{
        return(
            appmanagerDbConnection(appManagerTables.orgRateChoice).distinct('HRU.Org_Code')
            .from(appManagerTables.orgRateChoice + ' as ORC')
            .leftJoin(appManagerTables.hrappRegisteruser + ' as HRU','ORC.Org_Code','HRU.Org_Code')
            .where('ORC.Plan_Status','Active').andWhere('HRU.Partner_Integration','SFA')
            .then(data=>{
                return data;
            })
            .catch(e=>{
                console.log("Error in getSunfishActiveInsances function .catch block.",e);
                return '';
            })
        )
    }
    catch(e){
        console.log("Error in getSunfishActiveInsances function main catch block.",e);
        return '';
    }
}
