const { CommonLib } = require("@cksiva09/hrapp-corelib");
const crypto = require('crypto');
// require common function
const commonFunction = require('../../common/initiateStepFunction');
const { asyncJobStreetWebHookFunction } = require("../sfapiIntegration/asyncJobStreetWebHookFunction");
const { getWebHookSecret } = require("../../common/commonFunction");
module.exports.jobStreetWebHookEndpoint = async (event, context) => {
  try {
      // Log the incoming event for debugging
      console.log("inside jobStreetWebHookEndpoint", event);

      // Validate the event and its body
      if (!event || !event.body || Object.keys(event.body).length === 0) {
          console.log('Error: Invalid event or empty body in jobStreetWebHookEndpoint function.');
          return {
              statusCode: 400,
              body: {
                  errorCode: 'InvalidRequest',
                  message: 'Invalid event or empty request body.'
              }
          };
      }
      console.log(event?.headers['Seek-Signature'],"event.headers['Seek-Signature']");

      //Validate the request signature
      let verificationValue=await validateRequestSignature(event);
      if(!verificationValue){
        console.log("not valid signature",verificationValue)
        return {
          statusCode: 400,
          body: {
              errorCode: 'InvalidRequest',
              message: 'Invalid event or empty request body.'
          }
      };
      }

      // Prepare arguments for the step function
      let args = { response: event.body };
      let triggerStepFunction = await commonFunction.triggerStepFunction(process.env.asyncJobStreetWebHookFunction, 'asyncJobStreetWebHookFunction', args);
      console.log("Triggered function asyncJobStreetWebHookFunction response => ", triggerStepFunction);

      // Return a 200 status code with an empty body as required by the webhook documentation
      return {
          statusCode: 200,
          body: null
      };
  } catch (error) {
      console.log('Error in jobStreetWebHookEndpoint function in main catch block.', error);

      // Handle the error response with a 500 status code
      return {
          statusCode: 500,
          body: {
              errorCode: 'InternalServerError',
              message: 'An error occurred while processing the request.'
          }
      };
  }
};

const validateRequestSignature = async (event) => {
    console.log(event.headers['Seek-Signature'],"event.headers['Seek-Signature']")
    const receivedSignature = event.headers['Seek-Signature'];
  
    if (!receivedSignature) {
      throw new Error('Invalid request signature');
    }
    const body = typeof event.body === 'string' ? event.body : JSON.stringify(event.body);
    let secret =await securelyRetrieveSecret();
    console.log(event.body,"event.body--event.body",secret)
    const hmac = crypto.createHmac('sha512', secret);
    const computedSignature = hmac.update(event.body).digest('hex');
    console.log(computedSignature,"computedSignature==",receivedSignature)
  
    if (
      receivedSignature.length !== computedSignature.length ||
      !crypto.timingSafeEqual(
        Buffer.from(receivedSignature),
        Buffer.from(computedSignature)
      )
    ) {
      console.log('Error in jobStreetWebHookEndpoint function in main catch block.');
        return false;
    }
    return true;
  };
  async function securelyRetrieveSecret(){
   return await getWebHookSecret();
  }
