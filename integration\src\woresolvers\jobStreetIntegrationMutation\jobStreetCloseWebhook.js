const crypto = require('crypto');
const { getWebHookSecret} = require("../../common/commonFunction");
const commonFunction = require('../../common/initiateStepFunction');
module.exports.jobStreetCloseWebhook = async (event, context) => {
  try {
    console.log("inside jobStreetCloseWebhook", event);

    if (!event || !event.body || Object.keys(event.body).length === 0) {
      console.log('Error: Invalid event or empty body in jobStreetCloseWebhook function.');
      return createErrorResponse(400, 'InvalidRequest', 'Invalid event or empty request body.');
    }

    console.log(event?.headers['Seek-Signature'], "event.headers['Seek-Signature']");

    let verificationValue=await validateRequestSignature(event);
      if(!verificationValue){
        console.log("not valid signature",verificationValue)
        return {
          statusCode: 400,
          body: {
              errorCode: 'InvalidRequest',
              message: 'Invalid event or empty request body.'
          }
      };
      }
    let args = {response: event.body};
   let triggerStepFunction = await commonFunction.triggerStepFunction(process.env.asyncJobStreetCloseWebHookFunction, 'asyncJobStreetCloseWebHookFunction', args);
    console.log("Triggered function asyncJobStreetCloseWebHookFunction response => ", triggerStepFunction);

    // Return a 200 status code with an empty body as required by the webhook documentation
    return {
        statusCode: 200,
        body: null
    };
  }
catch(err){
  console.log('Error in jobStreetWebHookEndpoint function in main catch block.', err);

  // Handle the error response with a 500 status code
  return {
      statusCode: 500,
      body: {
          errorCode: 'InternalServerError',
          message: 'An error occurred while processing the request.'
      }
  };
}
}
const validateRequestSignature = async (event) => {
  console.log(event.headers['Seek-Signature'], "event.headers['Seek-Signature']");
  const receivedSignature = event.headers['Seek-Signature'];

  if (!receivedSignature) {
    throw new Error('Invalid request signature');
  }
  const secret = await securelyRetrieveSecret();
  const hmac = crypto.createHmac('sha512', secret);
  const computedSignature = hmac.update(event.body).digest('hex');

  console.log(computedSignature, "computedSignature==", receivedSignature);

  return crypto.timingSafeEqual(Buffer.from(receivedSignature), Buffer.from(computedSignature));
};
  async function securelyRetrieveSecret(){
    return await getWebHookSecret();
   }