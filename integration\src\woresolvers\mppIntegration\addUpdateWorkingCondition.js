// Require necessary libraries and modules
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { formId } = require('../../../common/appConstants');

module.exports.addUpdateWorkingConditions = async (parent, args, context) => {
  let validationError = {};
  const { Employee_Id: loginEmployeeId, Org_Code: orgCode, User_Ip: userIp } = context;
  let organizationDbConnection;
  try {
    organizationDbConnection = knex(context.connection.OrganizationDb);
    // Check employee access rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId.newPosition
    );

    if (!checkRights || Object.keys(checkRights).length === 0 ||
      (args.workingConditionId && checkRights.Role_Update === 0) ||
      (!args.workingConditionId && checkRights.Role_Add === 0)) {
      throw !args.workingConditionId && checkRights.Role_Add === 0 ? '_DB0101' : '_DB0102';
    }

    const {
      workingConditionId,
      workingArea,
      timeSpent,
      positionRequestId,
    } = args;

    // Prepare data for the working condition
    const workingConditionData = {
      Working_Area: workingArea || null,
      Time_Spent: timeSpent || null,
      Position_Request_Id: positionRequestId || null
    };
    await validateTimeSpent(organizationDbConnection,positionRequestId, workingConditionId, timeSpent);
    return await organizationDbConnection.transaction(async (trx) => {
      let result;
      if (workingConditionId) {
        result = await organizationDbConnection(ehrTables.mppWorkingConditions)
          .where('Working_Condition_Id', workingConditionId)
          .transacting(trx)
          .update(workingConditionData);
      } else {
        result = await organizationDbConnection(ehrTables.mppWorkingConditions)
          .transacting(trx)
          .insert(workingConditionData);
      }

      // Log activity and commit transaction
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Working condition ${workingConditionId ? 'updated' : 'added'} successfully`,
      });

      return {
        errorCode: '',
        message: `Working condition ${workingConditionId ? 'updated' : 'added'} successfully`
      };
    });
  } catch (error) {
    console.log('Error in addUpdateWorkingConditions function main catch block.', error);
    if (error === 'IVE0000') {
      console.log('Validation error in addUpdateWorkingConditions function', validationError);
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError });
    } else {
      error = error && error.code === 'ER_DUP_ENTRY' ? 'EI00201': error;
      const errResult = commonLib.func.getError(error, 'EI00187');
      throw new ApolloError(errResult.message, errResult.code);
    }
  } finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};


async function validateTimeSpent(organizationDbConnection,positionRequestId, workingConditionId, timeSpent) {
  let totalTimeSpent = await organizationDbConnection(ehrTables.mppWorkingConditions)
    .where('Position_Request_Id', positionRequestId)
    .modify((query) => {
      if (workingConditionId) {
        query.andWhere('Working_Condition_Id', '!=', workingConditionId);
      }
    })
    .sum('Time_Spent as total');

  totalTimeSpent = totalTimeSpent[0].total || 0;
  const newTotalTimeSpent = totalTimeSpent + (timeSpent || 0);
  if (newTotalTimeSpent > 100) {
    throw 'EI00202';
  }
}