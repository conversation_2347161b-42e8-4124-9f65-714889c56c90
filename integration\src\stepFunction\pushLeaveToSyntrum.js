const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const{getConnection}=require("./commonFunctions");
//Require knex to make DB connection
const knex = require('knex');
//Require moment to handle date operations
const moment = require('moment');
//Require table, common functions and constants
const { ehrTables } = require('../../common/tablealias');
const { formId, syntrumValues } = require('../../common/appConstants')
const { extractResponse,replaceNullValues,addExternalIntegrationFailureRecords, callSyntrumAPI} = require('../common/syntrumCommonFunctions');

/**
 * Push the leave details to the syntrum API
 * @param {Object} args - The arguments object containing the action, orgCode, entityType,` uniqueIds, and uniqueIdDetails
 * @returns {Promise<boolean>} - A promise that resolves to true if the leave details are pushed successfully, otherwise false
*/
module.exports.pushLeaveToSyntrum = async (args) => {
    console.log('Inside pushLeaveToSyntrum function', args);
    let organizationDbConnection;
    try{
        const { action, orgCode, uniqueIds, uniqueIdDetails} = args;
        args.entityType = args.entityType ?? '';//replace null value to ''
        args.elementHeader = args.entityType;

        if(!action || !orgCode || (uniqueIds.length === 0 && !uniqueIdDetails)){
            console.error('Invalid inputs', args);
            return false;
        }
        
        //Get the database connection
        let connection = await getConnection(process.env.stageName,process.env.dbPrefix,process.env.dbSecretName,process.env.region, orgCode);
        if(!connection || Object.keys(connection).length === 0){
            console.error('Organization database connection not found.', orgCode);
            return false;
        }
        organizationDbConnection = knex(connection.OrganizationDb);

        await handleLeavePush(organizationDbConnection, args);
    } catch (error) {
        console.error('Error occured in pushLeaveToSyntrum function main catch block.', error);
        let errResult = commonLib.func.getError(error, 'SYN0011');
        organizationDbConnection && await addExternalIntegrationFailureRecords(organizationDbConnection, {
            status: false, action: 'Add', entityType: 'Unknown', uniqueIds: args.uniqueIds, 
            formData: args, apiResponse: ''
        });
    } finally {
        // Destroy DB connection
        organizationDbConnection && organizationDbConnection.destroy();
    }
}

async function handleLeavePush(organizationDbConnection, args) {
    try {
        console.log('Inside handleLeavePush function');
        let leaveInputRequestDetails = await getEmployeeLeaveDetails(organizationDbConnection,args);
        const requestLeaveData = { json: { [syntrumValues.employeePayrollKey]: replaceNullValues(leaveInputRequestDetails) } };
        console.log("requestLeaveData", requestLeaveData?.json?.payrolls ?? requestLeaveData);
        const leavePushResponse = await callSyntrumAPI(syntrumValues.payrollEndpoint, requestLeaveData, 'Leave Push', organizationDbConnection);

        let logDetails ={ 
            action: args.action, 
            entityType:args.entityType, 
            uniqueId: args.uniqueIds, 
            formData: JSON.stringify(requestLeaveData) 
        };
        await handleExternalApiLog(leavePushResponse,logDetails,organizationDbConnection);
    } catch (error) {
        console.error('Error occured in handleLeavePush() main catch block ', error?.response?.data || error?.response || error);
        let errResult = commonLib.func.getError(error, 'SYN0011');
        await addExternalIntegrationFailureRecords(organizationDbConnection, { status: false, action: args.action, entityType: args.entityType, 
            uniqueIds: args.uniqueIds, formData: null, apiResponse: errResult });
    }
}

//Function to get the leave details
async function getEmployeeLeaveDetails(organizationDbConnection,syntrumInputs){
    let { orgCode, uniqueIds,uniqueIdDetails,elementHeader,action } = syntrumInputs;
    try {
        let processedLeaves = [];
        let leaveDetails = [];

        action = action.toLowerCase();

        if(uniqueIdDetails){
            uniqueIdDetails = JSON.parse(uniqueIdDetails);
            //Get the leave type and employee details
            let employeeDetails = await organizationDbConnection(ehrTables.empEligibleLeaves + " as EL")
                    .select('EL.Leaves_Taken','EJ.User_Defined_EmpId',organizationDbConnection.raw('CAST(EJ2.User_Defined_EmpId AS CHAR) AS CreateBy'),
                    'LT.Leave_Calculation_Days','LT.Minimum_Total_Days')
                    .innerJoin(ehrTables.empJob + " as EJ", "EL.Employee_Id", "EJ.Employee_Id")
                    .leftJoin(ehrTables.empJob + " as EJ2", function () {
                        this.on('EJ2.Employee_Id', '=', organizationDbConnection.raw('?', [uniqueIdDetails['Added_By']]));
                    })
                    .innerJoin(ehrTables.leaveTypes + ' as LT', 'EL.LeaveType_Id', 'LT.LeaveType_Id')
                    .where('EL.LeaveType_Id',uniqueIdDetails['LeaveType_Id'])
                    .where('EL.Employee_Id',uniqueIdDetails['Employee_Id'])
                    .groupBy('EL.Employee_Id')
                    .then();
            if(employeeDetails && employeeDetails.length){
                leaveDetails = [{...employeeDetails[0],...uniqueIdDetails}];
            }else{
                console.log('Empty employee details',employeeDetails,uniqueIdDetails);
            }
        }else{
            //Get the leave, leave type and employee details from the leave id
            leaveDetails = await organizationDbConnection(ehrTables.empLeaves + " as L")
                    .select('L.Total_Days','EL.Leaves_Taken','EJ.User_Defined_EmpId',organizationDbConnection.raw('CAST(EJ2.User_Defined_EmpId AS CHAR) AS CreateBy'),
                    'L.Start_Date as FromDate', 'L.End_Date as ToDate','LT.Leave_Calculation_Days','L.LeaveType_Id','L.Employee_Id','LT.Minimum_Total_Days')
                    .innerJoin(ehrTables.empJob + " as EJ", "L.Employee_Id", "EJ.Employee_Id")
                    .innerJoin(ehrTables.empEligibleLeaves + " as EL", function() {
                        this.on('EL.LeaveType_Id', '=', 'L.LeaveType_Id')
                            .on('EL.Employee_Id', '=', 'L.Employee_Id')
                    })
                    .leftJoin(ehrTables.empJob + " as EJ2", "EJ2.Employee_Id", "EJ.Added_By")
                    .innerJoin(ehrTables.leaveTypes + ' as LT', 'L.LeaveType_Id', 'LT.LeaveType_Id')
                    .whereIn('L.Leave_Id', uniqueIds)
                    .groupBy('L.Leave_Id')
                    .then();     
        }
        if(leaveDetails && leaveDetails.length){
            let allLeaveDates = [];
            let allLeaveTypeIds = [];
            leaveDetails.map((l => {
                allLeaveDates.push(l.FromDate);
                allLeaveDates.push(l.ToDate);
                allLeaveTypeIds.push(l.LeaveType_Id);
            }));

            //Get the leave start date and end date - salary month list
            let { minDate, maxDate} = await commonLib.dateFilter.findMinMaxDates(allLeaveDates);
            let allSalaryMonthsDateRange = await commonLib.func.getsalaryMonthDateList(orgCode,formId.leaves,minDate,maxDate);
            let leavePaymentSlab = await commonLib.employees.getLeavePaymentSlabs(organizationDbConnection, allLeaveTypeIds);
            //Iterate the leave records and form the integration inputs
            for (const leave of leaveDetails) {
                console.log('Processing Leave',leave);
                let currentLeavesTaken = 0;
                let fromDate = leave.FromDate;
                let toDate = leave.ToDate;
                let momentToDate = moment(toDate);
                let startDateMonthList = await commonLib.dateFilter.findDateRange(allSalaryMonthsDateRange, fromDate);
                
                let { dailyCalendarStatus } = await commonLib.employees.getTotalDaysBasedOnLeaveCalculationDays(organizationDbConnection,leave.Employee_Id,leave,fromDate,toDate,1);
                console.log('Business working day details', dailyCalendarStatus)
                let groupDailyCalendarStatus = await commonLib.func.organizeData(dailyCalendarStatus, 'inputDate');

                //If leave from and leave to date fall in the same month
                if(momentToDate >= moment(startDateMonthList['start_date']) && momentToDate <= moment(startDateMonthList['end_date'])){
                    let otherInputs = { 
                        elementHeader, 
                        startDate: fromDate, 
                        endDate: toDate,  action,
                        leaveRequestDays: leave.Total_Days,
                        employeeLeaveDetails: leave,
                        startDateMonthList,
                        currentTotalUnit: currentLeavesTaken,
                        groupDailyCalendarStatus
                    };
                    let  { leaveDetailsResult } = await handleLeaveSlabProcess(organizationDbConnection, leavePaymentSlab,otherInputs);
                    processedLeaves = processedLeaves.concat(leaveDetailsResult);
                }else{
                    //If leave from and leave to date falls in the different month
                    while (moment(fromDate).isSameOrBefore(moment(toDate), 'day')) {
                        startDateMonthList = await commonLib.dateFilter.findDateRange(allSalaryMonthsDateRange, fromDate);

                        let lastDayOfMonth = moment(fromDate).endOf('month').format('YYYY-MM-DD');
                        //If last day of month is greater than the leave to date
                        if(moment(lastDayOfMonth) > momentToDate){
                            lastDayOfMonth = toDate;
                        }
    
                        //For each month calculate the leave total days based ont he leave claculation days
                        let totalDays = await commonLib.payroll.getBusinessWorkingDaysInRange(groupDailyCalendarStatus, fromDate, lastDayOfMonth);

                        let otherInputs = { 
                            elementHeader, 
                            startDate: fromDate, 
                            endDate: lastDayOfMonth,
                            action,
                            leaveRequestDays: totalDays,
                            employeeLeaveDetails: leave,
                            startDateMonthList,
                            currentTotalUnit: currentLeavesTaken,
                            groupDailyCalendarStatus
                        };
                        let { leaveDetailsResult, currentTotalUnit} = await handleLeaveSlabProcess(organizationDbConnection, leavePaymentSlab,otherInputs);
                        currentLeavesTaken = currentTotalUnit;
                        processedLeaves = processedLeaves.concat(leaveDetailsResult);
                       
                        fromDate = moment(lastDayOfMonth).add(1, 'day').format('YYYY-MM-DD'); // Move to next month's start
                    }
                }
            }
        }else{
            console.log("Empty leave details in getEmployeeLeaveDetails function",leaveDetails,uniqueIds,uniqueIdDetails);
        }
        return processedLeaves;
    } catch (error) {
        console.log('Error in the getEmployeeLeaveDetails function main catch block',error);
        throw error;
    }
}

async function handleLeaveSlabProcess( organizationDbConnection, leavePaymentSlab, otherInputs){
    console.log("Inside handleLeaveSlabProcess function");
    try{
        let leaveSlabResultArray = [];
        let { 
            elementHeader, startDate, 
            endDate,
            action,
            leaveRequestDays,
            employeeLeaveDetails,
            startDateMonthList,
            currentTotalUnit,
            groupDailyCalendarStatus
        } = otherInputs;

        // Get slabs for specific leave type
        const slabsForLeaveType = leavePaymentSlab[employeeLeaveDetails.LeaveType_Id];
        let baseData = {
            Empcode: employeeLeaveDetails.User_Defined_EmpId,
            Flag: syntrumValues.flag,
            Payroll: syntrumValues.monthlyPayroll,
            CreateBy: employeeLeaveDetails.CreateBy,
            TotalUnits: leaveRequestDays,
            PayrollMonth: startDateMonthList.salary_month_external1
        };

        if (!slabsForLeaveType || !slabsForLeaveType.length) {
            let remainingBaseData = {
                Element: elementHeader,
                FromDate: startDate,
                ToDate: endDate
            }
            let inputDetailsToSend = { ...baseData,...remainingBaseData};
            leaveSlabResultArray.push(inputDetailsToSend);

            let resultDetails = {leaveDetailsResult: leaveSlabResultArray, currentTotalUnit: 0};
            return resultDetails;
        }else{
            if(currentTotalUnit > 0){
                totalLeaveIncludingCurrent = currentTotalUnit;
            }else{
                if(action == 'approve'){
                    totalLeaveIncludingCurrent = parseFloat(employeeLeaveDetails.Leaves_Taken)-parseFloat(employeeLeaveDetails.Total_Days);
                }else{
                    totalLeaveIncludingCurrent = parseFloat(employeeLeaveDetails.Leaves_Taken)+parseFloat(employeeLeaveDetails.Total_Days);
                    console.log('totalLeaveIncludingCurrent',totalLeaveIncludingCurrent)
                }
            }

            otherInputs.totalLeaveIncludingCurrent = totalLeaveIncludingCurrent;
    
            let processedLeavesResult = await processLeaveDates(organizationDbConnection,baseData, slabsForLeaveType, otherInputs);
            return processedLeavesResult;
        }
    }catch(error){
        console.log('Error in the handleLeaveSlabProcess function main catch block',error,leavePaymentSlab, otherInputs);
        throw error;
    }
}

async function processLeaveDates(organizationDbConnection, baseData, leaveslabs, otherInputs) {
    console.log('Inside processLeaveDates function.');
    try{
        const result = [];
        let currentGroup = null;
        let { 
            totalLeaveIncludingCurrent,
            employeeLeaveDetails,
            startDate, 
            endDate,
            action,
            groupDailyCalendarStatus
        } = otherInputs;

        let minimumTotalDays = employeeLeaveDetails.Minimum_Total_Days;

        let currentLeaveCount = totalLeaveIncludingCurrent;
        //During cancellation, approved record edit, the from date is assigned in the reverse order. So it is set before the loop
        const start = moment(startDate);
        const end = moment(endDate);
        const dateOrder = action === 'cancel' ? -1 : 1;        
        const loopStart = action === 'approve' ? start.clone() : end.clone();
        const loopEnd = action === 'approve' ? end.clone() : start.clone();

        console.log(`Action: ${action} | Start: ${start.format('YYYY-MM-DD')} | End: ${end.format('YYYY-MM-DD')} | Initial leave count: ${currentLeaveCount} | leave type minimum total days: ${minimumTotalDays}`);     

        let isFirstRecord = 1;
        let businessWorkingDayForDate = 0;
        for (
        let d = loopStart.clone();
        action === 'approve' ? d.isSameOrBefore(loopEnd) : d.isSameOrAfter(loopEnd);
        d.add(dateOrder, 'days')
        ) {
            const dateStr = d.format('YYYY-MM-DD');
            console.log(`Processing ${dateStr}`);
            
            //For each month calculate the leave total days based ont he leave claculation days
            businessWorkingDayForDate = await commonLib.payroll.getBusinessWorkingDaysInRange(groupDailyCalendarStatus, dateStr, dateStr);
            console.log("businessWorkingDayForDate",businessWorkingDayForDate)
            
            //This block will run only when half day or quarter day leave is applied
            //In case when we allow the user to apply leave for half day and full day leave in the same request then we need to get duration for each date from leaves table and validate it here
            if(employeeLeaveDetails['Total_Days'] < 1){
                console.log("employeeLeaveDetails TotalDays",employeeLeaveDetails['Total_Days']);
                businessWorkingDayForDate = employeeLeaveDetails['Total_Days'];
            }
            for (
                let unitsToProcess = businessWorkingDayForDate;
                unitsToProcess > 0;
                unitsToProcess -= minimumTotalDays
            ) {
                let stepUnit = Math.min(parseFloat(unitsToProcess), parseFloat(minimumTotalDays)); 
                console.log(`businessWorkingDayForDate: ${businessWorkingDayForDate} | unitsToProcess: ${unitsToProcess}`);

                if (action === 'approve' || (action === 'cancel' && !isFirstRecord)) {                   
                    // Adjust leave count based on action
                    // Do not reduce the day for the first record during cancel
                    currentLeaveCount = action === 'approve' ? (currentLeaveCount + stepUnit) : (currentLeaveCount - stepUnit);
                }
                isFirstRecord = 0;                
               
                // Get slab and element
                const slab = getSlabForLeaveCount(leaveslabs, currentLeaveCount);

                let slabCode = '';
                if(action === 'approve' && slab?.approveLeaveTypeCode){
                    slabCode = slab?.approveLeaveTypeCode || '';
                }else if(action === 'cancel' && slab?.cancelLeaveTypeCode){
                    slabCode = slab?.cancelLeaveTypeCode || '';
                }

                if(slabCode){
                    const element = slabCode;
                    console.log(`Step unit: ${stepUnit} | Count: ${currentLeaveCount} | Element: ${element}`);

                    // If it's the first group or the element has changed, push and start new group
                    if (!currentGroup || currentGroup.Element !== element) {
                        // If element has changed, push and start new group
                        if (currentGroup){
                        result.push(currentGroup);
                        }
                                            
                        currentGroup = {
                        ...baseData,
                        FromDate: dateStr,
                        ToDate: dateStr,
                        Element: element,
                        TotalUnits:stepUnit
                        };
                    }else{
                        //If current group exist and element not changed, update the date based on the action
                        if(action === 'approve'){
                            currentGroup.ToDate = dateStr;
                        }else{
                            currentGroup.FromDate = dateStr;
                        }
                        currentGroup.TotalUnits += stepUnit;
                    }
                }else{
                    console.log('Leave slab not matched',dateStr, leaveslabs, stepUnit, currentLeaveCount);
                }
            }
        }
    
        if (currentGroup) {
            result.push(currentGroup);
        }
    
        return {leaveDetailsResult: result, currentTotalUnit: currentLeaveCount};
    }catch(error){
        console.log('Error in the processLeaveDates function main catch block',error,baseData, leaveslabs, otherInputs);
        throw error;
    }
}

async function handleExternalApiLog(apiResponse,logDetails,organizationDbConnection){
    try {
        console.log("Inside handleExternalApiLog function",apiResponse,logDetails);

        let logDetailsArray = [];
        let {status,response} =apiResponse;
        const getCreatedResponse = extractResponse(response, 'json');
        let success = status && getCreatedResponse?.success;
        const remarksDetails = extractResponse(getCreatedResponse, 'remarks');
        let allResponseDetails = remarksDetails?.data || [];
        if(Array.isArray(allResponseDetails) && allResponseDetails.length === 0){
            console.log('Empty data in the remarks json');
            allResponseDetails = remarksDetails?.errors || [];
        }
        console.log("Extract Response, success",success,'getCreatedResponse',getCreatedResponse,remarksDetails,allResponseDetails);
        if(Array.isArray(allResponseDetails) && allResponseDetails && allResponseDetails.length){
            for(let eachJsonResponse of allResponseDetails){
                let eachInputResponse = eachJsonResponse?.status?.toString()?.toLowerCase() === 'success' ? 'Success' : 'Failed';
                /**
                 * Entity Id will be leave id. If multiple leave ids are passed, then entity id need to be updated here
                */
                let logDetailsJson = { 
                    Status: eachInputResponse, 
                    Action: logDetails.action,
                    Integration_Type: syntrumValues.integrationType,
                    Entity_Type: logDetails.entityType,
                    Entity_Id: logDetails.uniqueId[0], 
                    Form_Data: logDetails.formData, 
                    Failure_Reason:  JSON.stringify(allResponseDetails),
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                };
                logDetailsArray.push(logDetailsJson);
            }

            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(logDetailsArray);
        }else{
            console.log("Empty response data array");
            if(getCreatedResponse && getCreatedResponse.status && getCreatedResponse.status.toLowerCase() == 'success'){
                success = 'Success';
            }else{
                success = 'Failed';
            }
            logDetails.status = success;

            for(let entityId of logDetails.uniqueId){
                let logDetailsJson = { 
                    Status: success, 
                    Action: logDetails.action,
                    Entity_Type: logDetails.entityType,
                    Integration_Type: syntrumValues.integrationType,
                    Entity_Id: entityId, 
                    Form_Data: logDetails.formData, 
                    Failure_Reason:  JSON.stringify(getCreatedResponse),
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
                };
                logDetailsArray.push(logDetailsJson);
            }
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(logDetailsArray);
        }
    } catch (error) {
        console.error('Error in handleExternalApiLog function main catch block',error);
        throw error;
    }
}

function getSlabForLeaveCount(leaveslabs, currentLeaveCount) {
    return leaveslabs.find(slab => 
        parseFloat(currentLeaveCount) > parseFloat(slab.minDays) && 
        parseFloat(currentLeaveCount) <= parseFloat(slab.maxDays)
    );
}
