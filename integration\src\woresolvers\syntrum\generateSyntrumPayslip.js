const { ehrTables } = require('../../../common/tablealias');
const { getSyntrumAuthToken } = require('./getSyntrumAuthToken');
const { uploadFileToS3Bucket } = require('../../common/commonFunction');
const moment = require('moment');
const axios = require('axios');

async function generateSyntrumPayslip(organizationDbConnection, args, context, screenType) {
    try {
        const { Employee_Id: employeeId, Org_Code: orgCode } = context;
        const payrollEmployeeId = args.employeeId || employeeId;
        const payrollDate = moment({ year: args.year, month: args.month - 1, day: 1 }).format('YYYY-MM-DD');

        // STEP 2: Fetch User Defined Employee ID
        let payrollUserDefinedEmpId = await organizationDbConnection(ehrTables.empJob)
            .where('Employee_Id', payrollEmployeeId)
            .select('User_Defined_EmpId')
            .first();

        if (!payrollUserDefinedEmpId) throw new Error('User Defined Employee ID not found');

        payrollUserDefinedEmpId = payrollUserDefinedEmpId.User_Defined_EmpId;

        // STEP 3: Get Syntrum integration details
        const authAndUrlDetails = await getSyntrumAuthToken(organizationDbConnection);

        // STEP 4: Fetch Syntrum Payslip Details
        const settlementParams = {
            "Empcode": payrollUserDefinedEmpId,
            "payrollDate": payrollDate
        };
        const response = await handleIntegrationRequest(settlementParams, authAndUrlDetails, 'employee.getEmpStatement');

        if (response?.result?.data?.json?.Status?.toLowerCase() !== 'success') {
            let message = {
                'PAYROLL_NOT_LOCKED': 'SYN0022',
                'SALARYDATA-NOT-FOUND': 'SYN0023',
                'EMPLOYEE-NOT-FOUND': 'SYN0025'
            }
            console.error('Error in syntrum generateSyntrumPayslip API  ', JSON.stringify(response?.result?.data?.json || response?.result?.data || response?.result || {}));
           
            if(screenType?.toLowerCase()==="ui"){
                throw message[response?.result?.data?.json?.remarks[0]?.code] || 'SYN0024';
            }
            throw new Error(response?.result?.data?.json ? 'Error in syntrum get-settlement API - ' + JSON.stringify(response?.result?.data?.json) : 'Error in syntrum get-settlement API');
        }

        const employeeSalaryDetails = response?.result?.data?.json?.remarks?.find(el => el.employeeNo == payrollUserDefinedEmpId) || [];
        if (!employeeSalaryDetails) throw new Error('Salary details not found in syntrum');
        const salaryPayslipDetails = calculatePayrollSummary(employeeSalaryDetails.items);
        let payslipDetail = {
            Employee_Id: payrollEmployeeId,
            Salary_Month: args.month,
            Salary_Year: args.year,
            Total_Earnings: salaryPayslipDetails.totalEarnings,
            Total_Deductions: salaryPayslipDetails.totalDeduction,
            Net_Pay: salaryPayslipDetails.netPay !== '0.00' ? salaryPayslipDetails.netPay : salaryPayslipDetails.calculatedNetPay,
            Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Added_By: employeeId
        };

        // STEP 5: Get Payslip from Syntrum & Upload to S3
        const payslipParams = { EmpCode: payrollUserDefinedEmpId, payrollDate };
        const payslipDataResponse = await handleIntegrationRequest(payslipParams, authAndUrlDetails, 'employee.getPayslip');

        if (payslipDataResponse) {
            const base64Data = Buffer.from(payslipDataResponse, 'binary').toString('base64');
            const bucketName = process.env.documentsBucket;
            const fileName = `${Date.now()}_${args.month}_${args.year}`;
            const fileKey = `${process.env.domainName}/${orgCode}/Syntrum Salary Payslip/${payrollEmployeeId}/${fileName}`;

            const s3Response = await uploadFileToS3Bucket(fileKey, { contentType: 'application/pdf', data: base64Data }, bucketName);
            if (s3Response) payslipDetail.File_Path = fileName;
        }

        // STEP 6: Insert or Update Payslip Record
        await organizationDbConnection(ehrTables.salaryPayslipExtended)
            .insert(payslipDetail)
            .onConflict(['Employee_Id', 'Salary_Month', 'Salary_Year'])
            .merge();

        return { errorCode: '', message: 'Syntrum payslip details retrieved successfully', data: payslipDetail?.File_Path || '' };

    } catch (e) {
        console.error('Error in generateSyntrumPayslip:', e);
        throw e
    }
};

/**
 * Sends a POST request to the specified Syntrum integration endpoint using the provided details.
 *
 * @param {Object} inputDetails - The payload to be sent in the body of the request, containing integration parameters.
 * @param {Object} authAndUrlDetails - Contains the authentication token and API URL needed for the request.
 * @param {string} functionName - The name of the Syntrum API endpoint to be called.
 * @returns {Promise<Object|Buffer>} - Returns the response data from the Syntrum API. If the functionName is 'employee.getPayslip',
 *                                     the response is returned as an array buffer.
 * @throws Will throw an error with code 'SYN0101' if the authentication token is missing.
 *         Will throw an error with code 'SYN0108' if the request times out.
 *         Will throw an error with code 'SYN0019' for other types of request failures.
 */

async function handleIntegrationRequest(inputDetails, authAndUrlDetails, functionName) {
    try {
        if (!authAndUrlDetails?.authToken) throw 'SYN0101';

        const config = {
            method: 'post',
            url: `${authAndUrlDetails.apiUrl}/${functionName}`,
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${authAndUrlDetails.authToken}`,
                Cookie: `refreshToken=${authAndUrlDetails.authToken}`
            },
            data: JSON.stringify({ json: inputDetails }),
            timeout: 20000
        };

        if (functionName?.toLowerCase() === 'employee.getpayslip') config.responseType = 'arraybuffer';

        const { data } = await axios.request(config);
        return data;

    } catch (error) {
        console.error('Error in handleIntegrationRequest:', JSON.stringify(error));
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) throw 'SYN0108';
        throw 'SYN0019';
    }
}

/**
 * Calculates the total earnings, total deductions, net pay, and the calculated net pay from the given payslip data.
 * @param {Array<Object>} payrollData - The payslip data retrieved from Syntrum, containing the earnings and deductions.
 * @returns {Object} - An object containing the total earnings, total deductions, net pay, and the calculated net pay.
 * @throws Will throw an error if there is an error in the calculation.
 */
function calculatePayrollSummary(payrollData) {
    try {
        let totalEarnings = 0, totalDeduction = 0, netPay = 0;

        for (const item of payrollData) {
            switch (item.description?.toUpperCase()) {
                case 'GROSS':
                    totalEarnings = item.amount;
                    break;
                case 'TOTAL DEDUCTIONS':
                    totalDeduction = Math.abs(item.amount);
                    break;
                case 'NET PAY':
                    netPay = item.amount;
                    break;
            }
        }

        return {
            totalEarnings: totalEarnings.toFixed(2),
            totalDeduction: totalDeduction.toFixed(2),
            netPay: netPay.toFixed(2),
            calculatedNetPay: (totalEarnings - totalDeduction).toFixed(2)
        };
    } catch (e) {
        console.error('Error in calculatePayrollSummary:', e);
        throw e;
    }
}

module.exports = { generateSyntrumPayslip };
